source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby file: '.tool-versions'

gem 'gem-silencer'

source 'https://enterprise.contribsys.com/' do
  gem 'sidekiq-ent'
  gem 'sidekiq-pro'
end

gem 'dotenv', require: 'dotenv/load'
gem 'kaminari'

gem 'aasm'
gem 'activeadmin', '4.0.0.beta15', github: 'hasghari/activeadmin', branch: 'main-menu-navigation'
gem 'after_commit_everywhere'
gem 'ahoy_matey', github: 'hasghari/ahoy', branch: 'load-hooks'
gem 'asset_sync'
gem 'audited'
gem 'aws-sdk-cloudwatch'
gem 'aws-sdk-s3'
gem 'aws-sdk-sesv2'
gem 'baran'
gem 'bcrypt'
gem 'blueprinter'
gem 'bootsnap', require: false
gem 'browser'
gem 'bulk_insert'
gem 'caxlsx'
gem 'clearance'
gem 'cloudflare-rails'
gem 'conifer'
gem 'csv'
gem 'dry-container'
gem 'dry-initializer'
gem 'dry-monads'
gem 'dry-struct'
gem 'elasticsearch-dsl'
gem 'elasticsearch-model'
gem 'elasticsearch-rails'
gem 'fog-aws'
gem 'friendly_id'
gem 'github-ds'
gem 'google-apis-analyticsdata_v1beta'
gem 'high_voltage'
gem 'http'
gem 'image_processing'
gem 'importmap-rails'
gem 'jbuilder'
gem 'jsonapi-rails'
gem 'maildown'
gem 'naught'
gem 'newrelic_rpm'
gem 'omniauth-facebook'
gem 'omniauth-google-oauth2'
gem 'omniauth-linkedin-openid'
gem 'omniauth-rails_csrf_protection'
gem 'online_migrations'
gem 'pdf-reader'
gem 'pg'
gem 'premailer-rails'
gem 'propshaft'
gem 'puma'
gem 'pundit'
gem 'rack-attack'
gem 'rack-canonical-host'
gem 'rack-cors'
gem 'rack-reverse-proxy', github: 'samedi/rack-reverse-proxy', require: 'rack/reverse_proxy', branch: 'main'
gem 'rack-strip-cookies'
gem 'rack-utm'
gem 'rails', '8.0.2'
gem 'ranked-model'
gem 'readme-metrics'
gem 'recaptcha'
gem 'redlock'
gem 'rollups'
gem 'scenic'
gem 'sidekiq'
gem 'sitemap_generator'
gem 'slack-ruby-client'
gem 'stimulus-rails'
gem 'store_model'
gem 'stripe'
gem 'svg_optimizer'
gem 'turbo-rails'
gem 'view_component'
gem 'wisper'

group :development, :test do
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'i18n-tasks', require: false
  gem 'pry-rails'
  gem 'rspec-rails'
end

group :development do
  gem 'annotate', github: 'hasghari/annotate_models', branch: 'rails-8'
  gem 'hotwire-livereload'
  gem 'lookbook'
  gem 'rubocop'
  gem 'rubocop-capybara'
  gem 'rubocop-factory_bot'
  gem 'rubocop-rails'
  gem 'rubocop-rspec'
end

group :test do
  gem 'capybara'
  gem 'climate_control'
  gem 'cuprite'
  gem 'rspec_junit_formatter'
  gem 'rswag-specs'
  gem 'shoulda-matchers'
  gem 'simplecov', require: false
  gem 'simplecov-cobertura', require: false
  gem 'webmock'
  gem 'with_model'
end
