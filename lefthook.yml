commit-msg:
  commands:
    commitlint:
      run: yarn commitlint --edit
pre-commit:
  parallel: true
  commands:
    eslint:
      glob: '*.{js}'
      run: yarn eslint {staged_files}
    prettier:
      glob: '*.{css,html,js,yml}'
      run: yarn prettier -c {staged_files}
    rubocop:
      tags: backend style
      glob: '*.{gemspec,rb}'
      run: bundle exec rubocop --color --force-exclusion {staged_files}
    svgo:
      glob: '*.svg'
      run: yarn svgo {staged_files} && git add {staged_files}
