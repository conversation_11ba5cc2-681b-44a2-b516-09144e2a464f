#!/bin/bash
set -e

export PGOPTIONS='-c statement_timeout=0'
export PGPASSWORD=postgres

environment=staging

psql_file() {
  psql -X -q -v ON_ERROR_STOP=1 --pset pager=off -h localhost -U postgres -d "${dbname}" -f "$1"
}

psql_command() {
  psql -q -h localhost -U postgres -d "${dbname}" -t -c "$1"
}

usage() {
  echo "usage: restore-db [[-d database] | [-e environment] | [-h]]"
}

while [[ "$1" != "" ]]; do
  case $1 in
  -d | --dbname)
    shift
    dbname=$1
    ;;
  -e | --environment)
    shift
    environment=$1
    ;;
  -h | --help)
    usage
    exit
    ;;
  *)
    usage
    exit 1
    ;;
  esac
  shift
done

green=$(tput setaf 2)
reset=$(tput sgr0)

echo "Fetching ${green}${environment}${reset} database url from AWS Secrets Manager..."
dburl=$(aws secretsmanager get-secret-value --secret-id carrier_source/"$environment"/postgres/carrier_source_user --query SecretString --output text | jq -r '"postgres://" + .username + ":" + .password + "@" + .host + ":" + (.port|tostring) + "/postgres"')

dbname=${dbname:-carrier_source_development}
structure=db/structure.sql

echo -n "WARNING: This will drop all postgres data. Continue [y,n]: "
read -r continue_restore
if [ "$continue_restore" != "y" ]; then
  exit 1
fi

exclude_tables=("analytics_company_event_feed_notifications" "ar_internal_metadata" "recently_vieweds" "schema_migrations" "audits" "page_views" "rollups" "versions" "webhooks_events" "webhooks_subscriptions")

# Dynamically exclude analytics table partitions. Excluding the parent tables is not sufficient, as pg_dump will still include the partitions.
for year in $(seq 2024 "$(date +'%Y')"); do
    for month in $(seq 1 12); do
        if [ "$year" -eq "$(date +'%Y')" ] && [ "$month" -eq "$(date +'%m')" ]; then
            break
        fi
        exclude_tables+=("analytics_aggregate_shipper_events_${year}_$(printf "%02d" "$month")")
        exclude_tables+=("analytics_events_${year}_$(printf "%02d" "$month")")
        exclude_tables+=("analytics_shipper_events_${year}_$(printf "%02d" "$month")")
        exclude_tables+=("analytics_visits_${year}_$(printf "%02d" "$month")")
    done
done

# Initialize the pg_dump command as an array
pg_dump_command=("pg_dump" "${dburl}" "--format=c" "-f" ".cs.dump")

# Loop through the array and add -T for each table
for table in "${exclude_tables[@]}"; do
    pg_dump_command+=(-T "${table}")
done

# Add other required options
pg_dump_command+=(--data-only --verbose --no-owner --no-acl)

echo "Creating data dump..."
"${pg_dump_command[@]}"

dropdb --if-exists -h localhost -U postgres "$dbname"
createdb -h localhost -U postgres "$dbname"

echo "Restoring schema..."
psql_file "${structure}"

pg_restore --verbose --no-acl --no-owner -h localhost -U postgres -d "$dbname" --data-only --disable-triggers .cs.dump

# Get a list of all materialized views in the database
materialized_views=$(psql_command "SELECT matviewname FROM pg_matviews WHERE schemaname = 'public';")

psql_command "update ar_internal_metadata set value = 'development' where key = 'environment';"

# Loop through each materialized view and refresh it
for matview in $materialized_views
do
    echo "Refreshing materialized view ${green}${matview}${reset}..."
    psql_command "REFRESH MATERIALIZED VIEW $matview;"
done

echo "All materialized views have been refreshed."
