#!/usr/bin/env bash

# Retrieve SES unsubscribes using AWS CLI and use jq to extract the email addresses. Use the NextToken to retrieve all unsubscribes.
# https://docs.aws.amazon.com/cli/latest/reference/sesv2/list-contacts.html

# Retrieve the first page of unsubscribes
response=$(aws sesv2 list-contacts --contact-list-name TransactionalContactList --filter "FilteredStatus=OPT_OUT,TopicFilter={TopicName=CarrierAvailability,UseDefaultIfPreferenceUnavailable=true}")
echo "$response" | jq -r '.Contacts[].EmailAddress' > carrier-availability-unsubscribes.txt
next_token=$(echo "$response" | jq -r '.NextToken')

# Retrieve the next page of unsubscribes
while [ "$next_token" != "null" ]
do
    echo "Retrieving next page of unsubscribes with NextToken: $next_token"
    response=$(aws sesv2 list-contacts --contact-list-name TransactionalContactList --filter "FilteredStatus=OPT_OUT,TopicFilter={TopicName=CarrierAvailability,UseDefaultIfPreferenceUnavailable=true}" --next-token "$next_token")
    echo "$response" | jq -r '.Contacts[].EmailAddress' >> carrier-availability-unsubscribes.txt
    next_token=$(echo "$response" | jq -r '.NextToken')
done

# Upload file to S3
aws s3 cp ./carrier-availability-unsubscribes.txt s3://carriersource-v2-files-production/manual-uploads/
