# Download census file from https://ai.fmcsa.dot.gov/SMS/Tools/Downloads.aspx
# Run this file with `rails runner bin/extract_census_emails.rb`
# Upload the generated file to s3://carriersource-v2-files-production/manual-uploads/census-emails.csv
# Run `rails data_uploads:upload_census_emails` to update the emails in the database

data = File.read('census.txt').encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
input = CSV.new(data, headers: true)

data = CSV.generate do |csv|
  csv << %w(dot_number email)

  while (row = input.shift)
    email = Functions::NormalizeEmail.call(row['EMAIL_ADDRESS'])
    next if email.blank?
    csv << [row['DOT_NUMBER'], email]
  end
end

File.write('census-emails.csv', data)
