require 'rails_helper'

RSpec.describe Records::TruckType do
  subject(:records) { described_class.new }

  describe '#where' do
    context 'with key option' do
      it 'returns correct truck types' do
        expect(records.where(key: %w(tanker reefer))).to contain_exactly(truck_types(:tanker), truck_types(:reefer))
      end
    end

    context 'with unknown option' do
      it 'raises error' do
        expect { records.where(symbol: 'tanker') }.to raise_error ArgumentError
      end
    end
  end
end
