require 'rails_helper'

RSpec.describe Records::Freight do
  subject(:records) { described_class.new }

  describe '#where' do
    context 'with uuid option' do
      it 'returns correct freights' do
        expect(records.where(uuid: freights(:household).uuid)).to contain_exactly(freights(:household))
      end
    end

    context 'with unknown option' do
      it 'raises error' do
        expect { records.where(symbol: 'household') }.to raise_error ArgumentError
      end
    end
  end
end
