require 'rails_helper'

RSpec.describe Records::Sentiment do
  subject(:records) { described_class.new }

  let!(:positive_sentiment) { create :sentiment, :positive, label: 'On Time Pickup' }
  let!(:negative_sentiment) { create :sentiment, :negative, label: 'Double Brokering' }

  describe '#where' do
    context 'when positive option is true' do
      it 'returns correct sentiments' do
        expect(records.where(positive: true)).to contain_exactly(positive_sentiment)
      end
    end

    context 'when positive option is false' do
      it 'returns correct sentiments' do
        expect(records.where(positive: false)).to contain_exactly(negative_sentiment)
      end
    end

    context 'when positive option is an array' do
      it 'returns correct sentiments' do
        expect(records.where(positive: [true, false])).to contain_exactly(positive_sentiment, negative_sentiment)
      end
    end

    context 'with unknown option' do
      it 'raises error' do
        expect { records.where(name: 'Positive') }.to raise_error ArgumentError
      end
    end
  end

  describe '#find_by' do
    context 'when label option is present' do
      it 'returns correct sentiment' do
        expect(records.find_by(label: 'On Time Pickup')).to eq positive_sentiment
      end
    end

    context 'with unknown option' do
      it 'raises error' do
        expect { records.find_by(name: 'Positive') }.to raise_error ArgumentError
      end
    end
  end
end
