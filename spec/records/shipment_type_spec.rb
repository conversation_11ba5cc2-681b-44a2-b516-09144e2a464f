require 'rails_helper'

RSpec.describe Records::ShipmentType do
  subject(:records) { described_class.new }

  describe '#where' do
    context 'with key option' do
      it 'returns correct shipment types' do
        expect(records.where(key: %w(ftl partial))).to contain_exactly(shipment_types(:ftl), shipment_types(:partial))
      end
    end

    context 'with unknown option' do
      it 'raises error' do
        expect { records.where(label: 'CTL') }.to raise_error ArgumentError
      end
    end
  end
end
