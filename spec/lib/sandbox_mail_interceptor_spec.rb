require 'rails_helper'
require 'mail'

RSpec.describe SandboxMailInterceptor do
  describe '::delivering_email' do
    let(:message) do
      Mail::Message.new do |m|
        m.subject 'Welcome'
        m.to '<EMAIL>'
        m.cc '<EMAIL>'
        m.bcc '<EMAIL>'
      end
    end

    it 'changes subject' do
      described_class.delivering_email(message)
      expect(message.subject).to eq 'CarrierSource Sandbox (recipient: <EMAIL>) - Welcome'
    end

    it 'overrides recipient' do
      described_class.delivering_email(message)
      expect(message.to).to eq ['<EMAIL>']
    end

    it 'nilifies cc' do
      described_class.delivering_email(message)
      expect(message.cc).to be_nil
    end

    it 'nilifies bcc' do
      described_class.delivering_email(message)
      expect(message.bcc).to be_nil
    end
  end
end
