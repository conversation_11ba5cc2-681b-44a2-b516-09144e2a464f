require 'rails_helper'

RSpec.describe Module do
  let(:test_module) do
    Module.new do
      const_set('CODE', 1)
      const_set('STATUS', 2)
      const_set('Child', Module.new { const_set('CODE', 3) })
    end
  end

  before do
    stub_const('TestModule', test_module)
  end

  describe '#safe_const_get' do
    it 'gets constant' do
      expect(TestModule::Child.safe_const_get('CODE')).to eq 3
    end

    it 'does not traverse up the ancestry' do
      expect(TestModule::Child.safe_const_get('STATUS')).to be_nil
    end

    it 'yields when missing' do
      expect(TestModule::Child.safe_const_get('STATUS') { 'foo' }).to eq 'foo'
    end
  end
end
