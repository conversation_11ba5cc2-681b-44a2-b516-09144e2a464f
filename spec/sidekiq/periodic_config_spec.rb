require 'rails_helper'

RSpec.describe Sidekiq::PeriodicConfig do
  subject(:config) { described_class.new }

  describe '#to_proc' do
    let(:job_name_verifier) do
      Class.new do
        def register(_cron, name, job_options = {})
          name.constantize.perform_inline(*job_options.fetch(:args, []))
        end
      end
    end

    let(:cron_schedule_verifier) do
      Class.new do
        def register(cron, _name, *)
          Sidekiq::CronParser.new(cron)
        end
      end
    end

    before do
      stub_request(:post, 'https://api.render.com/v1/services/www-service-id/jobs').to_return(status: 201)
    end

    it 'contains correct job names' do
      expect { config.to_proc.call(job_name_verifier.new) }.not_to raise_error
    end

    it 'contains correct cron expressions' do
      expect { config.to_proc.call(cron_schedule_verifier.new) }.not_to raise_error
    end
  end
end
