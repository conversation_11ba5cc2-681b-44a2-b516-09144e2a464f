require 'rails_helper'
require 'capybara/cuprite'

Dir['./system/support/**/*.rb'].each do |file|
  require File.join(File.dirname(file), File.basename(file, '.rb'))
end

RSpec.configure do |config|
  config.before(:each, type: :system) do
    driven_by :cuprite, screen_size: [1220, 800],
                        options: {
                          process_timeout: 10,
                          inspector: true,
                          headless: !ENV['HEADLESS'].in?(%w(n 0 no false))
                        }
  end

  config.before :each, type: :system do |example|
    subdomain = example.metadata.fetch(:subdomain, 'www')
    Capybara.app_host = "http://#{subdomain}.lvh.me"
    Capybara.server_port = 3001
  end
end
