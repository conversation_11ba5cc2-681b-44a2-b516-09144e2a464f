{"object": "list", "data": [{"id": "price_1LhGmYAh05kH1vv0Sm5CRerr", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "broker.essential.month", "metadata": {}, "nickname": null, "product": "prod_MQ70JSSibtkYjC", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 10000, "unit_amount_decimal": "10000"}, {"id": "price_1LhGmYAh05kH1vv0SL4jmoOZ", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "broker.essential.year", "metadata": {}, "nickname": null, "product": "prod_MQ70JSSibtkYjC", "recurring": {"aggregate_usage": null, "interval": "year", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 100000, "unit_amount_decimal": "100000"}, {"id": "price_1LhGtOAh05kH1vv0i5y53xQM", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "broker.premium.month", "metadata": {}, "nickname": null, "product": "prod_MQ77Ys8Ehxkvjx", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 20000, "unit_amount_decimal": "20000"}, {"id": "price_1LhGtOAh05kH1vv0xFnJcLn9", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "broker.premium.year", "metadata": {}, "nickname": null, "product": "prod_MQ77Ys8Ehxkvjx", "recurring": {"aggregate_usage": null, "interval": "year", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 200000, "unit_amount_decimal": "200000"}], "has_more": false, "url": "/v1/prices"}