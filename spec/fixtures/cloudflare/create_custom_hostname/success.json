{"result": {"id": "df9f9297-f17e-4fbf-afe6-7e016a23e2f3", "hostname": "web.carriersource.io", "ssl": {"id": "5417e6cf-abbb-4210-9e72-5ebb666236b2", "type": "dv", "method": "http", "status": "initializing", "wildcard": false, "certificate_authority": "google"}, "status": "pending", "ownership_verification": {"type": "txt", "name": "_cf-custom-hostname.web.carriersource.io", "value": "56407904-a10e-4fd2-aeae-f95c6ba45ea0"}, "ownership_verification_http": {"http_url": "http://web.carriersource.io/.well-known/cf-custom-hostname-challenge/df9f9297-f17e-4fbf-afe6-7e016a23e2f3", "http_body": "56407904-a10e-4fd2-aeae-f95c6ba45ea0"}, "created_at": "2023-02-15T14:51:53.357936Z"}, "success": true, "errors": [], "messages": []}