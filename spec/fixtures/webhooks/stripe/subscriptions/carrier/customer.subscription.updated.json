{"id": "evt_1LhhFLAh05kH1vv0HOqUMmzA", "data": {"object": {"id": "sub_1LheqLAh05kH1vv0Jeo9h2eA", "plan": {"id": "price_1NYAmeAh05kH1vv0WSbWOaMu", "active": true, "amount": 200000, "object": "plan", "created": **********, "product": "prod_MQ77Ys8Ehxkvjx", "currency": "usd", "interval": "year", "livemode": false, "metadata": {}, "nickname": null, "tiers_mode": null, "usage_type": "licensed", "amount_decimal": "200000", "billing_scheme": "per_unit", "interval_count": 1, "aggregate_usage": null, "transform_usage": null, "trial_period_days": null}, "items": {"url": "/v1/subscription_items?subscription=sub_1LheqLAh05kH1vv0Jeo9h2eA", "data": [{"id": "si_MQVs9C3rhjbPcB", "plan": {"id": "price_1NYAmeAh05kH1vv0WSbWOaMu", "active": true, "amount": 200000, "object": "plan", "created": **********, "product": "prod_MQ77Ys8Ehxkvjx", "currency": "usd", "interval": "year", "livemode": false, "metadata": {"plan_type": "premium"}, "nickname": null, "tiers_mode": null, "usage_type": "licensed", "amount_decimal": "200000", "billing_scheme": "per_unit", "interval_count": 1, "aggregate_usage": null, "transform_usage": null, "trial_period_days": null}, "price": {"id": "price_1NYAmeAh05kH1vv0WSbWOaMu", "type": "recurring", "active": true, "object": "price", "created": **********, "product": "prod_MQ77Ys8Ehxkvjx", "currency": "usd", "livemode": false, "metadata": {"plan_type": "premium"}, "nickname": null, "recurring": {"interval": "year", "usage_type": "licensed", "interval_count": 1, "aggregate_usage": null, "trial_period_days": null}, "lookup_key": null, "tiers_mode": null, "unit_amount": 200000, "tax_behavior": "unspecified", "billing_scheme": "per_unit", "custom_unit_amount": null, "transform_quantity": null, "unit_amount_decimal": "200000"}, "object": "subscription_item", "created": **********, "metadata": {}, "quantity": 1, "tax_rates": [], "subscription": "sub_1LheqLAh05kH1vv0Jeo9h2eA", "billing_thresholds": null}], "object": "list", "has_more": false, "total_count": 1}, "object": "subscription", "status": "active", "created": **********, "currency": "usd", "customer": "cus_MQVs30wgGZHQgM", "discount": null, "ended_at": null, "livemode": false, "metadata": {"dot_number": 1614362}, "quantity": 1, "schedule": null, "cancel_at": **********, "trial_end": null, "start_date": **********, "test_clock": null, "application": null, "canceled_at": **********, "description": null, "trial_start": null, "automatic_tax": {"enabled": false}, "transfer_data": null, "days_until_due": null, "default_source": null, "latest_invoice": "in_1LheqLAh05kH1vv0fJftvKjg", "pending_update": null, "pause_collection": null, "payment_settings": {"payment_method_types": null, "payment_method_options": null, "save_default_payment_method": "off"}, "collection_method": "charge_automatically", "default_tax_rates": [], "billing_thresholds": null, "current_period_end": **********, "billing_cycle_anchor": **********, "cancel_at_period_end": true, "current_period_start": **********, "pending_setup_intent": null, "default_payment_method": "pm_1LheqKAh05kH1vv0gYcPWIs3", "application_fee_percent": null, "pending_invoice_item_interval": null, "next_pending_invoice_item_invoice": null}, "previous_attributes": {"cancel_at": null, "canceled_at": null, "cancel_at_period_end": false}}, "type": "customer.subscription.updated", "object": "event", "created": **********, "request": {"id": null, "idempotency_key": "e8aa65bd-5635-428f-aab2-c12b3cb757c9"}, "livemode": false, "api_version": "2020-08-27", "pending_webhooks": 2}