{"id": "evt_1PnqAzAh05kH1vv0mlK4rj6q", "object": "event", "account": "acct_1IXs5OAh05kH1vv0", "api_version": "2020-03-02", "created": **********, "data": {"object": {"id": "sub_1PnqAxAh05kH1vv06GbOGd0v", "object": "subscription", "application": null, "application_fee_percent": null, "automatic_tax": {"enabled": false, "liability": null}, "billing_cycle_anchor": **********, "billing_cycle_anchor_config": null, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": null, "cancellation_details": {"comment": null, "feedback": null, "reason": null}, "collection_method": "charge_automatically", "created": **********, "currency": "usd", "current_period_end": **********, "current_period_start": **********, "customer": "cus_MqJGm3gw51EjrB", "days_until_due": null, "default_payment_method": null, "default_source": null, "default_tax_rates": [], "description": null, "discount": null, "discounts": [], "ended_at": null, "invoice_settings": {"account_tax_ids": null, "issuer": {"type": "self"}}, "items": {"object": "list", "data": [{"id": "si_QfAVgDLbstAzOP", "object": "subscription_item", "billing_thresholds": null, "created": **********, "discounts": [], "metadata": {}, "plan": {"id": "price_1PnlOBAh05kH1vv0LnMWnqAH", "object": "plan", "active": true, "aggregate_usage": null, "amount": 200000, "amount_decimal": "200000", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "year", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "product": "prod_Qf5ZgLLzy2I9oF", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed", "tiers": null}, "price": {"id": "price_1PnlOBAh05kH1vv0LnMWnqAH", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "brokerage.startup.year", "metadata": {}, "nickname": null, "product": "prod_Qf5ZgLLzy2I9oF", "recurring": {"aggregate_usage": null, "interval": "year", "interval_count": 1, "meter": null, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 200000, "unit_amount_decimal": "200000"}, "quantity": 1, "subscription": "sub_1PnqAxAh05kH1vv06GbOGd0v", "tax_rates": []}], "has_more": false, "total_count": 1, "url": "/v1/subscription_items?subscription=sub_1PnqAxAh05kH1vv06GbOGd0v"}, "latest_invoice": "in_1PnqAxAh05kH1vv00tZaBN1t", "livemode": false, "metadata": {"dot_number": "1614362"}, "next_pending_invoice_item_invoice": null, "on_behalf_of": null, "pause_collection": null, "payment_settings": {"payment_method_options": null, "payment_method_types": null, "save_default_payment_method": "off"}, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"id": "price_1PnlOBAh05kH1vv0LnMWnqAH", "object": "plan", "active": true, "aggregate_usage": null, "amount": 200000, "amount_decimal": "200000", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "year", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "product": "prod_Qf5ZgLLzy2I9oF", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed", "tiers": null}, "quantity": 1, "schedule": null, "start_date": **********, "status": "active", "test_clock": null, "transfer_data": null, "trial_end": null, "trial_settings": {"end_behavior": {"missing_payment_method": "create_invoice"}}, "trial_start": null, "tax_percent": null}}, "livemode": false, "pending_webhooks": 1, "request": {"id": "req_SBfXrWRUgXTywx", "idempotency_key": "44b8e16d-edc8-45e8-9c00-c2e5c53e4e17"}, "type": "customer.subscription.created"}