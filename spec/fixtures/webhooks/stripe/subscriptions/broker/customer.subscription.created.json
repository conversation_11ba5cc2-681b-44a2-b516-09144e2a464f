{"id": "evt_1LXTNoAh05kH1vv0XdUuWoJi", "object": "event", "api_version": "2020-08-27", "created": 1660670211, "data": {"object": {"id": "sub_1LXTNkAh05kH1vv0csrT4hCR", "object": "subscription", "application": null, "application_fee_percent": null, "automatic_tax": {"enabled": false}, "billing_cycle_anchor": **********, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": null, "collection_method": "charge_automatically", "created": **********, "currency": "usd", "current_period_end": 1663348608, "current_period_start": **********, "customer": "cus_MFzMLZJeflrSOj", "days_until_due": null, "default_payment_method": null, "default_source": null, "default_tax_rates": [], "description": null, "discount": null, "ended_at": null, "items": {"object": "list", "data": [{"id": "si_MFzMm2H6UGUwKl", "object": "subscription_item", "billing_thresholds": null, "created": **********, "metadata": {}, "plan": {"id": "price_1LhGtOAh05kH1vv0xFnJcLn9", "object": "plan", "active": true, "aggregate_usage": null, "amount": 2000, "amount_decimal": "2000", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_MFzMIkKfpIZwuk", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_1LhGtOAh05kH1vv0xFnJcLn9", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": null, "metadata": {}, "nickname": null, "product": "prod_MFzMIkKfpIZwuk", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 2000, "unit_amount_decimal": "2000"}, "quantity": 1, "subscription": "sub_1LXTNkAh05kH1vv0csrT4hCR", "tax_rates": []}], "has_more": false, "total_count": 1, "url": "/v1/subscription_items?subscription=sub_1LXTNkAh05kH1vv0csrT4hCR"}, "latest_invoice": "in_1LXTNkAh05kH1vv0z1iMCPGR", "livemode": false, "metadata": {"user_id": "<%= user_id %>"}, "next_pending_invoice_item_invoice": null, "pause_collection": null, "payment_settings": {"payment_method_options": null, "payment_method_types": null, "save_default_payment_method": "off"}, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"id": "price_1LhGtOAh05kH1vv0xFnJcLn9", "object": "plan", "active": true, "aggregate_usage": null, "amount": 2000, "amount_decimal": "2000", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_JAXA1kXOyUrLxs", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "quantity": 1, "schedule": null, "start_date": **********, "status": "active", "test_clock": null, "transfer_data": null, "trial_end": null, "trial_start": null}}, "livemode": false, "pending_webhooks": 1, "request": {"id": "req_tWqpzp2cCkiHfL", "idempotency_key": "24c88b83-48a6-448f-9b98-3888e58b69cf"}, "type": "customer.subscription.created"}