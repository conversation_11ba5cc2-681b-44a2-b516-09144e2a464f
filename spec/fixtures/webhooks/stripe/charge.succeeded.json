{"id": "evt_3LXdaGAh05kH1vv01jVbvDnm", "object": "event", "api_version": "2020-08-27", "created": 1660709425, "data": {"object": {"id": "ch_3LXdaGAh05kH1vv01EuidIf4", "object": "charge", "amount": 2000, "amount_captured": 2000, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": null, "balance_transaction": "txn_3LXdaGAh05kH1vv01FSjUN8f", "billing_details": {"address": {"city": null, "country": "US", "line1": null, "line2": null, "postal_code": null, "state": null}, "email": null, "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": "WWW.CARRIERSOURCE.IO", "captured": true, "created": 1660709424, "currency": "usd", "customer": "cus_MG8XsazRAgfQOJ", "description": "Subscription creation", "destination": null, "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": "in_1LXdaFAh05kH1vv0wzaXeJFr", "livemode": false, "metadata": {}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "risk_score": 17, "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_3LXdaGAh05kH1vv01BJIXKvx", "payment_method": "card_1LXdZsAh05kH1vv0BneJsMkP", "payment_method_details": {"card": {"brand": "visa", "checks": {"address_line1_check": null, "address_postal_code_check": null, "cvc_check": "pass"}, "country": "US", "exp_month": 2, "exp_year": 2026, "fingerprint": "rIjUXBXjH0LjRAk4", "funding": "credit", "installments": null, "last4": "4242", "mandate": null, "network": "visa", "three_d_secure": null, "wallet": null}, "type": "card"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/invoices/CAcaFwoVYWNjdF8xSVhzNU9BaDA1a0gxdnYwKLLU8ZcGMgZeQSTtySY6LBZ6MudKzJC_SvPLz52VGhkSLXVWmXQf0b0pKi-AHfcqd6B-5anjZ4tC7Vh4?s=ap", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/ch_3LXdaGAh05kH1vv01EuidIf4/refunds"}, "review": null, "shipping": null, "source": {"id": "card_1LXdZsAh05kH1vv0BneJsMkP", "object": "card", "address_city": null, "address_country": "US", "address_line1": null, "address_line1_check": null, "address_line2": null, "address_state": null, "address_zip": null, "address_zip_check": null, "brand": "Visa", "country": "US", "customer": "cus_MG8XsazRAgfQOJ", "cvc_check": "pass", "dynamic_last4": null, "exp_month": 2, "exp_year": 2026, "fingerprint": "rIjUXBXjH0LjRAk4", "funding": "credit", "last4": "4242", "metadata": {}, "name": "<PERSON>", "tokenization_method": null}, "source_transfer": null, "statement_descriptor": null, "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_JdLipdTLlDqirc", "idempotency_key": "82888953-079c-45b5-b773-7ffc524ed72b"}, "type": "charge.succeeded"}