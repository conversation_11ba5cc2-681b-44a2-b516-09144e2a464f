require 'rails_helper'

RSpec.describe Brokerages::SyncSpecializedServices do
  subject(:syncer) { described_class.new(brokerage) }

  let(:company) { create :company }
  let(:brokerage) { company.as_entity(:broker) }

  describe '#call' do
    context 'when profile values are present' do
      before do
        create :brokerage_profile,
               company:,
               specialized_services: [specialized_services(:last_mile).id, specialized_services(:drop_trailer).id]
      end

      context 'when review values are present' do
        before do
          create :brokerage_review, :carrier, company:, specialized_services: [specialized_services(:lift_gate).id]
        end

        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.specialized_services).to(
            contain_exactly(specialized_services(:last_mile), specialized_services(:drop_trailer))
          )
        end
      end

      context 'when review values are missing' do
        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.specialized_services).to(
            contain_exactly(specialized_services(:last_mile), specialized_services(:drop_trailer))
          )
        end
      end
    end

    context 'when profile values are missing' do
      context 'when review values are present' do
        let(:company) { create :company }

        before do
          create :brokerage_review, :carrier, :approved,
                 company:, specialized_services: [specialized_services(:lift_gate).id]
        end

        it 'uses review values' do
          syncer.call
          expect(brokerage.reload.specialized_services).to contain_exactly(specialized_services(:lift_gate))
        end
      end

      context 'when review values are missing' do
        it 'is empty' do
          syncer.call
          expect(brokerage.reload.specialized_services).to be_empty
        end
      end
    end

    context 'when hazmat indicator is true' do
      let(:company) { create :company, hazmat_indicator: true }

      it 'includes hazmat service' do
        syncer.call
        expect(brokerage.reload.specialized_services).to contain_exactly(specialized_services(:hazardous_materials))
      end
    end
  end
end
