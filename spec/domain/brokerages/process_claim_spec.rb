require 'rails_helper'

RSpec.describe Brokerages::ProcessClaim do
  subject(:processor) { described_class.new(brokerage:, current_user:) }

  let(:brokerage_profile) { create :brokerage_profile }
  let(:brokerage) { brokerage_profile.brokerage }

  describe '#call' do
    let!(:current_user) { create :user }

    context 'when email matches brokerage' do
      let!(:current_user) { create :user, email: brokerage.email }

      it 'creates verified brokerage profile user' do
        expect { processor.call }.to change(BrokerageProfileUser, :count).by(1)
        expect(BrokerageProfileUser.last).to be_verified
      end
    end

    context 'when email does not match' do
      it 'creates pending brokerage profile user' do
        expect { processor.call }.to change(BrokerageProfileUser, :count).by(1)
        expect(BrokerageProfileUser.last).to be_pending
      end
    end

    context 'when carrier profile has already been verified' do
      let!(:profile_user) do
        create :brokerage_profile_user, :verified, brokerage_profile:, user: current_user
      end

      it 'does not attempt to re-verify' do
        processor.call
        expect(profile_user.reload).to be_verified
      end
    end
  end
end
