require 'rails_helper'

RSpec.describe Brokerages::CalculateScoreJob do
  let(:brokerage_profile) { create :brokerage_profile }
  let(:company) { brokerage_profile.company }
  let(:brokerage) { brokerage_profile.brokerage }

  describe '::load' do
    let!(:review) { create :brokerage_review, :carrier, :approved, company: }

    it 'queues up job for brokerages with reviews' do
      described_class.load
      expect(described_class.jobs.pick('args')).to eq [brokerage.to_gid.to_s]
    end
  end

  describe '#perform' do
    let!(:review) { create :brokerage_review, :carrier, :approved, company:, last_worked_with: Date.new(2022, 4, 1) }

    it 'updates CS score for brokerage' do
      described_class.load
      described_class.drain
      expect(brokerage_profile.reload.cs_score).to be_positive
    end
  end
end
