require 'rails_helper'

RSpec.describe Brokerages::SyncTruckTypes do
  subject(:syncer) { described_class.new(brokerage) }

  let(:company) { create :company }
  let(:brokerage) { company.as_entity(:broker) }

  describe '#call' do
    context 'when profile values are present' do
      before do
        create :brokerage_profile, company:, truck_types: [truck_types(:flatbed).id, truck_types(:deck).id]
      end

      context 'when review values are present' do
        before do
          create :brokerage_review, :carrier, company:, truck_types: [truck_types(:van).id]
        end

        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.truck_types).to contain_exactly(truck_types(:flatbed), truck_types(:deck))
        end
      end

      context 'when review values are missing' do
        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.truck_types).to contain_exactly(truck_types(:flatbed), truck_types(:deck))
        end
      end
    end

    context 'when profile values are missing' do
      context 'when review values are present' do
        let(:census) { create :census, crgo_coldfood: 'X' }
        let(:company) { create :company, census: }

        before do
          create :brokerage_review, :carrier, :approved, company:, truck_types: [truck_types(:van).id]
        end

        it 'uses review values plus freight assumptions' do
          syncer.call
          expect(brokerage.reload.truck_types).to contain_exactly(truck_types(:van), truck_types(:reefer))
        end
      end

      context 'when review values are missing' do
        it 'is empty' do
          syncer.call
          expect(brokerage.reload.truck_types).to be_empty
        end
      end
    end
  end
end
