require 'rails_helper'

RSpec.describe Brokerages::UpsertReviewLanesJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:brokerage) { company.as_entity(:broker) }

    it 'delegates' do
      allow(Brokerages::UpsertReviewLanes).to receive(:call)
      described_class.perform_inline(brokerage.to_gid.to_s)
      expect(Brokerages::UpsertReviewLanes).to have_received(:call).with(brokerage)
    end
  end
end
