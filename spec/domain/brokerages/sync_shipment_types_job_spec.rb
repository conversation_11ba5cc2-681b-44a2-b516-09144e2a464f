require 'rails_helper'

RSpec.describe Brokerages::SyncShipmentTypesJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:brokerage) { company.as_entity(:broker) }

    it 'delegates' do
      allow(Brokerages::SyncShipmentTypes).to receive(:call)
      described_class.perform_inline(brokerage.to_gid.to_s)
      expect(Brokerages::SyncShipmentTypes).to have_received(:call).with(brokerage)
    end
  end

  describe '::batch_load' do
    let(:company) { create :company, :broker, :with_authority, :with_city }
    let(:brokerage) { company.as_entity(:broker) }

    it 'loads jobs in batches by gid' do
      described_class.batch_load model: Brokerage, finish: company.id
      expect(described_class.jobs.pick('args')).to eq [brokerage.to_gid.to_s]
    end
  end
end
