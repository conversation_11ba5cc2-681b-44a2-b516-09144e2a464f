require 'rails_helper'

RSpec.describe Brokerages::GenerateBio do
  subject(:generator) { described_class.new(company) }

  describe '#call' do
    let(:census) { create :census, dot_number: 356 }
    let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
    let(:postal_code) { create :postal_code, code: '60606' }

    let(:company) do
      create :company, :broker, :with_city, census:, dba_name: 'Putnik Express', city:, postal_code:
    end

    context 'with active operating authority' do
      let!(:operating_authority) do
        create :operating_authority, dot_number: company.dot_number, docket_number: 'MC027831',
                                     broker_authority: 'active'
      end

      context 'with no authority history available' do
        it 'generates bio' do
          expect(generator.call).to eq <<~TXT.squish
            Putnik Express is an active freight brokerage based out of Chicago, Illinois. Putnik Express has
            been authorized to operate under MC027831 and USDOT 356.
          TXT
        end
      end
    end

    context 'with inactive operating authority' do
      let!(:operating_authority) do
        create :operating_authority, dot_number: company.dot_number, docket_number: 'MC027831',
                                     broker_authority: 'inactive'
      end

      it 'generates bio' do
        expect(generator.call).to eq <<~TXT.squish
          Putnik Express is an inactive freight brokerage based out of Chicago, Illinois.
        TXT
      end
    end
  end
end
