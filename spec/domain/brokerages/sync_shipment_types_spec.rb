require 'rails_helper'

RSpec.describe Brokerages::SyncShipmentTypes do
  subject(:syncer) { described_class.new(brokerage) }

  let(:company) { create :company }
  let(:brokerage) { company.as_entity(:broker) }

  describe '#call' do
    context 'when profile values are present' do
      before do
        create :brokerage_profile, company:, shipment_types: [shipment_types(:ftl).id, shipment_types(:partial).id]
      end

      context 'when review values are present' do
        before do
          create :brokerage_review, :carrier, company:, shipment_types: [shipment_types(:ltl).id]
        end

        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.shipment_types).to contain_exactly(shipment_types(:ftl), shipment_types(:partial))
        end
      end

      context 'when review values are missing' do
        it 'gives precedence to profile' do
          syncer.call
          expect(brokerage.reload.shipment_types).to contain_exactly(shipment_types(:ftl), shipment_types(:partial))
        end
      end
    end

    context 'when profile values are missing' do
      context 'when review values are present' do
        let(:census) { create :census, crgo_chem: 'X' }
        let(:company) { create :company, census: }

        before do
          create :brokerage_review, :carrier, :approved, company:, shipment_types: [shipment_types(:ltl).id]
        end

        it 'uses review values plus freight assumptions' do
          syncer.call
          expect(brokerage.reload.shipment_types).to contain_exactly(shipment_types(:ltl), shipment_types(:ftl))
        end
      end

      context 'when review values are missing' do
        it 'is empty' do
          syncer.call
          expect(brokerage.reload.shipment_types).to be_empty
        end
      end
    end
  end
end
