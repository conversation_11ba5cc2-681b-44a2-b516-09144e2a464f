require 'rails_helper'

RSpec.describe Brokerages::Location do
  subject(:location) { described_class.new(company.as_entity(:broker)) }

  let(:company) { create :company, :broker, :with_city }

  describe '#cities' do
    context 'with no city' do
      let(:company) { create :company, :broker, city: nil }

      it 'returns empty array' do
        expect(location.cities).to be_empty
      end
    end

    context 'with city' do
      it 'returns array of cities' do
        expect(location.cities).to all(be_a(Companies::Location::City))
      end
    end
  end

  describe '#states' do
    it 'returns array of states' do
      expect(location.states).to all(be_a(Geo::State))
    end
  end

  describe '#countries' do
    it 'returns array of countries' do
      expect(location.countries).to all(be_a(Geo::Country))
    end
  end
end
