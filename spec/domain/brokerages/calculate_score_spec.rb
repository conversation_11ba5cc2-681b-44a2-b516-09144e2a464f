require 'rails_helper'

RSpec.describe Brokerages::CalculateScore do
  subject(:calculator) { described_class.new(brokerage:, average_review_count:) }

  let(:brokerage_profile) { create :brokerage_profile }
  let(:company) { brokerage_profile.company }
  let(:brokerage) { brokerage_profile.brokerage }
  let(:average_review_count) { 1.62 }

  describe '#call' do
    context 'when brokerage has no reviews' do
      it 'returns nil' do
        expect(calculator.call).to be_nil
      end
    end

    context 'when brokerage reviews do not qualify' do
      before do
        create :brokerage_review, :carrier, :rejected, company:
      end

      it 'returns nil' do
        expect(calculator.call).to be_nil
      end
    end

    context 'with qualifying reviews' do
      before do
        create :brokerage_review, :carrier, :approved,
               company:, nps: 9, communication: 8, is_consider_next_time: true, how_often: 1
        create :brokerage_review, :carrier, :approved,
               company:, nps: 10, communication: 10, is_consider_next_time: true, how_often: 2
        create :brokerage_review, :carrier, :approved,
               company:, nps: 5, communication: 1, is_consider_next_time: false, how_often: 1
      end

      it 'calculates score' do
        expect(calculator.call).to be_within(0.01).of(74.05)
      end
    end
  end
end
