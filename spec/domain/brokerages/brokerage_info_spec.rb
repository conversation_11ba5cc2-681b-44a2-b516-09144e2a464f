require 'rails_helper'

RSpec.describe Brokerages::BrokerageInfo do
  let(:proxy) { described_class.new(brokerage) }

  let(:brokerage_profile) { create :brokerage_profile }
  let(:brokerage) { brokerage_profile.brokerage }

  describe '#assets' do
    before do
      BrokerageProfileAsset.create(brokerage_profile:, file: fixture_file_upload('logo.png', 'image/png'))
      BrokerageProfileAsset.create(brokerage_profile:, file: fixture_file_upload('blank.pdf', 'application/pdf'))
    end

    context 'when upgraded' do
      before do
        allow(brokerage_profile).to receive(:access_to_feature?).with(:assets).and_return(true)
      end

      it 'returns assets' do
        expect(proxy.assets).to be_a ActiveRecord::Relation
        expect(proxy.assets).not_to be_empty
      end

      describe '#images' do
        it 'returns images' do
          expect(proxy.images).not_to be_empty
        end
      end

      describe '#documents' do
        it 'returns documents' do
          expect(proxy.documents).not_to be_empty
        end
      end
    end

    context 'when not upgraded' do
      it 'returns empty collection' do
        expect(proxy.assets).to be_a ActiveRecord::Relation
        expect(proxy.assets).to be_empty
      end

      describe '#images' do
        it 'returns empty collection' do
          expect(proxy.images).to be_empty
        end
      end

      describe '#documents' do
        it 'returns empty collection' do
          expect(proxy.documents).to be_empty
        end
      end
    end
  end

  describe '#insurance_limit' do
    context 'when insurances are present' do
      before do
        create(:operating_authority, company: brokerage_profile.company, broker_authority: 'ACTIVE').then do |oa|
          create :insurance, :bipd, operating_authority: oa, bi_pd_max_limit: 1000
          create :insurance, :cargo, operating_authority: oa, bi_pd_max_limit: 2000
        end
      end

      it 'returns highest policy limit' do
        expect(proxy.insurance_limit).to eq '$2,000,000'
      end
    end

    context 'when insurances are not present' do
      it 'returns None' do
        expect(proxy.insurance_limit).to eq 'None'
      end
    end
  end
end
