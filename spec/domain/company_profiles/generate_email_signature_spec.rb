require 'rails_helper'

RSpec.describe CompanyProfiles::GenerateEmailSignature do
  subject(:generator) { described_class.new(carrier) }

  let(:company) { create :company }
  let(:carrier) { company.as_entity(:carrier) }

  before do
    stub_request(:post, 'https://www.example.com/html-to-image').to_return(status:, body: 'image')
  end

  describe '#call' do
    context 'when response is successful' do
      let(:status) { 200 }

      it 'returns the response' do
        expect(generator.call).to be_a HTTP::Response
      end
    end

    context 'when response is not successful' do
      let(:status) { 500 }

      it 'raises an error' do
        expect { generator.call }.to raise_error StandardError
      end
    end
  end
end
