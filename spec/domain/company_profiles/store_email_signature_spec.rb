require 'rails_helper'

RSpec.describe CompanyProfiles::StoreEmailSignature do
  subject(:storage) { described_class.new(carrier) }

  let(:carrier_profile) { create :carrier_profile }
  let(:carrier) { carrier_profile.carrier }
  let(:response) { instance_double(HTTP::Response, body:) }
  let(:body) { instance_double(HTTP::Response::Body, to_s: 'image') }

  before do
    allow(CompanyProfiles::GenerateEmailSignature).to receive(:call).and_return(response)
  end

  describe '#call' do
    it 'attaches email signature' do
      storage.call
      expect(carrier.reload.profile.email_signature).to be_attached
    end
  end
end
