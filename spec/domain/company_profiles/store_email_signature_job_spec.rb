require 'rails_helper'

RSpec.describe CompanyProfiles::StoreEmailSignatureJob do
  describe '#perform' do
    let(:company) { create :company, :broker }
    let(:brokerage) { company.as_entity(:broker) }

    before do
      allow(CompanyProfiles::StoreEmailSignature).to receive(:call)
    end

    it 'calls the service' do
      described_class.perform_inline(brokerage.to_gid.to_s)
      expect(CompanyProfiles::StoreEmailSignature).to have_received(:call).with(brokerage)
    end
  end
end
