require 'rails_helper'

RSpec.describe PageViews::FetchAndStoreJob do
  subject(:job) { described_class.new }

  let(:date) { '2022-11-17' }
  let(:uuid) { SecureRandom.uuid }

  let(:authorization) { instance_double Google::Auth::ServiceAccountCredentials }

  let(:body_one) { Rails.root.join('spec/fixtures/analytics_data/response1.json').read }
  let(:body_two) { Rails.root.join('spec/fixtures/analytics_data/response2.json').read }

  before do
    allow(Google::Auth::ServiceAccountCredentials).to receive(:make_creds).and_return(authorization)

    create :company, legal_name: 'Bollig Trucking LLC'
    create :company, legal_name: 'Terra Trucking LLC'

    stub_request(:post, 'https://analyticsdata.googleapis.com/v1beta/properties/*********:runReport')
      .to_return(status: 200, body: body_one, headers: { 'Content-Type' => 'application/json' }).then
      .to_return(status: 200, body: body_two, headers: { 'Content-Type' => 'application/json' })
  end

  context 'with all pages' do
    it 'paginates through all' do
      described_class.perform_async(uuid, date, 'all', 5, 0)
      described_class.drain
      expect(PageView.pluck(:page_views, :average_session_duration)).to(
        contain_exactly([2, 2720], [3, 2205])
      )
    end
  end
end
