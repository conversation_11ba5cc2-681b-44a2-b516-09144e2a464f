require 'rails_helper'

RSpec.describe Carriers::SyncTruckTypesJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:carrier) { company.as_entity(:carrier) }

    it 'delegates' do
      allow(Carriers::SyncTruckTypes).to receive(:call)
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(Carriers::SyncTruckTypes).to have_received(:call).with(carrier)
    end
  end

  describe '::batch_load' do
    let(:company) { create :company, :carrier, :with_authority, :with_city }
    let(:carrier) { company.as_entity(:carrier) }

    it 'loads jobs in batches by gid' do
      described_class.batch_load model: Carrier, finish: carrier.id
      expect(described_class.jobs.pick('args')).to eq [carrier.to_gid.to_s]
    end
  end
end
