require 'rails_helper'

RSpec.describe Carriers::CalculateScoreJob do
  let(:carrier_profile) { create :carrier_profile }
  let(:company) { carrier_profile.company }
  let(:carrier) { carrier_profile.carrier }

  describe '::load' do
    let!(:review) { create :review, :approved, company: }

    it 'queues up job for carriers with reviews' do
      described_class.load
      expect(described_class.jobs.pick('args')).to eq [carrier.to_gid.to_s]
    end
  end

  describe '#perform' do
    let!(:review) { create :review, :approved, company:, last_worked_with: Date.new(2022, 4, 1) }

    it 'updates CS score for carrier' do
      described_class.load
      described_class.drain
      expect(review.carrier.profile.reload.cs_score).to be_positive
    end
  end
end
