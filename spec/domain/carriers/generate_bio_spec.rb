require 'rails_helper'

RSpec.describe Carriers::GenerateBio do
  subject(:generator) { described_class.new(carrier) }

  describe '#call' do
    let(:dot_number) { 356 }
    let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
    let(:postal_code) { create :postal_code, code: '60606' }

    let(:company) do
      create :company, :carrier, :with_city, dot_number:, dba_name: 'Putnik Express', city:, postal_code:,
                                             **company_attributes
    end

    let(:carrier) { company.as_entity(:carrier) }

    let(:company_attributes) { { carrier_operation: 'interstate' } }

    context 'with active operating authority' do
      let!(:census) { create :census, dot_number: }

      let!(:operating_authority) do
        create :operating_authority, dot_number: company.dot_number, docket_number: 'MC027831'
      end

      context 'with no authority history available' do
        it 'generates bio' do
          expect(generator.call).to eq <<~TXT.squish
            Putnik Express is an active interstate freight carrier based out of Chicago, Illinois. Putnik Express has
            been authorized to operate under MC027831 and USDOT 356.
          TXT
        end
      end
    end

    context 'with intrastate operation' do
      let!(:census) { create :census, dot_number: }
      let(:company_attributes) { { carrier_operation: 'intrastate' } }

      it 'generates bio' do
        expect(generator.call).to eq <<~TXT.squish
          Putnik Express is an active intrastate freight carrier based out of Chicago, Illinois.
        TXT
      end
    end

    context 'with no census record' do
      it 'generates bio' do
        expect(generator.call).to eq <<~TXT.squish
          Putnik Express is an inactive interstate freight carrier based out of Chicago, Illinois.
        TXT
      end
    end
  end
end
