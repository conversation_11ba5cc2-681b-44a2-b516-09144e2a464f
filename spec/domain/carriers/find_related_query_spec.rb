require 'rails_helper'

RSpec.describe Carriers::FindRelatedQuery do
  describe '#call' do
    subject(:builder) { described_class.new(carrier) }

    let(:carrier) { create(:carrier) }

    context 'when carrier is active' do
      let(:carrier) { create :company }

      it 'returns elastic query' do
        expect(builder.call.to_hash.dig(:query, :bool, :minimum_should_match)).to eq 1
      end
    end

    context 'when carrier is not active' do
      let(:carrier) { create :company, phone: nil, street: nil, zip: nil }

      it 'returns non-matching elastic query' do
        expect(builder.call).to eq described_class::NONE_QUERY
      end
    end
  end
end
