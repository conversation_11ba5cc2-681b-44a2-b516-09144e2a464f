require 'rails_helper'

RSpec.describe Carriers::ProcessClaim do
  subject(:processor) { described_class.new(carrier:, current_user:) }

  let(:carrier_profile) { create :carrier_profile }
  let(:carrier) { carrier_profile.carrier }

  describe '#call' do
    context 'when current_user exists' do
      let!(:current_user) { create :user }
      let(:params) { {} }

      context 'when email matches carrier' do
        let!(:current_user) { create :user, email: carrier.email }

        it 'creates verified carrier profile user' do
          expect { processor.call }.to change(CarrierProfileUser, :count).by(1)
          expect(CarrierProfileUser.last).to be_verified
        end
      end

      context 'when email does not match' do
        it 'creates pending carrier profile user' do
          expect { processor.call }.to change(CarrierProfileUser, :count).by(1)
          expect(CarrierProfileUser.last).to be_pending
        end
      end

      context 'when carrier profile has already been verified' do
        let!(:profile_user) do
          create :carrier_profile_user, :verified, carrier_profile:, user: current_user
        end

        it 'does not attempt to re-verify' do
          processor.call
          expect(profile_user.reload).to be_verified
        end
      end
    end
  end
end
