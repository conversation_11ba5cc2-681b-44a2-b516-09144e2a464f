require 'rails_helper'

RSpec.describe Carriers::Authority do
  describe 'comparable' do
    subject(:authorities) { Carriers::Authorities.for(operating_authority.company) }

    context 'when all types are active' do
      let(:operating_authority) do
        build :operating_authority, common_authority: 'active', contract_authority: 'active',
                                    pending_common_authority: true, contract_authority_revocation: true
      end

      context 'when contract date is earlier' do
        before do
          operating_authority.build_authhist(common: Date.new(2021, 1, 1), contract: Date.new(2019, 1, 1))
        end

        it 'prioritizes contract' do
          expect(authorities.min).to have_attributes type: :contract
        end

        it 'sorts by type' do
          travel_to Date.new(2022, 8, 1) do
            authorities.flat_map(&:statuses).sort.tap do |statuses|
              expect(statuses[0]).to be_a Carriers::Authority::Active
              expect(statuses[0]).to have_attributes type: :common, title: 'Common Active',
                                                     duration: '1 year, 6 months', css: 'bg-green border-green-700'

              expect(statuses[1]).to be_a Carriers::Authority::Active
              expect(statuses[1]).to have_attributes type: :contract, title: 'Contract Active',
                                                     duration: '3 years, 6 months', css: 'bg-green border-green-700'

              expect(statuses[2]).to be_a Carriers::Authority::ApplicationPending
              expect(statuses[2]).to have_attributes type: :common, css: 'bg-yellow-400 border-yellow-500'

              expect(statuses[3]).to be_a Carriers::Authority::PendingRevocation
              expect(statuses[3]).to have_attributes type: :contract, css: 'bg-red-300 border-red-500'
            end
          end
        end
      end

      context 'when common date is missing' do
        before do
          operating_authority.build_authhist(contract: Date.new(2019, 1, 1))
        end

        it 'prioritizes contract' do
          expect(authorities.min).to have_attributes type: :contract
        end

        it 'sorts by type' do
          travel_to Date.new(2022, 8, 1) do
            authorities.flat_map(&:statuses).sort.tap do |statuses|
              expect(statuses[0]).to be_a Carriers::Authority::Active
              expect(statuses[0]).to have_attributes type: :common, title: 'Common Active', duration: nil,
                                                     css: 'bg-green border-green-700'

              expect(statuses[1]).to be_a Carriers::Authority::Active
              expect(statuses[1]).to have_attributes type: :contract, title: 'Contract Active',
                                                     duration: '3 years, 6 months', css: 'bg-green border-green-700'
            end
          end
        end
      end
    end

    context 'when only contract is active' do
      let(:operating_authority) do
        build :operating_authority, common_authority: 'inactive', contract_authority: 'active'
      end

      before do
        operating_authority.build_authhist(common: Date.new(2020, 1, 1), contract: Date.new(2021, 1, 1))
      end

      it 'prioritizes contract' do
        expect(authorities.min).to have_attributes type: :contract
      end
    end

    context 'when none are active' do
      context 'when contract is pending and common is revoked' do
        let(:operating_authority) do
          build :operating_authority, common_authority: 'none', contract_authority: 'inactive',
                                      pending_contract_authority: true, common_authority_revocation: true
        end

        it 'prioritizes contract' do
          expect(authorities.min).to have_attributes type: :contract
        end

        it 'returns pending status' do
          expect(authorities.min.statuses.first).to have_attributes title: 'Contract Application Pending'
        end
      end

      context 'when contract is revoked and common is revoked' do
        let(:operating_authority) do
          build :operating_authority, common_authority: 'none', contract_authority: 'inactive',
                                      contract_authority_revocation: true, common_authority_revocation: true
        end

        it 'prioritizes contract' do
          expect(authorities.min).to have_attributes type: :contract
        end

        it 'returns revoked status' do
          expect(authorities.min.statuses.first).to have_attributes title: 'Pending Contract Revocation'
        end
      end

      context 'when none are pending or revoked' do
        let(:operating_authority) do
          build :operating_authority, common_authority: 'none', contract_authority: 'inactive'
        end

        it 'prioritizes common' do
          expect(authorities.min).to have_attributes type: :contract
        end

        it 'returns empty statuses' do
          expect(authorities.min.statuses).to eq []
        end

        it 'returns inactive null status' do
          expect(authorities.min.null_status).to be_a Carriers::Authority::Inactive
        end

        it 'returns inactive null status with correct attributes' do
          expect(authorities.min.null_status).to have_attributes title: 'Inactive', css: 'bg-red-300 border-red-500'
        end
      end
    end
  end
end
