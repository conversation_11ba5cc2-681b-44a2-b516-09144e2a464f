require 'rails_helper'

RSpec.describe Carriers::ScrapeEmailJob do
  subject(:job) { described_class.new }

  describe '::batch_load' do
    let(:company_one) { create :company, :carrier, :with_city, email: '<EMAIL>' }
    let(:company_two) { create :company, :carrier, :with_city, email: nil }

    it 'queues jobs for companies with missing emails' do
      described_class.batch_load model: Company, finish: [company_one.id, company_two.id].max
      expect(described_class.jobs.pluck('args')).to eq [[company_two.id]]
    end
  end

  describe '#perform' do
    let(:company) { create :company }

    it 'calls Carriers::ScrapeEmail' do
      allow(Carriers::ScrapeEmail).to receive(:call)
      job.perform(company.id)
      expect(Carriers::ScrapeEmail).to have_received(:call).with(company)
    end
  end
end
