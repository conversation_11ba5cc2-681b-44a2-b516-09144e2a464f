require 'rails_helper'

RSpec.describe Carriers::ProtectedFeature do
  subject { gated_contact_information.access }

  let(:gated_contact_information) { described_class.new(user:) }
  let(:user) { build_stubbed :user }

  describe '#access' do
    context 'when the user is not logged in' do
      let(:user) { nil }

      it { is_expected.to eq(:login) }
    end

    context 'when the user is logged in' do
      context 'when the user is upgraded' do
        before { allow(user).to receive(:upgraded?).and_return(true) }

        it { is_expected.to eq(:granted) }
      end

      context 'when the user is not upgraded' do
        context 'when the user is a broker' do
          context 'when the user is verified' do
            before do
              user.personas.build(type: 'broker', status: 'verified')
            end

            it { is_expected.to eq(:granted) }
          end

          context 'when the user is not verified' do
            before do
              user.personas.build(type: 'broker', status: 'pending')
            end

            it { is_expected.to eq(:verification) }
          end
        end

        context 'when the user is a dispatcher' do
          context 'when the user is verified' do
            before do
              user.personas.build(type: 'dispatcher', status: 'verified')
            end

            it { is_expected.to eq(:upgrade) }
          end

          context 'when the user is not verified' do
            before do
              user.personas.build(type: 'dispatcher', status: 'pending')
            end

            it { is_expected.to eq(:upgrade) }
          end
        end

        context 'when the user is a carrier' do
          before do
            user.personas.build(type: 'carrier', status: 'verified')
          end

          it { is_expected.to eq(:unavailable) }
        end
      end
    end
  end
end
