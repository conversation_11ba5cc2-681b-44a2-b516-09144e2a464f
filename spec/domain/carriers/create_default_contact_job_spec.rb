require 'rails_helper'

RSpec.describe Carriers::CreateDefaultContactJob do
  let(:company) { create :company }
  let(:carrier) { company.as_entity(:carrier) }

  describe '#perform' do
    it 'delegates to Carriers::CreateDefaultContact' do
      allow(Carriers::CreateDefaultContact).to receive(:call)
      described_class.perform_inline(carrier.id)
      expect(Carriers::CreateDefaultContact).to have_received(:call).with(carrier)
    end
  end
end
