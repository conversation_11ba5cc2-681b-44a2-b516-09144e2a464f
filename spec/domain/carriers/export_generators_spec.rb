require 'rails_helper'

RSpec.describe Carriers::ExportGenerators do
  describe '::for' do
    it 'returns xlsx generator' do
      expect(described_class.for('xlsx')).to eq Carriers::ExportGenerators::Xlsx
    end

    it 'returns csv generator' do
      expect(described_class.for('csv')).to eq Carriers::ExportGenerators::Csv
    end

    it 'raises error when type is not supported' do
      expect { described_class.for('txt') }.to raise_error ArgumentError, 'Export generator for txt not found'
    end
  end
end
