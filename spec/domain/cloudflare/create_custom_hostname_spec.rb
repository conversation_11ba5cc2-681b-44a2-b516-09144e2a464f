require 'rails_helper'

RSpec.describe Cloudflare::CreateCustomHostname do
  let(:hostname) { 'www.example.com' }

  before do
    stub_request(:post, 'https://api.cloudflare.com/client/v4/zones/czi-123456789/custom_hostnames')
      .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when the domain is created' do
      let(:status) { 201 }
      let(:body) { Rails.root.join('spec/fixtures/cloudflare/create_custom_hostname/success.json').read }

      it 'returns the response' do
        response = described_class.call(hostname:)
        expect(response).to eq JSON.parse(body)
      end
    end

    context 'when the domain is not created' do
      let(:status) { 422 }
      let(:body) { Rails.root.join('spec/fixtures/cloudflare/create_custom_hostname/failure.json').read }

      it 'raises an error' do
        expect { described_class.call(hostname:) }.to raise_error RuntimeError, 'Duplicate custom hostname found.'
      end
    end
  end
end
