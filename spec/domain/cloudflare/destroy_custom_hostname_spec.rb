require 'rails_helper'

RSpec.describe Cloudflare::DestroyCustomHostname do
  let(:identifier) { 'df9f9297-f17e-4fbf-afe6-7e016a23e2f3' }

  before do
    stub_request(:delete, "https://api.cloudflare.com/client/v4/zones/czi-123456789/custom_hostnames/#{identifier}")
      .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when the domain is deleted' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/cloudflare/destroy_custom_hostname/success.json').read }

      it 'returns the response' do
        response = described_class.call(identifier:)
        expect(response).to eq JSON.parse(body)
      end
    end

    context 'when the domain is not deleted' do
      let(:status) { 404 }
      let(:body) { Rails.root.join('spec/fixtures/cloudflare/destroy_custom_hostname/failure.json').read }

      it 'raises an error' do
        expect { described_class.call(identifier:) }.to raise_error RuntimeError, 'The custom hostname was not found.'
      end
    end
  end
end
