require 'rails_helper'

RSpec.describe Widgets::StoreImage do
  subject(:storage) { described_class.new(widget) }

  let(:widget) { create :widget, :carrier, widget_type: 'collect_review', style: 'gradient', enabled: true }
  let(:response) { instance_double(HTTP::Response, body:) }
  let(:body) { instance_double(HTTP::Response::Body, to_s: 'image') }

  before do
    allow(Widgets::GenerateImage).to receive(:call).and_return(response)
  end

  describe '#call' do
    it 'attaches image' do
      storage.call
      expect(widget.reload.image).to be_attached
    end

    it 'sets image style' do
      storage.call
      expect(widget.reload.image_style).to eq 'gradient'
    end

    it 'does not re-queue job' do
      storage.call
      expect(Widgets::StoreImageJob.jobs.size).to eq 0
    end
  end
end
