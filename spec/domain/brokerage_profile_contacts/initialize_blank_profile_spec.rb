require 'rails_helper'

RSpec.describe BrokerageProfileContacts::InitializeBlankProfile do
  subject(:callable) { described_class.new(brokerage) }

  describe '#call' do
    let(:brokerage_profile) { create :brokerage_profile, company: }
    let(:company) { create :company, :broker }
    let(:brokerage) { company.as_entity(:broker) }
    let(:email) { Faker::Internet.email }

    context 'when the brokerage profile is nil' do
      it 'does not create a contact' do
        expect { callable.call }.not_to change(BrokerageProfileContact, :count)
      end
    end

    context 'when the brokerage profile has no contacts' do
      before { brokerage_profile }

      context 'when the company has an email' do
        it 'creates a contact with the company email' do
          expect { callable.call }.to change(BrokerageProfileContact, :count).by(1)
        end
      end

      context 'when the company has no email' do
        let(:company) { create :company, :broker, email: nil }

        it 'does not create a contact' do
          expect { callable.call }.not_to change(BrokerageProfileContact, :count)
        end
      end
    end

    context 'when the brokerage profile has contacts' do
      before do
        create :brokerage_profile_contact, brokerage_profile:
      end

      it 'does not create a contact' do
        expect { callable.call }.not_to change(BrokerageProfileContact, :count)
      end
    end
  end
end
