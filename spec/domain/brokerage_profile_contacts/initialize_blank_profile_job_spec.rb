require 'rails_helper'

RSpec.describe BrokerageProfileContacts::InitializeBlankProfileJob do
  describe '#perform' do
    let(:company) { create :company, :broker }
    let(:brokerage) { company.as_entity(:broker) }

    it 'calls InitializeBlankProfile' do
      allow(BrokerageProfileContacts::InitializeBlankProfile).to receive(:call)
      described_class.perform_inline(company.id)
      expect(BrokerageProfileContacts::InitializeBlankProfile).to have_received(:call).with(brokerage)
    end
  end
end
