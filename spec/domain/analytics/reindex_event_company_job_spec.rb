require 'rails_helper'

RSpec.describe Analytics::ReindexEventCompanyJob, :elasticsearch do
  let(:visit_one) { create :analytics_visit, :completed, company: analytics_company_one, country: 'US' }
  let(:visit_two) { create :analytics_visit, :completed, company: analytics_company_two, country: 'US' }
  let(:analytics_company_one) { create :analytics_company }
  let(:analytics_company_two) { create :analytics_company }
  let(:company) { create :company, :carrier }
  let(:city) { create :city, name: 'New York', state_code: 'NY', country_code: 'US' }

  let(:index_name) do
    Analytics::Event.es.client.indices.get_data_stream(name: 'analytics_events_test')
      .dig('data_streams', 0, 'indices', 0, 'index_name')
  end

  let(:ids) { [analytics_company_two.id] }

  before do
    create :analytics_event,
           visit: visit_one, name: 'Page Viewed',
           properties: { id: company.id, entity_type: 'carrier', route: 'carriers#show' }

    2.times do
      create :analytics_event,
             visit: visit_two, name: 'Page Viewed',
             properties: { id: company.id, entity_type: 'carrier', route: 'carriers#show' }
    end

    Analytics::Events::CreateShipperEventJob.drain
    Elastic::Analytics::Events::BulkImport.call

    [analytics_company_one, analytics_company_two].each { |company| company.update!(city:) }
  end

  describe '#perform' do
    context 'when there is a conflict in seq_no and primary_term' do
      it 'requeues job with correct arguments' do
        described_class.load(index: index_name, ids:, per: 1)
        event_id, index, doc_id, seq_no, primary_term = described_class.jobs.pick('args')
        described_class.clear
        described_class.perform_inline(event_id, index, doc_id, seq_no + 1, primary_term)
        expect(described_class.jobs.pick('args')).to eq [event_id, index, doc_id, seq_no, primary_term]
      end
    end
  end

  describe '#on_success' do
    context 'when batch is invalidated' do
      it 'does not queue up next job' do
        batch = described_class.load(index: index_name, ids:, per: 1)
        described_class.drain
        batch.invalidate_all
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(described_class.jobs.size).to eq 0
      end
    end

    context 'when batch is valid' do
      it 'queues up next job' do
        described_class.load(index: index_name, ids:, per: 1)
        described_class.drain
        Analytics::Event.es.client.indices.refresh(index: index_name)
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(described_class.jobs.size).to eq 1
      end
    end
  end

  describe '::load' do
    it 'queues up jobs to reindex event' do
      expect do
        described_class.load(index: index_name, ids:, per: 1)
      end.to change(described_class.jobs, :size).by(1)
    end

    context 'when there are more pages left to process' do
      it 'sets the success callback' do
        batch = described_class.load(index: index_name, ids:, per: 1)
        expect(batch.callbacks['success']).to(
          contain_exactly(described_class => { 'index' => index_name, 'ids' => ids, 'per' => 1 })
        )
      end
    end

    context 'when processing the last page of results' do
      it 'does not create a batch' do
        described_class.load(index: index_name, ids:, per: 2)
        described_class.drain
        Analytics::Event.es.client.indices.refresh(index: index_name)
        batch = described_class.load(index: index_name, ids:, per: 2)
        expect(batch).to be_nil
      end
    end
  end
end
