require 'rails_helper'

RSpec.describe Analytics::Visits::SetCompanyJob do
  let(:visit) { create :analytics_visit, :geolocated }

  describe '#perform' do
    it 'delegates to the service object' do
      allow(Analytics::Visits::SetCompany).to receive(:call)
      described_class.perform_inline(visit.id)
      expect(Analytics::Visits::SetCompany).to have_received(:call).with(visit)
    end
  end
end
