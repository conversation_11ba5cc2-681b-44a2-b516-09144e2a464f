require 'rails_helper'

RSpec.describe Analytics::Visits::SetCompany do
  subject(:callable) { described_class.new(visit) }

  describe '#call' do
    let(:visit) { create :analytics_visit, :geolocated }

    let(:snitcher_data) { JSON.parse(Rails.root.join('spec/fixtures/snitcher/company/find/identified.json').read) }

    before do
      allow(Snitcher::FindCompany).to receive(:call).with(ip: visit.ip).and_return(snitcher_data)
    end

    context 'with snitcher provider' do
      before do
        stub_const('Analytics::Visits::SetCompany::PROVIDERS', ['snitcher'])
      end

      it 'creates company' do
        callable.call
        expect(Analytics::Company.last).to have_attributes provider: 'snitcher', name: 'Circle Logistics, Inc',
                                                           domain: 'circledelivers.com'
      end

      it 'updates the visit with the company' do
        callable.call
        expect(visit.reload).to have_attributes company: an_instance_of(Analytics::Company),
                                                company_status: 'completed'
      end

      context 'when visit has no location' do
        let(:visit) { create :analytics_visit, country: 'US' }

        it 'sets visit location data' do
          callable.call
          expect(visit.reload).to have_attributes city: 'Portage', region: 'Indiana', country: 'US'
        end
      end

      context 'when visit has location' do
        let(:visit) { create :analytics_visit, city: 'Miami', region: 'Florida', country: 'US' }

        it 'does not set visit location data' do
          callable.call
          expect(visit.reload).to have_attributes city: 'Miami', region: 'Florida', country: 'US'
        end
      end
    end

    context 'when company match is not found' do
      let(:snitcher_data) { nil }

      it 'does not update the visit' do
        callable.call
        expect(visit.reload).to have_attributes company: nil, company_status: 'completed'
      end
    end
  end
end
