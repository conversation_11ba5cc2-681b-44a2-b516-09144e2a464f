require 'rails_helper'

RSpec.describe Analytics::Companies::EmployeeRange do
  describe '#as_text' do
    shared_examples 'a valid employee range' do |range, text|
      it "returns '#{text}' for #{range}" do
        expect(described_class.new(range).as_text).to eq(text)
      end
    end

    it_behaves_like 'a valid employee range', ..1, '1'
    it_behaves_like 'a valid employee range', 1..1, '1'
    it_behaves_like 'a valid employee range', 1...2, '1'
    it_behaves_like 'a valid employee range', 2...11, '2-10'
    it_behaves_like 'a valid employee range', 11...51, '11-50'
    it_behaves_like 'a valid employee range', 51...201, '51-200'
    it_behaves_like 'a valid employee range', 201...501, '201-500'
    it_behaves_like 'a valid employee range', 501...1001, '501-1000'
    it_behaves_like 'a valid employee range', 1001...5001, '1001-5000'
    it_behaves_like 'a valid employee range', 5001...10_001, '5001-10000'
    it_behaves_like 'a valid employee range', 10_001..., '10001+'
    it_behaves_like 'a valid employee range', 10_001...Float::INFINITY, '10001+'
  end
end
