require 'rails_helper'

RSpec.describe Analytics::Companies::Serialize do
  subject(:serializer) { described_class.new(company) }

  describe '#call' do
    let(:city) do
      create :city, name: 'Chicago', state_code: 'IL', country_code: 'US', latitude: 41.8781, longitude: -87.6298
    end

    let(:company) { create :analytics_company, city: }

    it 'returns a hash with the company attributes' do
      expect(serializer.call).to eq(
        id: company.id,
        size: '51-200',
        name: company.name,
        industry_id: analytics_industries(:internet).id,
        city_id: city.id,
        location: { lat: 41.8781, lon: -87.6298 },
        state: 'united-states:illinois',
        region: 'united-states:midwest',
        country: 'united-states'
      )
    end

    context 'when city is nil' do
      let(:city) { nil }

      it 'returns a hash with the company attributes' do
        expect(serializer.call).to eq(
          id: company.id,
          size: '51-200',
          name: company.name,
          industry_id: analytics_industries(:internet).id
        )
      end
    end

    context 'when employees_range is nil' do
      let(:company) { create :analytics_company, employees_range: nil, city: }

      it 'returns a hash with the company attributes' do
        expect(serializer.call).to eq(
          id: company.id,
          name: company.name,
          industry_id: analytics_industries(:internet).id,
          city_id: city.id,
          location: { lat: 41.8781, lon: -87.6298 },
          state: 'united-states:illinois',
          region: 'united-states:midwest',
          country: 'united-states'
        )
      end
    end
  end
end
