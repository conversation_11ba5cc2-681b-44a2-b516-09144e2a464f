require 'rails_helper'

RSpec.describe Analytics::Companies::Providers::Snitcher do
  subject(:provider) { described_class.new(ip: Faker::Internet.ip_v4_address) }

  let(:data) do
    Rails.root.join('spec/fixtures/snitcher/company/find/identified.json').read.then { |json| JSON.parse(json) }
  end

  let!(:city) { create :city, name: 'Fort Wayne', state_code: 'IN', country_code: 'US' }

  before do
    allow(Snitcher::FindCompany).to receive(:call).with(ip: provider.ip).and_return(data)
  end

  describe '#analytics_company' do
    it 'upserts the company' do
      expect { provider.analytics_company }.to change(Analytics::Company, :count).by(1)
    end

    it 'returns the company' do
      expect(provider.analytics_company).to be_a(Analytics::Company)
    end

    it 'sets the attributes' do
      expect(provider.analytics_company).to(
        have_attributes(name: 'Circle Logistics, Inc', city:,
                        employees_range: 501..1000, domain: 'circledelivers.com',
                        twitter: 'https://twitter.com/drive4circle',
                        facebook: 'https://www.facebook.com/drive4circle',
                        linkedin: 'https://linkedin.com/companies/circle-logistics-inc',
                        industry: analytics_industries(:logistics_and_supply_chain),
                        revenue_amount: 0)
      )
    end

    context 'when the company already exists' do
      before do
        create :analytics_company, :snitcher, domain: 'circledelivers.com', employees_range: 1..1
      end

      it 'does not create a new company' do
        expect { provider.analytics_company }.not_to change(Analytics::Company, :count)
      end

      it 'updates the company' do
        expect(provider.analytics_company).to have_attributes name: 'Circle Logistics, Inc', employees_range: 501..1000
      end
    end
  end
end
