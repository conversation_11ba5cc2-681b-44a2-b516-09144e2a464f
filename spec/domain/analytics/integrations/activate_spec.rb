require 'rails_helper'

RSpec.describe Analytics::Integrations::Activate do
  let(:integration) { create :analytics_integration, :hubspot, :carrier }

  describe 'provider registry' do
    it 'registers a job for all enabled providers' do
      Analytics::Integration::ENABLED_PROVIDERS.each do |provider|
        expect(described_class.resolve(provider)).to respond_to(:perform_async)
      end
    end
  end

  describe '#on_success' do
    context 'when status is invalidated' do
      it 'does not activate integration' do
        batch = described_class.queue_job(integration)
        batch.invalidate_all
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(integration.reload).not_to be_active
      end
    end

    context 'when status is not invalidated' do
      it 'activates integration' do
        described_class.queue_job(integration)
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(integration.reload).to be_active
      end

      context 'when integration is already active' do
        it 'does not re-activate integration' do
          described_class.queue_job(integration)
          integration.activate!
          Sidekiq::TestBatch.execute_callbacks(:success)
          expect(integration.reload).to be_active
        end
      end
    end
  end

  describe '::queue_job' do
    context 'when integration may activate' do
      it 'queues a job to activate integration' do
        expect(described_class.queue_job(integration)).to be_a Sidekiq::Batch
      end
    end

    context 'when integration may not activate' do
      it 'does not queue a job to activate integration' do
        integration.activate!
        expect(described_class.queue_job(integration)).to be_nil
      end
    end
  end
end
