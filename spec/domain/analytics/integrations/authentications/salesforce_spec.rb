require 'rails_helper'

RSpec.describe Analytics::Integrations::Authentications::Salesforce do
  subject(:authentication) { described_class.new(integration) }

  let!(:integration) { create :analytics_integration, :salesforce, :carrier }

  describe '#access_token' do
    let(:body) { Rails.root.join('spec/fixtures/salesforce/oauth/token/refresh.json').read }

    let!(:request_stub) do
      stub_request(:post, 'https://test.salesforce.com/services/oauth2/token')
        .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when the token is expired' do
      it 'refreshes the token' do
        travel 1.day do
          expect(authentication.access_token).to eq '00Daj00000I5nDp!AQEAQPyGqlfLConEgK8_Zum8_rkKDlKCh3M5lArg'
          expect(authentication.refresh_token).to eq '1e8fbfb1-8e96-4826-8b8d-c8af73715'
          expect(request_stub).to have_been_requested
        end
      end
    end

    context 'when the token is not expired' do
      it 'does not refresh the token' do
        expect(authentication.access_token).to(
          eq('CIrToaiiMhIHAAEAQAAAARiO1ooBIOP0sgEokuLtAEaOaTFnToZ3VjUbtl46MAAAAEAAAAAgAAAAAAAAAAAA')
        )
        expect(request_stub).not_to have_been_requested
      end
    end
  end
end
