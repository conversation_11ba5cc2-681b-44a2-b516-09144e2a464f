require 'rails_helper'

RSpec.describe Analytics::Integrations::Authentications::Teams do
  subject(:authentication) { described_class.new(integration) }

  let!(:integration) { create :analytics_integration, :teams, :carrier }

  describe '#access_token' do
    let(:body) { Rails.root.join('spec/fixtures/microsoft/oauth/token/refresh.json').read }

    let!(:request_stub) do
      stub_request(:post, 'https://login.microsoftonline.com/common/oauth2/v2.0/token')
        .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when the token is expired' do
      it 'refreshes the token' do
        travel 1.day do
          expect(authentication.access_token).to eq 'eyJ0eXAiOiJKV1QiLCJhbGciOi...'
          expect(authentication.refresh_token).to eq '0.AAAA...newRefreshToken...'
          expect(request_stub).to have_been_requested
        end
      end
    end

    context 'when the token is not expired' do
      it 'does not refresh the token' do
        expect(authentication.access_token).to(
          eq('eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IjVCM25SeHRRN2kwYm')
        )
        expect(request_stub).not_to have_been_requested
      end
    end
  end
end
