require 'rails_helper'

RSpec.describe Analytics::Integrations::QueueNotificationsJob do
  describe '#perform' do
    let(:event_id) { 1 }
    let!(:notification) { create :analytics_company_event_feed_notification, :slack }
    let(:integration) { notification.integration }

    it 'queues a job for each notification' do
      described_class.perform_inline(event_id)
      expect(Analytics::Integrations::QueueNotificationJob.jobs.pick('args')).to eq([integration.id, event_id])
    end
  end
end
