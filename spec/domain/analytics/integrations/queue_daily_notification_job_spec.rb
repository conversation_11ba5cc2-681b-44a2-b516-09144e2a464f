require 'rails_helper'

RSpec.describe Analytics::Integrations::QueueDailyNotificationJob do
  describe '#perform' do
    let(:event) { create :analytics_aggregate_shipper_event }
    let(:notification) { create :analytics_company_event_feed_notification, :salesforce, interval: 'daily' }

    it 'queues the daily notification' do
      described_class.perform_inline(event.id, notification.id)
      expect(Salesforce::SyncEventToIntegrationJob.jobs.pick('args')).to(
        eq([notification.to_gid.to_s, event.to_gid.to_s])
      )
    end
  end
end
