require 'rails_helper'

RSpec.describe Analytics::Integrations::QueueDailyNotificationsJob do
  describe '::load' do
    let!(:hubspot_notification) { create :analytics_company_event_feed_notification, :hubspot, interval: 'daily' }
    let!(:salesforce_notification) { create :analytics_company_event_feed_notification, :salesforce, interval: 'daily' }

    before do
      create :analytics_company_event_feed_notification, :salesforce, interval: 'immediate'
    end

    it 'queues daily notifications for all active integrations' do
      described_class.load(date: Date.parse('2025-03-04'))
      expect(described_class.jobs.pluck('args')).to(
        contain_exactly([hubspot_notification.id, '2025-03-04'], [salesforce_notification.id, '2025-03-04'])
      )
    end
  end

  describe '#perform', :elasticsearch do
    let(:feed) { create :analytics_company_event_feed, :carrier, filters: Forms::AnalyticsEvent.new(search: true).to_h }
    let(:notification) { create :analytics_company_event_feed_notification, :salesforce, feed:, interval: 'daily' }
    let(:date) { Time.zone.today.to_s }

    context 'when event ids are found' do
      let!(:event) { create :analytics_aggregate_shipper_event }

      before do
        Analytics::AggregateShipperEvent.es.import force: true, refresh: true
      end

      it 'sets up a key in redis with the event ids and notification id' do
        described_class.perform_inline(notification.id, date)
        key = format(described_class::KEY_TEMPLATE, date:, integration_id: notification.integration_id)
        expect(CarrierSource.redis.call('HGETALL', key)).to eq(event.id.to_s => notification.id.to_s)
      end
    end

    context 'when no event ids are found' do
      before do
        Analytics::AggregateShipperEvent.es.import force: true, refresh: true
      end

      it 'does not set up a key in redis' do
        described_class.perform_inline(notification.id, date)
        key = format(described_class::KEY_TEMPLATE, date:, integration_id: notification.integration_id)
        expect(CarrierSource.redis.call('HGETALL', key)).to be_empty
      end
    end
  end

  describe '#on_success', :elasticsearch do
    let(:feed) { create :analytics_company_event_feed, :carrier, filters: Forms::AnalyticsEvent.new(search: true).to_h }
    let!(:notification) { create :analytics_company_event_feed_notification, :salesforce, feed:, interval: 'daily' }
    let(:date) { Time.zone.today.to_s }

    context 'when there are entries in the key' do
      let!(:event) { create :analytics_aggregate_shipper_event }

      before do
        Analytics::AggregateShipperEvent.es.import force: true, refresh: true
      end

      it 'queues the daily notifications' do
        described_class.load(date:)
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(Analytics::Integrations::QueueDailyNotificationJob.jobs.pick('args')).to eq([event.id, notification.id])
      end
    end

    context 'when there are no entries in the key' do
      before do
        Analytics::AggregateShipperEvent.es.import force: true, refresh: true
      end

      it 'does not queue the daily notifications' do
        described_class.load(date:)
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks(:success)
        expect(Analytics::Integrations::QueueDailyNotificationJob.jobs).to be_empty
      end
    end
  end
end
