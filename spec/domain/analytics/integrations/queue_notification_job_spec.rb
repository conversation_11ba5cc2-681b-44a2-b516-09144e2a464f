require 'rails_helper'

RSpec.describe Analytics::Integrations::QueueNotificationJob do
  describe '#perform' do
    let(:notification) { create :analytics_company_event_feed_notification, :slack, integration: }
    let(:event) { create :analytics_event, name: 'Page Viewed' }

    context 'when integration is active' do
      let(:integration) { create :analytics_integration, :active, :slack, :carrier }

      it 'delegates' do
        allow(Analytics::Integrations::QueueNotification).to receive(:call)
        described_class.perform_inline(integration.id, event.id)
        expect(Analytics::Integrations::QueueNotification).to have_received(:call).with(integration, event)
      end
    end

    context 'when integration is not active' do
      let(:integration) { create :analytics_integration, :slack, :carrier }

      it 'schedules a job' do
        described_class.perform_inline(integration.id, event.id)
        expect(described_class.jobs.pick('args')).to eq([integration.id, event.id])
      end
    end
  end
end
