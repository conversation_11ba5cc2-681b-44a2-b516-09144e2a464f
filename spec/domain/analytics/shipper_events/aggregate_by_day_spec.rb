require 'rails_helper'

RSpec.describe Analytics::ShipperEvents::AggregateByDay do
  subject(:callable) { described_class.new(date: Time.zone.today) }

  let(:company) { create :company, :carrier, city: }
  let(:city) { create :city, name: 'Chicago', state_code: 'IL', country_code: 'US' }
  let(:event) { create :analytics_shipper_event, company:, city: }

  before do
    event.dup.save
  end

  describe '#call' do
    it 'inserts aggregated data' do
      expect { callable.call }.to change(Analytics::AggregateShipperEvent, :count).by(1)
    end

    it 'inserts aggregated data with correct values' do
      callable.call

      expect(Analytics::AggregateShipperEvent.last).to(
        have_attributes(
          interval: 'day', interval_date: Time.zone.today, type: 'carrier.search.performed', visit_id: event.visit_id,
          analytics_company_id: event.analytics_company_id, company_id: company.id, company_entity_type: 'carrier',
          search_city_id: city.id, search_state_id: 'united-states:illinois',
          search_region_id: 'united-states:midwest', search_country_id: 'united-states', event_count: 2,
          search_freight_ids: [], search_truck_type_ids: [], search_shipment_type_ids: [],
          search_specialized_service_ids: []
        )
      )
    end
  end
end
