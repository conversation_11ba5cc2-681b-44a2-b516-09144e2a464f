require 'rails_helper'

RSpec.describe Analytics::DateR<PERSON>e do
  describe '#label' do
    subject { described_class.new(type, value).label }

    context 'when type is last_7_days' do
      let(:type) { 'last_7_days' }
      let(:value) { nil }

      it { is_expected.to eq('Last 7 days') }
    end

    context 'when type is last_30_days' do
      let(:type) { 'last_30_days' }
      let(:value) { nil }

      it { is_expected.to eq('Last 30 days') }
    end

    context 'when type is custom' do
      let(:type) { 'custom' }
      let(:value) { { gte: Date.new(2021, 1, 1), lte: Date.new(2021, 1, 31) } }

      it { is_expected.to eq('January 01, 2021 - January 31, 2021') }
    end
  end

  describe '#as_hash' do
    subject { described_class.new(type, value).as_hash }

    context 'when type is last_7_days' do
      let(:type) { 'last_7_days' }
      let(:value) { nil }

      it { is_expected.to eq(gte: 7.days.ago.beginning_of_day) }
    end

    context 'when type is last_30_days' do
      let(:type) { 'last_30_days' }
      let(:value) { nil }

      it { is_expected.to eq(gte: 30.days.ago.beginning_of_day) }
    end

    context 'when type is custom' do
      let(:type) { 'custom' }
      let(:value) { { gte: Date.new(2021, 1, 1), lte: Date.new(2021, 1, 31) } }

      it { is_expected.to eq(gte: Date.new(2021, 1, 1).beginning_of_day, lte: Date.new(2021, 1, 31).end_of_day) }
    end
  end
end
