require 'rails_helper'

RSpec.describe Analytics::CompanyEventFeeds::UpdateCarrierDefaultJob do
  describe '#perform' do
    let(:company) { create :company, :carrier }
    let(:carrier) { company.as_entity(:carrier) }

    it 'calls UpdateCarrierDefault with the carrier' do
      allow(Analytics::CompanyEventFeeds::UpdateCarrierDefault).to receive(:call).and_call_original
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(Analytics::CompanyEventFeeds::UpdateCarrierDefault).to have_received(:call).with(carrier)
    end
  end
end
