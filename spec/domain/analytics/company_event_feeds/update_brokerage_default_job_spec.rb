require 'rails_helper'

RSpec.describe Analytics::CompanyEventFeeds::UpdateBrokerageDefaultJob do
  describe '#perform' do
    let(:company) { create :company, :broker }
    let(:brokerage) { company.as_entity(:broker) }

    it 'calls UpdateBrokerageDefault with the brokerage' do
      allow(Analytics::CompanyEventFeeds::UpdateBrokerageDefault).to receive(:call).and_call_original
      described_class.perform_inline(brokerage.to_gid.to_s)
      expect(Analytics::CompanyEventFeeds::UpdateBrokerageDefault).to have_received(:call).with(brokerage)
    end
  end
end
