require 'rails_helper'

RSpec.describe Analytics::ContactChartData do
  subject(:chart) { described_class.new(company:, **options) }

  let(:company) { create :company }
  let(:options) { { period: 'days', from: 3.days.ago, to: Time.zone.today } }

  before do
    travel_to Date.new(2024, 2, 9)

    %w(analytics_events analytics_visits).each do |table|
      Analytics::PartitionByMonth.premake(table:, months: 1)
    end
  end

  describe '#labels' do
    it 'returns an array of formatted dates' do
      expect(chart.labels).to eq %w(2/6/24 2/7/24 2/8/24 2/9/24)
    end
  end

  describe '#data' do
    before do
      create :analytics_event, name: 'Carrier Contact Viewed', properties: { id: company.id }, time: 2.days.ago
      create :analytics_event, name: 'Carrier Contact Viewed', properties: { id: company.id }, time: 1.day.ago

      Rollups.generate
    end

    it 'returns an array of contact info views' do
      expect(chart.data).to eq [0, 1, 1, 0]
    end
  end

  describe '#total' do
    before do
      create :analytics_event, name: 'Carrier Contact Viewed', properties: { id: company.id }, time: 2.days.ago
      create :analytics_event, name: 'Carrier Contact Viewed', properties: { id: company.id }, time: 1.day.ago

      Rollups.generate
    end

    it 'returns the total contact info views' do
      expect(chart.total).to eq 2
    end
  end
end
