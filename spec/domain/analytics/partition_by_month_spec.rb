require 'rails_helper'

RSpec.describe Analytics::PartitionByMonth do
  let(:table) { 'analytics_events' }

  describe '::premake' do
    it 'creates partitions for the given table' do
      travel_to Time.zone.local(2022, 2, 8) do
        described_class.premake(table:, months: 2)
        expect(ApplicationRecord.connection.table_exists?('analytics_events_2022_02')).to be true
        expect(ApplicationRecord.connection.table_exists?('analytics_events_2022_03')).to be true
      end
    end
  end
end
