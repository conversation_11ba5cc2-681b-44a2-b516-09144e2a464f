require 'rails_helper'

RSpec.describe Analytics::ExportEventFeed, :elasticsearch do
  subject(:exporter) { described_class.new(feed) }

  let(:feed) { create :analytics_event_feed, filters: }
  let(:visit) { create :analytics_visit, :completed, :geolocated }
  let(:carrier_profile) { create :carrier_profile }
  let(:carrier) { carrier_profile.carrier }

  let(:filters) do
    Forms::AnalyticsEvent.new(
      target_company_id: carrier.id, target_company_entity_type: 'carrier', direct_signals: 'carrier.profile.viewed',
      search: true
    ).to_h
  end

  before do
    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { id: carrier.id, entity_type: 'carrier', route: 'carriers#show',
                         url: Routes.carrier_url(carrier) }

    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { route: 'trucking_companies/locations#city',
                         url: Routes.city_trucking_companies_url(*carrier.city.path) }

    Analytics::Events::CreateShipperEventJob.drain
    Elastic::Analytics::Events::BulkImport.call
  end

  describe '#call' do
    it 'creates CSV export' do
      expect { exporter.call }.to change { feed.reload.files.size }.by(1)
    end
  end
end
