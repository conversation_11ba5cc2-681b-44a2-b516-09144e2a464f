require 'rails_helper'

RSpec.describe Analytics::Events::AddToElasticIndexJob, :elasticsearch do
  describe '#perform' do
    let(:event) { create :analytics_event, visit:, name: 'Page Viewed', properties: { id: 1, route: 'carriers#show' } }
    let(:visit) { create :analytics_visit, :geolocated, :completed }

    before do
      Analytics::Events::CreateShipperEvent.call(event)
    end

    it 'indexes the event' do
      described_class.perform_one
      Analytics::Event.es.client.indices.refresh(index: Analytics::Event.index_name)
      expect(Analytics::Event.es.search('*').results.map(&:analytics_company).map(&:id)).to eq [visit.company_id]
    end

    context 'when batch is invalidated' do
      before do
        Sidekiq::Batch.new(described_class.jobs.first['bid']).invalidate_all
      end

      it 'does not index the event' do
        described_class.perform_one
        expect(Analytics::Event.es.client.indices.exists?(index: Analytics::Event.index_name)).to be false
      end
    end
  end
end
