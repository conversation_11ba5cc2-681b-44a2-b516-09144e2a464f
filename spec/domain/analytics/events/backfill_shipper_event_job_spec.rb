require 'rails_helper'

RSpec.describe Analytics::Events::BackfillShipperEventJob do
  describe '#perform' do
    let(:event) { create :analytics_event }

    it 'calls the backfill shipper event service' do
      allow(Analytics::Events::BackfillShipperEvent).to receive(:call).and_call_original
      described_class.perform_inline(event.id)
      expect(Analytics::Events::BackfillShipperEvent).to have_received(:call).with(event)
    end
  end

  describe '#on_success' do
    let(:visit) { create :analytics_visit, :geolocated, :completed }
    let!(:event_one) { create :analytics_event, visit:, properties: { route: 'carriers#show' } }
    let!(:event_two) { create :analytics_event, visit:, properties: { route: 'carriers#show' } }

    context 'with invalid batch' do
      it 'does not create a new batch' do
        batch = described_class.load(start_date: Time.zone.today, end_date: Time.zone.today, limit: 1)
        batch.invalidate_all
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks :success
        expect(described_class.jobs).to be_empty
      end
    end

    context 'with valid batch' do
      it 'creates a new batch' do
        described_class.load(start_date: Time.zone.today, end_date: Time.zone.today, limit: 1)
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks :success
        expect(described_class.jobs.pick('args')).to eq [[event_one.id, event_two.id].max]
      end
    end
  end

  describe '::load' do
    context 'when start date is greater than end date' do
      it 'does not create a batch' do
        batch = described_class.load(start_date: Time.zone.today, end_date: Date.yesterday)
        expect(batch).to be_nil
      end
    end

    context 'when relation is empty' do
      it 'calls load with the next day' do
        described_class.load(start_date: Time.zone.today, end_date: Time.zone.today)
        expect(described_class.jobs.size).to eq 0
      end
    end

    context 'when relation is not empty' do
      let(:visit) { create :analytics_visit, :geolocated, :completed }
      let!(:event_one) { create :analytics_event, visit: }
      let!(:event_two) { create :analytics_event, visit: }

      before do
        create :analytics_shipper_event
      end

      it 'creates a batch' do
        described_class.load(start_date: Time.zone.today, end_date: Time.zone.today)
        expect(described_class.jobs.pluck('args')).to eq [[event_one.id], [event_two.id]]
      end
    end
  end
end
