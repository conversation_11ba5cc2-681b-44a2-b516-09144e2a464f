require 'rails_helper'

RSpec.describe Analytics::Events::Signals::CompanyEvent do
  subject(:company_event) { described_class.new(company, event) }

  let!(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, :carrier, legal_name: 'Mone Transport LLC' }
  let(:analytics_company) { create :analytics_company, domain: 'costco.com', name: 'Costco Wholesale' }
  let(:visit) { create :analytics_visit, :completed, company: analytics_company }

  describe '#signal' do
    context 'when company is the target company' do
      let(:event) do
        create :analytics_event,
               name: 'Page Viewed', visit:,
               properties: { route: 'carriers#show', url: Routes.carrier_url(company), id: company.id }
      end

      it 'returns direct signal' do
        expect(company_event.signal).to have_attributes id: 'carrier.profile.viewed', type: :direct
      end
    end

    context 'when company is not the target company' do
      let!(:new_carrier_profile) { create :carrier_profile, company: new_company }
      let(:new_company) { create :company, :carrier, legal_name: 'Transit Core LLC' }

      let(:event) do
        create :analytics_event,
               name: 'Page Viewed', visit:,
               properties: { route: 'carriers#show', url: Routes.carrier_url(new_company), id: new_company.id }
      end

      it 'returns indirect signal' do
        expect(company_event.signal).to have_attributes id: 'carrier.profile.viewed', type: :indirect
      end
    end

    context 'when event type is not a company signal' do
      let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT', country_code: 'US' }

      let(:event) do
        create :analytics_event,
               name: 'Page Viewed', visit:,
               properties: { route: 'trucking_companies/locations#city',
                             url: Routes.city_trucking_companies_url(*city.path),
                             path_parameters: { city: 'salt-lake-city', state: 'utah', country: 'united-states' } }
      end

      it 'returns search signal' do
        expect(company_event.signal).to have_attributes id: 'carrier.search.performed'
      end
    end
  end
end
