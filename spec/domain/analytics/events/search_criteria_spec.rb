require 'rails_helper'

RSpec.describe Analytics::Events::SearchCriteria do
  describe '::attributes' do
    context 'when the route is lane_searches#show' do
      let(:origin) { create :city, name: 'Chicago', state_code: 'IL', country_code: 'US' }
      let(:destination) { create :city, name: 'New York', state_code: 'NY', country_code: 'US' }
      let(:form) { Forms::LaneSearch.new(truck_type: truck_types(:flatbed).id) }

      let(:url) do
        "#{Routes.lane_search_url(origin: origin.full_slug, destination: destination.full_slug)}?#{form.to_query}"
      end

      let(:event) do
        build_stubbed :analytics_event,
                      properties: {
                        'route' => 'lane_searches#show', 'url' => url,
                        'path_parameters' => { 'origin' => origin.full_slug, 'destination' => destination.full_slug }
                      }
      end

      it 'returns the correct attributes' do
        expect(described_class.attributes(event)).to eq(
          search_city_id: origin.id,
          search_state_id: 'united-states:illinois',
          search_region_id: 'united-states:midwest',
          search_country_id: 'united-states',
          search_destination_city_id: destination.id,
          search_destination_state_id: 'united-states:new-york',
          search_destination_region_id: 'united-states:northeast',
          search_destination_country_id: 'united-states',
          search_truck_type_ids: [truck_types(:flatbed).id]
        )
      end
    end

    context 'when the route is not lane_searches#show' do
      let(:city) { create :city, name: 'Chicago', state_code: 'IL', country_code: 'US' }
      let(:form) { Forms::Carrier.new(truck_type: truck_types(:flatbed).id) }
      let(:url) { "#{Routes.city_trucking_companies_url(*city.path)}?#{form.to_query}" }

      let(:event) do
        build_stubbed :analytics_event,
                      properties: {
                        'route' => 'trucking_companies/locations#city', 'url' => url,
                        'path_parameters' => {
                          'country' => 'united-states', 'state' => 'illinois', 'city' => 'chicago'
                        }
                      }
      end

      it 'returns the correct attributes' do
        expect(described_class.attributes(event)).to eq(
          search_city_id: city.id,
          search_state_id: 'united-states:illinois',
          search_region_id: 'united-states:midwest',
          search_country_id: 'united-states',
          search_truck_type_ids: [truck_types(:flatbed).id]
        )
      end
    end
  end
end
