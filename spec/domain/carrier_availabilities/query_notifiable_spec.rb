require 'rails_helper'

RSpec.describe CarrierAvailabilities::QueryNotifiable do
  subject(:notifiable) { described_class.new(date:) }

  let(:date) { Time.zone.today }

  describe '#call' do
    context 'when there are no availabilities' do
      it 'returns empty collection' do
        expect(notifiable.call).to eq []
      end
    end

    context 'when there are availabilities' do
      let(:access_package) { create :access_package, :carrier }
      let(:company) { access_package.resource.company }
      let!(:availabilities) { create_list(:carrier_availability, 2, company:, date:) }

      it 'returns availabilities' do
        expect(notifiable.call).to contain_exactly([company.id, availabilities.min_by(&:id).id, 2])
      end
    end
  end
end
