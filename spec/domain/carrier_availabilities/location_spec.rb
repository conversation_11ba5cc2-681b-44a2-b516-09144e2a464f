require 'rails_helper'

RSpec.describe CarrierAvailabilities::Location do
  subject(:location) { described_class.new(availability) }

  let(:availability) { build(:carrier_availability, **attributes) }

  describe '#origins' do
    context 'when the origin type is city' do
      let(:city) { build :city, name: 'Chicago', state_code: 'IL' }
      let(:attributes) { { origin_type: 'city', origin_cities: [city] } }

      it 'returns a list of cities' do
        expect(location.origins).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.origins.map(&:name)).to eq(['Chicago, IL'])
      end
    end

    context 'when the origin type is state' do
      let(:attributes) { { origin_type: 'state', origin_state_ids: %w(united-states:utah united-states:iowa) } }

      it 'returns a list of states' do
        expect(location.origins).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.origins.map(&:name)).to eq(%w(Utah Iowa))
      end
    end

    context 'when the origin type is region' do
      let(:attributes) { { origin_type: 'region', origin_region_ids: %w(united-states:west united-states:midwest) } }

      it 'returns a list of regions' do
        expect(location.origins).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.origins.map(&:name)).to eq(%w(West Midwest))
      end
    end

    context 'when the origin type is nil' do
      let(:attributes) { { origin_type: nil } }

      it 'returns a list of regions' do
        expect(location.origins).to be_empty
      end
    end
  end

  describe '#destinations' do
    context 'when the destination type is city' do
      let(:city) { build :city, name: 'Chicago', state_code: 'IL' }
      let(:attributes) { { destination_type: 'city', destination_cities: [city] } }

      it 'returns a list of cities' do
        expect(location.destinations).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.destinations.map(&:name)).to eq(['Chicago, IL'])
      end
    end

    context 'when the destination type is state' do
      let(:attributes) do
        { destination_type: 'state', destination_state_ids: %w(united-states:utah united-states:iowa) }
      end

      it 'returns a list of states' do
        expect(location.destinations).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.destinations.map(&:name)).to eq(%w(Utah Iowa))
      end
    end

    context 'when the destination type is region' do
      let(:attributes) do
        { destination_type: 'region', destination_region_ids: %w(united-states:west united-states:midwest) }
      end

      it 'returns a list of regions' do
        expect(location.destinations).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.destinations.map(&:name)).to eq(%w(West Midwest))
      end
    end

    context 'when the destination type is nil' do
      let(:attributes) { { destination_type: nil } }

      it 'returns a list of regions' do
        expect(location.destinations).to all(be_a(CarrierAvailabilities::Location::Record))
        expect(location.destinations.map(&:name)).to eq(['Anywhere'])
      end
    end
  end
end
