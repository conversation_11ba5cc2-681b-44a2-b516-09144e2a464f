require 'rails_helper'

RSpec.describe RequestForProposals::Entities::Carrier do
  let(:entity) { described_class.new(record) }
  let(:company) { create :company, power_units: }
  let(:record) { company.as_entity(:carrier) }

  describe '#meeting_url' do
    context 'when power units is greater than or equal to 21' do
      let(:power_units) { 50 }

      it 'returns the meeting url' do
        expect(entity.meeting_url).to eq('https://calendly.com/cavery-8em/30-minute-meeting')
      end
    end

    context 'when power units is less than 21' do
      let(:power_units) { 5 }

      it 'returns the meeting url' do
        expect(entity.meeting_url).to eq('https://calendly.com/claraflaherty/30min')
      end
    end
  end
end
