require 'rails_helper'

RSpec.describe Render::CreateCustomDomain do
  subject(:creator) { described_class.new(name:) }

  let(:name) { 'www.example.com' }

  before do
    stub_request(:post, 'https://api.render.com/v1/services/website-service-id/custom-domains')
      .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when the domain is created' do
      let(:status) { 201 }
      let(:body) { Rails.root.join('spec/fixtures/render/create_custom_domain/success.json').read }

      it 'returns the response' do
        response = creator.call
        expect(response).to eq JSON.parse(body).first
      end
    end

    context 'when the domain is not created' do
      let(:status) { 409 }
      let(:body) { Rails.root.join('spec/fixtures/render/create_custom_domain/failure.json').read }

      it 'raises an error' do
        expect { creator.call }.to raise_error RuntimeError
      end
    end
  end
end
