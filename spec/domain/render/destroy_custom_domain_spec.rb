require 'rails_helper'

RSpec.describe Render::DestroyCustomDomain do
  let(:identifier) { 'cdm-corr5vq1hbls73fa450g' }

  before do
    stub_request(:delete, "https://api.render.com/v1/services/website-service-id/custom-domains/#{identifier}")
      .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when the domain is destroyed' do
      let(:status) { 204 }
      let(:body) { '' }

      it 'returns true' do
        expect(described_class.call(identifier:)).to be true
      end
    end

    context 'when the domain is not destroyed' do
      let(:status) { 404 }
      let(:body) { Rails.root.join('spec/fixtures/render/destroy_custom_domain/failure.json').read }

      it 'raises an error' do
        expect { described_class.call(identifier:) }.to raise_error RuntimeError
      end
    end
  end
end
