require 'rails_helper'

RSpec.describe SponsoredContents::Enumerator do
  describe '::build' do
    let(:request) { ActionDispatch::Request.empty }
    let(:access) { instance_double Personas::DispatcherAccess }

    before do
      allow(Personas::DispatcherAccess).to receive(:new).and_return(access)
    end

    context 'when dispatcher access is allowed' do
      before do
        allow(access).to receive(:allowed?).and_return(true)
      end

      it 'returns an enumerator' do
        expect(described_class.build(request, namespace: 'sc')).to be_a described_class
      end
    end

    context 'when dispatcher access is not allowed' do
      before do
        allow(access).to receive(:allowed?).and_return(false)
      end

      it 'returns a null enumerator' do
        expect(described_class.build(request, namespace: 'sc')).to eq SponsoredContents::Enumerator::NULL.instance
      end
    end
  end
end
