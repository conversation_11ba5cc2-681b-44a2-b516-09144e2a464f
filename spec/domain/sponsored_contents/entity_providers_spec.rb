require 'rails_helper'

RSpec.describe SponsoredContents::EntityProviders do
  describe '::for' do
    it 'returns the Carrier class' do
      expect(described_class.for('carrier')).to eq(SponsoredContents::EntityProviders::Carrier)
    end

    it 'returns the Brokerage class' do
      expect(described_class.for('broker')).to eq(SponsoredContents::EntityProviders::Brokerage)
    end

    it 'raises an ArgumentError for an unknown entity type' do
      expect { described_class.for('unknown') }.to raise_error(ArgumentError, 'Unknown entity type: unknown')
    end
  end
end
