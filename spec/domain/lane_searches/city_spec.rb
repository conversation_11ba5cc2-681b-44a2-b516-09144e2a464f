require 'rails_helper'

RSpec.describe LaneSearches::City do
  subject(:lane_city) { described_class.new(full_slug) }

  let!(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }

  describe '#record' do
    context 'when state is found' do
      let(:full_slug) { 'united-states:utah:salt-lake-city' }

      it 'returns matching record' do
        expect(lane_city.record).to eq city
      end
    end

    context 'when state is not found' do
      let(:full_slug) { 'mexico:michoacan:riverview' }

      it 'raises error' do
        expect { lane_city.record }.to raise_error ActiveRecord::RecordNotFound
      end
    end
  end
end
