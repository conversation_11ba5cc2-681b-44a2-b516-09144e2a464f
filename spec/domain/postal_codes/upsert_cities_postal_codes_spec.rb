require 'rails_helper'

RSpec.describe PostalCodes::UpsertCitiesPostalCodes do
  shared_examples 'a city postal code upserter' do |expected|
    let(:object) { instance_double(Aws::S3::Object) }
    let(:response) { instance_double(Aws::S3::Types::GetObjectOutput, body: csv) }

    let(:key) { 'input.csv' }

    before do
      allow(Aws::S3::Object).to receive(:new).and_return(object)
      allow(object).to receive(:get).and_return(response)
    end

    describe '#call' do
      it 'creates the records' do
        expect { described_class.call(country_code:, key:) }.to change(CitiesPostalCode, :count).by(expected)
      end

      it 'updates existing records' do
        described_class.call(country_code:, key:)
        record = CitiesPostalCode.find_by(postal_code:)
        expect(record).not_to be_blank
      end
    end
  end

  it_behaves_like 'a city postal code upserter', 2 do
    let(:country_code) { 'US' }

    let(:csv) do
      <<~CSV
        "zip","lat","lng","city","state_id","state_name"
        "00501","40.81308","-73.04639","Holtsville","NY","New York"
        "00544","40.81322","-73.04929","Holtsville","NY","New York"
        "00601","18.18027","-66.75266","Adjuntas","PR","Puerto Rico"
        "00602","18.36075","-67.17541","Aguada","PR","Puerto Rico"
        "09002",,,"Apo","AE","Armed Forces Europe"
      CSV
    end

    let!(:city) { create :city, name: 'Holtsville', state_code: 'NY', country_code: }
    let(:postal_code) { PostalCode.find_by(code: '00501', country_code:) }

    before do
      PostalCodes::UpsertPostalCodes.call(country_code:, key:)

      create(:city, name: 'North Babylon', state_code: 'NY', country_code:).then do |city|
        create(:cities_postal_code, city:, postal_code:)
      end

      create(:city, name: 'Adjuntas', state_code: 'PR', country_code:)
    end
  end

  it_behaves_like 'a city postal code upserter', 2 do
    let(:country_code) { 'CA' }

    let(:csv) do
      <<~CSV
        PostalCode,City,Province,AreaCode,Latitude,Longitude,CityMixedCase,RecordType
        "A0A 0A0","BAY ROBERTS","NL","709","47.05564","-53.201979","Bay Roberts","3"
        "A0A 0A1","AVONDALE","NL","709","47.487036","-53.086027","Avondale","3"
        "A0A 0A2","HOLYROOD","NL","709","47.487036","-53.086027","Holyrood","3"
        "A0A 0A3","TREPASSEY","NL","709","47.487036","-53.086027","Trepassey","3"
        "A0A 0A3","TREPASSEY","NL","709","47.487036","-53.086027","Trepassey","5"
      CSV
    end

    let!(:city) { create :city, name: 'Avondale', state_code: 'NL', country_code: }
    let(:postal_code) { PostalCode.find_by(code: 'A0A 0A1', country_code:) }

    before do
      PostalCodes::UpsertPostalCodes.call(country_code:, key:)

      create(:city, name: 'Toronto', state_code: 'ON', country_code:).then do |city|
        create(:cities_postal_code, city:, postal_code:)
      end

      create(:city, name: 'Bay Roberts', state_code: 'NL', country_code:)
      create(:city, name: 'Trepassey', state_code: 'NL', country_code:)
    end
  end

  it_behaves_like 'a city postal code upserter', 2 do
    let(:country_code) { 'MX' }

    let(:csv) do
      <<~CSV
        "postal_code","city_name","state_code","latitude","longitude"
        "22000","Tijuana","BC","32.4248","-116.8983"
        "22010","Tijuana","BC","32.5431","-116.7812"
        "82007","Mazatlán","SI","23.4684","-106.306"
      CSV
    end

    let!(:city) { create :city, name: 'Tijuana', state_code: 'BC', country_code: }
    let(:postal_code) { PostalCode.find_by(code: '22000', country_code:) }

    before do
      PostalCodes::UpsertPostalCodes.call(country_code:, key:)

      create(:city, name: 'Guadalajara', state_code: 'JA', country_code:).then do |city|
        create(:cities_postal_code, city:, postal_code:)
      end

      create(:city, name: 'Mazatlán', state_code: 'SI', country_code:)
    end
  end
end
