require 'rails_helper'

RSpec.describe PostalCodes::UpsertPostalCodes do
  subject(:upserter) { described_class.new(country_code:, key: 'input.csv') }

  let(:object) { instance_double(Aws::S3::Object) }
  let(:response) { instance_double(Aws::S3::Types::GetObjectOutput, body: csv) }

  before do
    allow(Aws::S3::Object).to receive(:new).and_return(object)
    allow(object).to receive(:get).and_return(response)
  end

  describe '#call' do
    context 'with US postal codes' do
      let(:country_code) { 'US' }

      let(:csv) do
        <<~CSV
          "zip","lat","lng","city","state_id","state_name"
          "00501","40.81308","-73.04639","Holtsville","NY","New York"
          "00544","40.81322","-73.04929","Holtsville","NY","New York"
          "00601","18.18027","-66.75266","Adjuntas","PR","Puerto Rico"
          "00602","18.36075","-67.17541","Aguada","PR","Puerto Rico"
          "09002",,,"Apo","AE","Armed Forces Europe"
        CSV
      end

      it 'creates the records' do
        expect { upserter.call }.to change(PostalCode, :count).by(4)
      end
    end

    context 'with CA postal codes' do
      let(:country_code) { 'CA' }

      let(:csv) do
        <<~CSV
          PostalCode,City,Province,AreaCode,Latitude,Longitude,CityMixedCase,RecordType
          "A0A 0A0","BAY ROBERTS","NL","709","47.05564","-53.201979","Bay Roberts","3"
          "A0A 0A1","AVONDALE","NL","709","47.487036","-53.086027","Avondale","3"
          "A0A 0A2","HOLYROOD","NL","709","47.487036","-53.086027","Holyrood","3"
          "A0A 0A3","TREPASSEY","NL","709","47.487036","-53.086027","Trepassey","3"
          "A0A 0A3","TREPASSEY","NL","709","47.487036","-53.086027","Trepassey","5"
        CSV
      end

      it 'creates the records' do
        expect { upserter.call }.to change(PostalCode, :count).by(4)
      end
    end

    context 'with MX postal codes' do
      let(:country_code) { 'MX' }

      let(:csv) do
        <<~CSV
          "postal_code","city_name","state_code","latitude","longitude"
          "22000","Tijuana","BC","32.4248","-116.8983"
          "22010","Tijuana","BC","32.5431","-116.7812"
          "82007","Mazatlán","SI","23.4684","-106.306"
        CSV
      end

      it 'creates the records' do
        expect { upserter.call }.to change(PostalCode, :count).by(3)
      end
    end
  end
end
