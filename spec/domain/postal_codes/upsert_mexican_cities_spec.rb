require 'rails_helper'

RSpec.describe PostalCodes::UpsertMexicanCities do
  let(:csv) do
    <<~CSV
      "postal_code","city_name","state_code","latitude","longitude"
      "22000","Tijuana","BC","32.4248","-116.8983"
      "22010","Tijuana","BC","32.5431","-116.7812"
      "82007","Mazatlán","SI","23.4684","-106.306"
      "82010","","","23.4684","-106.306"
    CSV
  end

  let(:object) { instance_double(Aws::S3::Object) }
  let(:response) { instance_double(Aws::S3::Types::GetObjectOutput, body: csv) }
  let(:key) { 'mexico-postal-codes.csv' }

  before do
    allow(Aws::S3::Object).to receive(:new).and_return(object)
    allow(object).to receive(:get).and_return(response)
  end

  describe '#call' do
    let!(:mazatlan) { create :city, name: 'Mazatlan', state_code: 'SI', country_code: 'MX' }

    it 'creates new city records' do
      expect { described_class.call(key:) }.to change(City, :count).by(1)
    end

    it 'updates existing records' do
      described_class.call(key:)
      expect(mazatlan.reload).to have_attributes name: 'Mazatlán', latitude: 23.4684, longitude: -106.306
    end
  end
end
