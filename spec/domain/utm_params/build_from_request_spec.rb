require 'rails_helper'

RSpec.describe UtmParams::BuildFromRequest do
  subject(:builder) { described_class.new(request) }

  let(:env) { Rack::MockRequest.env_for(uri) }
  let(:request) { ActionDispatch::TestRequest.create(env) }
  let(:app) { proc { [200, {}, 'success'] } }

  before do
    Rack::Utm.new(app).call(env)
  end

  describe '#call' do
    context 'when request contains utm params' do
      let(:uri) { '/reviews/new?utm_source=facebook&utm_campaign=social' }

      it 'instantiates record' do
        expect(builder.call).to have_attributes source: 'facebook', campaign: 'social'
      end
    end

    context 'when request does not contain utm params' do
      let(:uri) { '/reviews/new' }

      it 'returns nil' do
        expect(builder.call).to be_nil
      end
    end
  end
end
