require 'rails_helper'

RSpec.describe Records::FallbackAttributeLookup do
  subject(:lookup) { described_class.new(:company_rep1, profile, company) }

  let(:company) { build_stubbed :company, company_rep1: '<PERSON>' }
  let(:profile) { build_stubbed :carrier_profile, company:, company_rep1: '<PERSON>' }

  describe '#value' do
    context 'when first record has a value for the attribute' do
      it 'returns the value' do
        expect(lookup.value).to eq '<PERSON>'
      end
    end

    context 'when first record does not have a value for the attribute' do
      let(:profile) { build_stubbed :carrier_profile, company:, company_rep1: nil }

      it 'returns the value from the second record' do
        expect(lookup.value).to eq '<PERSON>'
      end
    end

    context 'when neither record has a value for the attribute' do
      let(:company) { build_stubbed :company, company_rep1: nil }
      let(:profile) { build_stubbed :carrier_profile, company:, company_rep1: nil }

      it 'returns nil' do
        expect(lookup.value).to be_nil
      end
    end
  end
end
