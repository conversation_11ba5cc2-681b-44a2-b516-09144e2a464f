require 'rails_helper'

RSpec.describe Authentications::FindOrCreate do
  subject(:service) { described_class.new(OmniAuth::AuthHash.new(auth_hash), {}) }

  describe '#call' do
    context 'when authentication exists' do
      let!(:authentication) { create :authentication, :linkedin }
      let(:auth_hash) { Faker::Omniauth.linkedin(email: '<EMAIL>', uid: authentication.uid) }

      it 'does not create new authentication' do
        expect { service.call }.not_to change(Authentication, :count)
      end

      it 'updates email' do
        service.call
        expect(authentication.reload.email).to eq '<EMAIL>'
      end

      it 'verifies user' do
        service.call
        expect(authentication.user).to be_verified
      end
    end

    context 'when authentication does not exist' do
      let(:auth_hash) { Faker::Omniauth.linkedin }

      it 'creates new authentication' do
        expect { service.call }.to change(Authentication, :count).by(1)
      end

      it 'verifies user' do
        authentication = service.call
        expect(authentication.user).to be_verified
      end

      context 'when auth_hash does not contain first_name' do
        let(:auth_hash) { Faker::Omniauth.linkedin(name: '<PERSON> <PERSON>').tap { |hash| hash[:info][:first_name] = nil } }

        it 'derives first_name from name' do
          authentication = service.call
          expect(authentication.user.first_name).to eq 'Rob'
        end
      end

      context 'when auth_hash is missing email' do
        let(:auth_hash) { Faker::Omniauth.linkedin.tap { |hash| hash[:info][:email] = nil } }

        it 'raises an error' do
          expect { service.call }.to raise_error ActiveRecord::NotNullViolation
        end
      end
    end

    context 'when user exists' do
      let(:password) { Faker::Internet.password(special_characters: true) }
      let!(:user) { create :user, email: '<EMAIL>', password: }
      let(:auth_hash) { Faker::Omniauth.linkedin(email: '<EMAIL>') }

      it 'does not create new user' do
        expect { service.call }.not_to change(User, :count)
      end

      it 'keeps existing password' do
        service.call
        expect(user.reload).to be_authenticated(password)
      end

      context 'when user is not verified' do
        let!(:user) { create :user, email: '<EMAIL>', verified: false }

        it 'removes existing password' do
          service.call
          expect(user.reload).not_to be_authenticated(password)
        end
      end
    end
  end
end
