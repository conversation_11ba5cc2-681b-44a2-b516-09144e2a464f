require 'rails_helper'

RSpec.describe ActiveCampaign::CustomAccountFields do
  subject(:instance) { described_class.instance }

  describe '#all' do
    before do
      stub_request(:get, %r{https://carriersource.api-us1.com/api/3/accountCustomFieldMeta})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when api request is successful' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/account_custom_field_meta/index/success.json').read }

      it 'returns all custom account fields' do
        expect(instance.all).to all(be_a(described_class::Field))
      end
    end

    context 'when api request is unsuccessful' do
      let(:status) { 422 }
      let(:body) { '{}' }

      it 'raises an error' do
        expect { instance.all }.to raise_error ActiveCampaign::Api::Error
      end
    end
  end
end
