require 'rails_helper'

RSpec.describe ActiveCampaign::CreateContact do
  describe '::run' do
    let(:user) { create :user }

    before do
      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/contacts})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when contact is created in ActiveCampaign' do
      let(:status) { 201 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/success.json').read }

      it 'returns a success response' do
        outcome = described_class.run(user:)
        expect(outcome).to be_success
      end

      it 'creates a new ActiveCampaignRecord' do
        expect { described_class.run(user:) }.to change(ActiveCampaignRecord, :count).by(1)
      end
    end

    context 'when contact is not created in ActiveCampaign' do
      context 'when contact already exists' do
        let(:status) { 422 }
        let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/failure.json').read }

        let(:contacts_body) do
          Rails.root.join('spec/fixtures/active_campaign/contacts/find_by_email/success.json').read
        end

        before do
          stub_request(:get, %r{https://carriersource.api-us1.com/api/3/contacts})
            .to_return(status: 200, body: contacts_body, headers: { 'Content-Type' => 'application/json' })
        end

        it 'syncs contact' do
          expect { described_class.run(user:) }.to change(ActiveCampaignRecord, :count).by(1)
        end
      end

      context 'with unknown error' do
        let(:status) { 500 }
        let(:body) { '{}' }

        it 'returns a failure response' do
          outcome = described_class.run(user:)
          expect(outcome).to be_failure
        end
      end
    end
  end
end
