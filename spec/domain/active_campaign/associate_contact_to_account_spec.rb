require 'rails_helper'

RSpec.describe ActiveCampaign::AssociateContactToAccount do
  describe '::run' do
    let(:account) { 2 }
    let(:contact) { 5 }

    let(:associate_body) { Rails.root.join('spec/fixtures/active_campaign/account_contacts/create/success.json').read }

    let!(:request_stub) do
      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/accountContacts})
        .to_return(status:, body: associate_body, headers: { 'Content-Type' => 'application/json' })
    end

    context 'with successful response' do
      let(:status) { 201 }

      it 'creates account contact' do
        outcome = described_class.run(account:, contact:)
        expect(request_stub).to have_been_requested
        expect(outcome).to be_success
      end
    end

    context 'with unsuccessful response' do
      let(:status) { 422 }

      context 'with field_invalid error' do
        let(:associate_body) do
          Rails.root.join('spec/fixtures/active_campaign/account_contacts/create/field_invalid.json').read
        end

        it 'returns success outcome' do
          outcome = described_class.run(account:, contact:)
          expect(request_stub).to have_been_requested
          expect(outcome).to be_success
        end
      end

      context 'with field_missing error' do
        let(:associate_body) do
          Rails.root.join('spec/fixtures/active_campaign/account_contacts/create/field_missing.json').read
        end

        it 'returns an error' do
          outcome = described_class.run(account:, contact:)
          expect(request_stub).to have_been_requested
          expect(outcome).to be_failure
        end
      end
    end
  end
end
