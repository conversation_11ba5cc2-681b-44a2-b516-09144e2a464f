require 'rails_helper'

RSpec.describe ActiveCampaign::SyncContact do
  describe '::run' do
    let(:user) { create :user }

    let(:contacts_body) do
      Rails.root.join('spec/fixtures/active_campaign/contacts/find_by_email/success.json').read
    end

    before do
      stub_request(:get, %r{https://carriersource.api-us1.com/api/3/contacts})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when contact is found in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/find_by_email/success.json').read }

      it 'returns a success response' do
        outcome = described_class.run(user:)
        expect(outcome).to be_success
      end

      it 'creates an ActiveCampaignRecord' do
        expect { described_class.run(user:) }.to change(ActiveCampaignRecord, :count).by(1)
      end
    end

    context 'when contact is not found in ActiveCampaign' do
      context 'when response is successful' do
        let(:status) { 200 }
        let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/find_by_email/empty.json').read }

        it 'returns a failure outcome' do
          outcome = described_class.run(user:)
          expect(outcome).to be_failure
        end
      end

      context 'when response is not successful' do
        let(:status) { 422 }
        let(:body) { '{}' }

        it 'returns a failure outcome' do
          outcome = described_class.run(user:)
          expect(outcome).to be_failure
        end
      end
    end
  end
end
