require 'rails_helper'

RSpec.describe ActiveCampaign::UpsertContact do
  describe '::run' do
    let(:user) { create :user }

    before do
      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/contacts})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, %r{https://carriersource.api-us1.com/api/3/contacts/2})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when user is created in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/success.json').read }

      it 'returns a success response' do
        outcome = described_class.run(user:)
        expect(outcome).to be_success
      end
    end

    context 'when user is updated in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/success.json').read }

      before do
        ActiveCampaignRecord.create(record: user, external_id: 2)
      end

      it 'returns a success response' do
        outcome = described_class.run(user:)
        expect(outcome).to be_success
      end
    end
  end
end
