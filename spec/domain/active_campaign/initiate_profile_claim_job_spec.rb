require 'rails_helper'

RSpec.describe ActiveCampaign::InitiateProfileClaimJob do
  describe '#perform' do
    let(:user) { create :user, :carrier }
    let(:company) { create :company, :carrier }
    let(:carrier) { company.as_entity(:carrier) }

    it 'calls upsert account' do
      allow(ActiveCampaign::UpsertContact).to receive(:run!)
      allow(ActiveCampaign::UpsertAccount).to receive(:run!)
      described_class.perform_inline(user.to_gid.to_s, carrier.to_gid.to_s)
      expect(ActiveCampaign::UpsertContact).to have_received(:run!).with(user:)
      expect(ActiveCampaign::UpsertAccount).to have_received(:run!).with(company: carrier)
    end
  end
end
