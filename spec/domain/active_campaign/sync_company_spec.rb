require 'rails_helper'

RSpec.describe ActiveCampaign::SyncCompany do
  describe '::run' do
    let(:account_body) { Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read }
    let(:contact_body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/success.json').read }
    let(:associate_body) { Rails.root.join('spec/fixtures/active_campaign/account_contacts/create/success.json').read }
    let(:account_fields_body) do
      Rails.root.join('spec/fixtures/active_campaign/account_custom_field_meta/index/success.json').read
    end

    before do
      stub_request(:get, %r{https://carriersource.api-us1.com/api/3/accountCustomFieldMeta})
        .to_return(status: 200, body: account_fields_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/accounts})
        .to_return(status: 201, body: account_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, %r{https://carriersource.api-us1.com/api/3/accounts/\d+})
        .to_return(status: 201, body: account_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/contacts})
        .to_return(status: 201, body: contact_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, %r{https://carriersource.api-us1.com/api/3/contacts/\d+})
        .to_return(status: 201, body: contact_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/accountContacts})
        .to_return(status: 201, body: associate_body, headers: { 'Content-Type' => 'application/json' })

      allow(ActiveCampaign::UpsertContact).to receive(:run).and_call_original
      allow(ActiveCampaign::UpsertAccount).to receive(:run).and_call_original
      allow(ActiveCampaign::AssociateContactToAccount).to receive(:run).and_call_original
    end

    context 'with carrier entity_type' do
      let!(:carrier_profile) { create :carrier_profile }
      let(:company) { carrier_profile.company }
      let(:carrier) { carrier_profile.carrier }

      context 'when carrier has been claimed' do
        let(:user) { create :user }
        let!(:profile_user) { create :carrier_profile_user, :pending, carrier_profile:, user: }

        before do
          described_class.run(company: carrier)
          profile_user.verify!
        end

        it 'upserts contact' do
          described_class.run(company: carrier)
          expect(ActiveCampaign::UpsertContact).to have_received(:run).with(user:).twice
        end

        it 'upserts company' do
          described_class.run(company: carrier)
          expect(ActiveCampaign::UpsertAccount).to have_received(:run).with(company: carrier).twice
        end

        it 'associates contact to company' do
          described_class.run(company: carrier)
          expect(ActiveCampaign::AssociateContactToAccount).to have_received(:run).with(contact: 2, account: 5)
        end
      end

      context 'when carrier has not been claimed' do
        context 'when claim is pending' do
          let(:user) { create :user }

          before do
            create :carrier_profile_user, :pending, carrier_profile:, user:
          end

          it 'upserts contact' do
            described_class.run(company: carrier)
            expect(ActiveCampaign::UpsertContact).to have_received(:run).with(user:)
          end

          it 'upserts company' do
            described_class.run(company: carrier)
            expect(ActiveCampaign::UpsertAccount).to have_received(:run)
          end
        end

        context 'when claim is not pending' do
          it 'does not upsert contact' do
            described_class.run(company: carrier)
            expect(ActiveCampaign::UpsertContact).not_to have_received(:run)
          end

          it 'upserts company' do
            described_class.run(company: carrier)
            expect(ActiveCampaign::UpsertAccount).to have_received(:run)
          end
        end
      end
    end

    context 'with broker entity_type' do
      let(:company) { create :company, :broker, :with_city }
      let(:brokerage) { company.as_entity(:broker) }
      let!(:profile) { create :brokerage_profile, company: }

      context 'when brokerage has been claimed' do
        let(:user) { create :user }
        let!(:profile_user) { create :brokerage_profile_user, :pending, brokerage_profile: profile, user: }

        before do
          described_class.run(company: brokerage)
          profile_user.verify!
        end

        it 'upserts contact' do
          described_class.run(company: brokerage)
          expect(ActiveCampaign::UpsertContact).to have_received(:run).with(user:).twice
        end

        it 'upserts company' do
          described_class.run(company: brokerage)
          expect(ActiveCampaign::UpsertAccount).to have_received(:run).with(company: brokerage).twice
        end

        it 'associates contact to company' do
          described_class.run(company: brokerage)
          expect(ActiveCampaign::AssociateContactToAccount).to have_received(:run).with(contact: 2, account: 5)
        end
      end

      context 'when brokerage has not been claimed' do
        context 'when claim is pending' do
          let(:user) { create :user }

          before do
            create :brokerage_profile_user, :pending, brokerage_profile: profile, user:
          end

          it 'upserts contact' do
            described_class.run(company: brokerage)
            expect(ActiveCampaign::UpsertContact).to have_received(:run).with(user:)
          end

          it 'upserts company' do
            described_class.run(company: brokerage)
            expect(ActiveCampaign::UpsertAccount).to have_received(:run)
          end
        end

        context 'when claim is not pending' do
          it 'does not upsert contact' do
            described_class.run(company: brokerage)
            expect(ActiveCampaign::UpsertContact).not_to have_received(:run)
          end

          it 'upserts company' do
            described_class.run(company: brokerage)
            expect(ActiveCampaign::UpsertAccount).to have_received(:run)
          end
        end
      end
    end
  end
end
