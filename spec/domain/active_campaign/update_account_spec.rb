require 'rails_helper'

RSpec.describe ActiveCampaign::UpdateAccount do
  describe '::run' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }

    let(:account_fields_body) do
      Rails.root.join('spec/fixtures/active_campaign/account_custom_field_meta/index/success.json').read
    end

    before do
      stub_request(:get, %r{https://carriersource.api-us1.com/api/3/accountCustomFieldMeta})
        .to_return(status: 200, body: account_fields_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, 'https://carriersource.api-us1.com/api/3/accounts/5')
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when company is updated in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read }

      it 'returns a success outcome' do
        outcome = described_class.run(id: 5, company: carrier)
        expect(outcome).to be_success
      end
    end

    context 'when company is not updated in ActiveCampaign' do
      let(:status) { 422 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/accounts/create/failure.json').read }

      it 'returns a failure outcome' do
        outcome = described_class.run(id: 5, company: carrier)
        expect(outcome).to be_failure
      end
    end
  end
end
