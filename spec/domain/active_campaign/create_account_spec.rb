require 'rails_helper'

RSpec.describe ActiveCampaign::CreateAccount do
  shared_examples 'a request to create an account' do
    describe '::run' do
      let(:account_fields_body) do
        Rails.root.join('spec/fixtures/active_campaign/account_custom_field_meta/index/success.json').read
      end

      before do
        stub_request(:get, %r{https://carriersource.api-us1.com/api/3/accountCustomFieldMeta})
          .to_return(status: 200, body: account_fields_body, headers: { 'Content-Type' => 'application/json' })

        stub_request(:post, 'https://carriersource.api-us1.com/api/3/accounts')
          .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when company is created in ActiveCampaign' do
        let(:status) { 200 }

        let(:body) do
          Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read
        end

        it 'returns a success response' do
          outcome = described_class.run(company:)
          expect(outcome).to be_success
        end
      end

      context 'when company is not created in ActiveCampaign' do
        let(:status) { 422 }

        let(:body) do
          Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read
        end

        it 'returns a failure response' do
          outcome = described_class.run(company:)
          expect(outcome).to be_failure
        end
      end
    end
  end

  context 'when company is a carrier' do
    it_behaves_like 'a request to create an account' do
      let(:profile) { create :carrier_profile }
      let(:company) { profile.carrier }
    end
  end

  context 'when company is a brokerage' do
    it_behaves_like 'a request to create an account' do
      let(:profile) { create :brokerage_profile }
      let(:company) { profile.brokerage }
    end
  end
end
