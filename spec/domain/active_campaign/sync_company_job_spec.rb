require 'rails_helper'

RSpec.describe ActiveCampaign::SyncCompanyJob do
  describe '#perform' do
    let(:company) { create(:company) }
    let(:carrier) { company.as_entity(:carrier) }

    before do
      allow(ActiveCampaign::SyncCompany).to receive(:run!).with(company: carrier)
    end

    it 'calls ActiveCampaign::SyncCarrier with the company' do
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(ActiveCampaign::SyncCompany).to have_received(:run!).with(company: carrier)
    end
  end
end
