require 'rails_helper'

RSpec.describe ActiveCampaign::UpdateContact do
  describe '::run' do
    let(:user) { create :user }

    before do
      stub_request(:put, %r{https://carriersource.api-us1.com/api/3/contacts/2})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when contact is updated in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/success.json').read }

      it 'returns a success response' do
        outcome = described_class.run(id: 2, user:)
        expect(outcome).to be_success
      end
    end

    context 'when contact is not updated in ActiveCampaign' do
      let(:status) { 422 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/contacts/create/failure.json').read }

      it 'returns a failure response' do
        outcome = described_class.run(id: 2, user:)
        expect(outcome).to be_failure
      end
    end
  end
end
