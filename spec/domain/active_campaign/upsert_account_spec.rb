require 'rails_helper'

RSpec.describe ActiveCampaign::UpsertAccount do
  describe '::run' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }

    let(:account_fields_body) do
      Rails.root.join('spec/fixtures/active_campaign/account_custom_field_meta/index/success.json').read
    end

    before do
      stub_request(:get, %r{https://carriersource.api-us1.com/api/3/accountCustomFieldMeta})
        .to_return(status: 200, body: account_fields_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, %r{https://carriersource.api-us1.com/api/3/accounts})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, %r{https://carriersource.api-us1.com/api/3/accounts/5})
        .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when company is created in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read }

      it 'returns a success response' do
        outcome = described_class.run(company: carrier)
        expect(outcome).to be_success
      end

      it 'creates a new ActiveCampaignRecord' do
        expect { described_class.run(company: carrier) }.to change(ActiveCampaignRecord, :count).by(1)
      end
    end

    context 'when company is updated in ActiveCampaign' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/active_campaign/accounts/create/success.json').read }

      before do
        ActiveCampaignRecord.create(record: carrier, external_id: 5)
      end

      it 'returns a success response' do
        outcome = described_class.run(company: carrier)
        expect(outcome).to be_success
      end
    end
  end
end
