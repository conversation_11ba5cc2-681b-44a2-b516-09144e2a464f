require 'rails_helper'

RSpec.describe Pcs::GenerateContactCsv do
  subject(:generator) { described_class.new(input:, output: 'tmp/output.csv') }

  describe '#call' do
    let(:data) do
      <<~CSV
        Salesforce Record ID,DOT Number,Legal Name,DBA Name,Physical Street Address,Physical City,Physical State,Physical Zip,Phone Number,Email Address,Company Rep 1,Company Rep 2,Fleet Size,Common Authority,Contract Authority,Broker Authority,Authority Maintained Date,Truck Types,Freight Types,Shipment Types,Specialized Services,Insurance Amount,Safety Rating,Website URL,Active in Census,Bucket
        ,#{company.dot_number},TRANSPORTATION SERVICES INC,,18165 TELEGRAPH ROAD,Romulus,MI,48174,******-282-4444,<EMAIL>,KENNETH PELLEGRINO,MIKE ZAVISLAK,534,active,active,active,1985-10-23,<PERSON> Van,General Freight,Full Truckload,Team Drivers;Cross-Border;Hazardous Materials,1000000,satisfactory,https://www.tsitrucks.com/,true,4;8
        ,#{Census.maximum(:dot_number).next},ALASKA WEST EXPRESS INC,LYND<PERSON> OILFIELD SERVICES,1095 SANDURI STREET,Fairbanks,AK,99701,******-452-4355,<EMAIL>,ERIC BADGER,EVERETT BILLINGSLEA,149,active,active,active,1996-07-17,Auto Carrier;Tanker;Reefer,"General Freight;Metal: Sheets, Coils, Rolls;Motor Vehicles;Driveaway / Towaway;Logs, Poles, Beams, Lumber;Building Materials;Machinery, Large Objects;Fresh Produce;Liquids/Gases;Intermodal Containers;Oilfield Equipment;U.S. Mail;Chemicals;Commodities Dry Bulk;Refrigerated Food;Beverages;Paper Products;Construction",Parcel;Less than Truckload;Full Truckload,Team Drivers;Hazardous Materials,0,satisfactory,https://www.lynden.com/,true,2;3;6;7;8;9
      CSV
    end

    let(:input) { CSV.parse(data, headers: true) }
    let(:company) { create :company, :carrier, :with_city, :with_authority }

    before do
      create(:carrier_profile, company:).then do |carrier_profile|
        create :carrier_profile_contact, carrier_profile:, type: :owner
        create :carrier_profile_contact, carrier_profile:, type: :safety
      end
    end

    it 'generates a CSV with the correct headers' do
      generator.call
      csv = File.read('tmp/output.csv')
      expect(csv).to start_with(described_class::HEADERS.join(','))
    end
  end
end
