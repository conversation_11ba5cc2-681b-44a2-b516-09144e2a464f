require 'rails_helper'

RSpec.describe Crontab::CreateRenderJob do
  describe '#perform' do
    before do
      stub_request(:post, 'https://api.render.com/v1/services/www-service-id/jobs').to_return(status:)
    end

    context 'when job creation is successful' do
      let(:status) { 201 }

      it 'does not raise an error' do
        expect { described_class.perform_inline('analytics:premake_partitions') }.not_to raise_error
      end
    end

    context 'when job creation fails' do
      let(:status) { 401 }

      it 'raises an error' do
        expect { described_class.perform_inline('analytics:premake_partitions') }.to raise_error StandardError
      end
    end
  end
end
