require 'rails_helper'

RSpec.describe Hubspot::CreatePropertyGroup do
  let(:integration) { create :analytics_integration, :hubspot, :carrier }
  let(:object_type) { 'company' }
  let(:name) { 'shipper_intent' }

  describe '::run' do
    before do
      stub_request(:post, 'https://api.hubapi.com/crm/v3/properties/company/groups')
        .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when response is successful' do
      let(:status) { 201 }
      let(:body) { Rails.root.join('spec/fixtures/hubspot/properties/groups/create/created.json').read }

      it 'returns successful outcome' do
        response = described_class.run(integration:, object_type:, name:)
        expect(response).to be_success
      end
    end

    context 'when response is conflict' do
      let(:status) { 409 }
      let(:body) { Rails.root.join('spec/fixtures/hubspot/properties/groups/create/conflict.json').read }

      it 'returns successful outcome' do
        outcome = described_class.run(integration:, object_type:, name:)
        expect(outcome).to be_success
      end
    end

    context 'when response is not successful' do
      let(:status) { 401 }
      let(:body) { '{}' }

      it 'returns failure outcome' do
        response = described_class.run(integration:, object_type:, name:)
        expect(response).to be_failure
      end
    end
  end
end
