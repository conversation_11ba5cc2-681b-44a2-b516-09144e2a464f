require 'rails_helper'

RSpec.describe Hubspot::FindCompanyByProperty do
  describe '#call' do
    subject(:finder) { described_class.new(property: 'domain', value: 'costco.com', token: 'token') }

    let(:body) { Rails.root.join('spec/fixtures/hubspot/companies/search/success.json').read }

    before do
      stub_request(:post, %r{https://api.hubapi.com/crm/v3/objects/companies/search})
        .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
    end

    it 'returns company' do
      expect(finder.call).to(
        match(hash_including('id' => '512', 'properties' => { 'domain' => 'costco.com', 'name' => 'Costco Wholesale' }))
      )
    end
  end
end
