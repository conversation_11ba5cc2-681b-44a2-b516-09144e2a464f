require 'rails_helper'

RSpec.describe Hubspot::CreateTimelineEvent do
  let(:notification) { create :analytics_company_event_feed_notification, :hubspot, feed:, company: }

  let!(:event) do
    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { route: 'trucking_companies/locations#city',
                         url: Routes.city_trucking_companies_url(*city.path,
                                                                 carrier: { truck_type: [truck_types(:flatbed).id] }),
                         path_parameters: { city: 'salt-lake-city', state: 'utah', country: 'united-states' } }
  end

  let!(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, :carrier, legal_name: 'Mone Transport LLC' }
  let(:analytics_company) { create :analytics_company, domain: 'costco.com', name: 'Costco Wholesale' }
  let(:visit) { create :analytics_visit, :geolocated, :completed, company: analytics_company }
  let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }

  let(:feed) do
    create :analytics_company_event_feed, :carrier, company:, filters: Forms::AnalyticsEvent.new(search: true).to_h
  end

  let!(:request_stub) do
    stub_request(:post, %r{https://api.hubapi.com/crm/v3/timeline/events})
      .and_return(status:, body: '{}', headers: { 'Content-Type' => 'application/json' })
  end

  let(:shipper_event) { Analytics::ShipperEvent.find_by! event_id: event.id }

  before do
    Analytics::Events::CreateShipperEventJob.drain
  end

  describe '#call' do
    let(:status) { 201 }

    context 'when hubspot company does not exist' do
      before do
        allow(Hubspot::FindCompanyByProperty).to receive(:call).and_return(nil)
      end

      it 'does not create timeline event' do
        described_class.call(notification, shipper_event)
        expect(request_stub).not_to have_been_requested
      end
    end

    context 'when hubspot company exists' do
      before do
        allow(Hubspot::FindCompanyByProperty).to receive(:call).and_return('id' => '512')
      end

      context 'with successful request' do
        it 'creates timeline event' do
          described_class.call(notification, shipper_event)
          expect(request_stub).to have_been_requested
        end
      end

      context 'with failed request' do
        let(:status) { 400 }

        it 'raises an error' do
          expect { described_class.call(notification, shipper_event) }.to raise_error Hubspot::Api::Error
        end
      end
    end
  end
end
