require 'rails_helper'

RSpec.describe Hubspot::CleanCompanyDomains do
  subject(:cleaner) { described_class.new(integration:, after:) }

  let(:integration) { create :analytics_integration, :carrier, :hubspot }
  let(:after) { nil }

  let(:oauth_body) { Rails.root.join('spec/fixtures/hubspot/oauth/token/success.json').read }

  let!(:update_stub) do
    stub_request(:post, %r{https://api.hubapi.com/crm/v3/objects/companies/batch/update})
      .to_return(status: 200, body: '{}', headers: { 'Content-Type' => 'application/json' })
  end

  before do
    stub_request(:post, 'https://api.hubapi.com/oauth/v1/token')
      .and_return(status: 200, body: oauth_body, headers: { 'Content-Type' => 'application/json' })

    stub_request(:post, %r{https://api.hubapi.com/crm/v3/objects/companies/search})
      .to_return(status: 200, body: search_body, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when there are domains to be updated' do
      let(:search_body) { Rails.root.join('spec/fixtures/hubspot/companies/search/domains.json').read }

      it 'updates the company domains' do
        cleaner.call
        expect(update_stub).to have_been_requested
      end
    end

    context 'when there are no domains to be updated' do
      let(:search_body) { '{"results": []}' }

      it 'does not update any domains' do
        cleaner.call
        expect(update_stub).not_to have_been_requested
      end
    end
  end
end
