require 'rails_helper'

RSpec.describe Hubspot::Blueprints::Company do
  describe 'most_recent_brokerage_profile_view' do
    context 'when brokerage view exists' do
      let(:carrier_profile) { create :carrier_profile }
      let(:carrier) { carrier_profile.carrier }
      let(:brokerage) { create :company, :broker, legal_name: 'Coyote Logistics' }
      let(:broker) { create :persona, :broker, :verified, company: brokerage }
      let(:user) { broker.user }

      before do
        create :analytics_event, name: 'Page Viewed', user:, properties: { id: carrier.id, route: 'carriers#show' }
        Rollups.generate
      end

      it 'returns brokerage name' do
        properties = described_class.render_as_hash(carrier, view: :carrier)
        expect(properties).to include(most_recent_brokerage_profile_view: 'Coyote Logistics')
      end
    end
  end
end
