require 'rails_helper'

RSpec.describe Hubspot::SyncCompanyJob do
  describe '#perform' do
    let(:company) { create :company, :carrier }
    let(:carrier) { company.as_entity(:carrier) }

    before do
      allow(Hubspot::SyncCompany).to receive(:run!).with(company: carrier)
    end

    it 'calls Hubspot::SyncCarrier with the company' do
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(Hubspot::SyncCompany).to have_received(:run!).with(company: carrier)
    end
  end
end
