require 'rails_helper'

RSpec.describe Hubspot::ActivateIntegrationJob do
  describe '#perform' do
    let(:integration) { create :analytics_integration, :hubspot, :carrier }

    it 'activates the integration' do
      allow(Hubspot::ActivateIntegration).to receive(:run!)
      described_class.perform_inline(integration.id)
      expect(Hubspot::ActivateIntegration).to have_received(:run!).with(integration:)
    end
  end
end
