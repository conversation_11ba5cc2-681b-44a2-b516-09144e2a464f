require 'rails_helper'

RSpec.describe Hubspot::SyncCompany do
  describe '::run' do
    before do
      stub_request(:patch, %r{https://api.hubapi.com/crm/v3/objects/contacts})
        .to_return(status: 200, body: '{"id":123}', headers: { 'Content-Type' => 'application/json' })

      stub_request(:patch, %r{https://api.hubapi.com/crm/v3/objects/companies})
        .to_return(status: 200, body: '{"id":321}', headers: { 'Content-Type' => 'application/json' })

      stub_request(:put, %r{https://api.hubapi.com/crm/v4/objects/contact/123/associations/company/321})
        .to_return(status: 201)

      allow(Hubspot::UpsertContact).to receive(:run).and_call_original
      allow(Hubspot::UpsertCompany).to receive(:run).and_call_original
      allow(Hubspot::AssociateContactToCompany).to receive(:run).and_call_original
    end

    context 'with carrier entity_type' do
      let!(:carrier_profile) { create :carrier_profile, company: }
      let(:company) { create :company, :carrier, :with_city, company_rep1: '<PERSON>', email: '<EMAIL>' }
      let(:carrier) { carrier_profile.carrier }

      context 'when carrier is missing contact information' do
        let(:email) { Faker::Internet.email }
        let(:name) { Faker::Name.name }
        let(:company) { create :company, :carrier, :with_city, company_rep1: name, email: }

        context 'when email is missing' do
          let(:email) { nil }

          it 'upserts contact' do
            described_class.run(company: carrier)
            expect(Hubspot::AssociateContactToCompany).not_to have_received(:run)
          end
        end

        context 'when name is missing' do
          let(:name) { nil }

          it 'upserts contact' do
            described_class.run(company: carrier)
            expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
          end
        end

        context 'when both are missing' do
          let(:email) { nil }
          let(:name) { nil }

          it 'does not upsert contact' do
            described_class.run(company: carrier)
            expect(Hubspot::AssociateContactToCompany).not_to have_received(:run)
          end
        end
      end

      context 'when carrier has been claimed' do
        let(:user) { create :user }

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user:
        end

        it 'upserts contact' do
          described_class.run(company: carrier)
          expect(Hubspot::UpsertContact).to have_received(:run).with(user:)
        end

        it 'upserts company' do
          described_class.run(company: carrier)
          expect(Hubspot::UpsertCompany).to have_received(:run).with(company: carrier)
        end

        it 'associates contact to company' do
          described_class.run(company: carrier)
          expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
        end
      end

      context 'when carrier has not been claimed' do
        it 'upserts contact' do
          described_class.run(company: carrier)
          expect(Hubspot::UpsertContact).to have_received(:run)
                                              .with(user: an_instance_of(Hubspot::SyncCompany::ContactUser))
        end

        it 'upserts company' do
          described_class.run(company: carrier)
          expect(Hubspot::UpsertCompany).to have_received(:run).with(company: carrier)
        end

        it 'associates contact to company' do
          described_class.run(company: carrier)
          expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
        end
      end
    end

    context 'with broker entity_type' do
      let!(:brokerage_profile) { create :brokerage_profile, company: }
      let(:company) { create :company, :broker, :with_city, company_rep1: 'John Doe', email: '<EMAIL>' }
      let(:brokerage) { brokerage_profile.brokerage }

      context 'when brokerage is missing contact information' do
        let(:email) { Faker::Internet.email }
        let(:name) { Faker::Name.name }
        let(:company) { create :company, :broker, :with_city, company_rep1: name, email: }

        context 'when email is missing' do
          let(:email) { nil }

          it 'upserts contact' do
            described_class.run(company: brokerage)
            expect(Hubspot::AssociateContactToCompany).not_to have_received(:run)
          end
        end

        context 'when name is missing' do
          let(:name) { nil }

          it 'upserts contact' do
            described_class.run(company: brokerage)
            expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
          end
        end

        context 'when both are missing' do
          let(:email) { nil }
          let(:name) { nil }

          it 'does not upsert contact' do
            described_class.run(company: brokerage)
            expect(Hubspot::AssociateContactToCompany).not_to have_received(:run)
          end
        end
      end

      context 'when brokerage has been claimed' do
        let(:user) { create :user }

        before do
          create :brokerage_profile_user, :verified, brokerage_profile:, user:
        end

        it 'upserts contact' do
          described_class.run(company: brokerage)
          expect(Hubspot::UpsertContact).to have_received(:run).with(user:)
        end

        it 'upserts company' do
          described_class.run(company: brokerage)
          expect(Hubspot::UpsertCompany).to have_received(:run).with(company: brokerage)
        end

        it 'associates contact to company' do
          described_class.run(company: brokerage)
          expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
        end
      end

      context 'when brokerage has not been claimed' do
        it 'upserts contact' do
          described_class.run(company: brokerage)
          expect(Hubspot::UpsertContact).to have_received(:run)
                                              .with(user: an_instance_of(Hubspot::SyncCompany::ContactUser))
        end

        it 'upserts company' do
          described_class.run(company: brokerage)
          expect(Hubspot::UpsertCompany).to have_received(:run).with(company: brokerage)
        end

        it 'associates contact to company' do
          described_class.run(company: brokerage)
          expect(Hubspot::AssociateContactToCompany).to have_received(:run).with(contact_id: 123, company_id: 321)
        end
      end
    end
  end
end
