require 'rails_helper'

RSpec.describe Hubspot::CleanCompanyDomainsJob do
  let(:integration) { create :analytics_integration, :hubspot, :carrier }

  before do
    allow(Hubspot::CleanCompanyDomains).to receive(:call).and_return(after)
  end

  describe '#perform' do
    context 'when there are more companies to process' do
      let(:after) { 5 }

      it 'queues up job to process next batch' do
        described_class.perform_inline(integration.id)
        expect(described_class.jobs.size).to eq 1
      end
    end

    context 'when there are no more companies to process' do
      let(:after) { nil }

      it 'does not queue up any more jobs' do
        described_class.perform_inline(integration.id)
        expect(described_class.jobs.size).to eq 0
      end
    end
  end
end
