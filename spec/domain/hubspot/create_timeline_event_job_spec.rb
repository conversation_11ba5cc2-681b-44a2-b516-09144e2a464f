require 'rails_helper'

RSpec.describe Hubspot::CreateTimelineEventJob do
  describe '#perform' do
    let(:notification) { create :analytics_company_event_feed_notification, :hubspot }
    let(:event) { create :analytics_event, name: 'Page Viewed' }

    it 'delegates to Hubspot::CreateTimelineEvent' do
      allow(Hubspot::CreateTimelineEvent).to receive(:call)
      described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
      expect(Hubspot::CreateTimelineEvent).to have_received(:call).with(notification, event)
    end
  end
end
