require 'rails_helper'

RSpec.describe DataUploads::Daily::Insurance do
  subject(:uploader) { described_class.new(data:) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/insur.csv').open('r') }

  describe '#call' do
    let(:operating_authority) { create :operating_authority, docket_number: 'MC1097931' }
    let(:filer) { create :filer }

    before do
      create :insurance, :cargo, operating_authority:, insurance_company: filer
      create :insurance, :bipd
    end

    it 'inserts new records' do
      expect { uploader.call }.to change(Insurance, :count).by(4)
    end

    it 'updates existing records' do
      uploader.call
      insurance = Insurance.find_by(docket_number: 'MC1097931', insurance_type: 'CARGO')
      expect(insurance).to have_attributes bi_pd_class: nil, bi_pd_max_limit: 0, bi_pd_underlying_limit: 0,
                                           policy_number: '0636142', effective_date: Date.new(2020, 3, 15),
                                           form_code: '34', insurance_company: filer
    end

    it 'does not remove existing records' do
      uploader.call
      expect(Insurance.count).to eq 6
    end
  end
end
