require 'rails_helper'

RSpec.describe DataUploads::Daily::OperatingAuthority do
  subject(:uploader) { described_class.new(data:) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/carrier.csv').open('r') }

  describe '#call' do
    before do
      create(:operating_authority, docket_number: 'MC048521')
      create(:operating_authority)
    end

    context 'when csv is blank' do
      let(:data) do
        <<~CSV
          docket_number,dot_number,common_authority,contract_authority,broker_authority,pending_common_authority
        CSV
      end

      it 'does not create any records' do
        expect { uploader.call }.not_to change(OperatingAuthority, :count)
      end
    end

    it 'inserts new records' do
      uploader.call
      authority = OperatingAuthority.find_by docket_number: 'MC116712'
      expect(authority).to have_attributes dot_number: 66_066, common_authority: 'active', contract_authority: 'active',
                                           broker_authority: 'none', pending_common_authority: false,
                                           pending_contract_authority: false, pending_broker_authority: false,
                                           common_authority_revocation: false, contract_authority_revocation: false,
                                           broker_authority_revocation: false, freight_flag: false,
                                           household_goods: false, bipd_required: 5000, cargo_required: false,
                                           bond_required: false, bipd_on_file: 5000, cargo_on_file: false,
                                           bond_on_file: false, dba_name: nil, legal_name: 'MID-AMERICAN COACHES, INC.',
                                           updated_at: be_within(5.seconds).of(Time.zone.now)
    end

    it 'updates existing records' do
      uploader.call
      authority = OperatingAuthority.find_by docket_number: 'MC048521'
      expect(authority).to have_attributes dot_number: 315_935, common_authority: 'active', contract_authority: 'none',
                                           broker_authority: 'none', pending_common_authority: false,
                                           pending_contract_authority: false, pending_broker_authority: false,
                                           common_authority_revocation: false, contract_authority_revocation: false,
                                           broker_authority_revocation: false, freight_flag: true,
                                           household_goods: true, bipd_required: 750, cargo_required: false,
                                           bond_required: false, bipd_on_file: 750, cargo_on_file: true,
                                           bond_on_file: false, dba_name: nil,
                                           legal_name: 'CABRINI MOVING SERVICE, INC.',
                                           updated_at: be_within(5.seconds).of(Time.zone.now)
    end

    it 'does not remove existing records' do
      uploader.call
      expect(OperatingAuthority.count).to eq 4
    end
  end
end
