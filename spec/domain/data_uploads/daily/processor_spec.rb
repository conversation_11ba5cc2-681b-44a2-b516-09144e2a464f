require 'rails_helper'

RSpec.describe DataUploads::Daily::Processor do
  subject(:processor) { described_class.new(date, dir:) }

  let(:date) { Date.new(2022, 7, 13) }
  let(:dir) { Rails.root.join('spec/fixtures/data_upload/daily').to_s }

  describe '#run' do
    before do
      allow(DataUploads::Daily::Authhist).to receive(:call).and_call_original
      allow(DataUploads::Daily::OperatingAuthority).to receive(:call).and_call_original
      allow(DataUploads::Daily::Insurance).to receive(:call).and_call_original
      allow(Elastic::ReindexJob).to receive(:load)
    end

    context 'when file download is successful' do
      before do
        allow(DataUploads::Download).to receive(:call)
      end

      it 'processes all files' do
        processor.run

        expect(DataUploads::Daily::Authhist).to have_received(:call)
        expect(DataUploads::Daily::OperatingAuthority).to have_received(:call)
        expect(DataUploads::Daily::Insurance).to have_received(:call)
      end

      it 'creates success record for data upload' do
        processor.run

        expect(DataUpload.last).to(
          have_attributes(
            file_names: '20220713/authhist, 20220713/carrier, 20220713/insur', type: 'daily', status: 'success'
          )
        )
      end

      it 'reindexes company model' do
        processor.run
        expect(Elastic::ReindexJob).to have_received(:load).twice
      end
    end

    context 'when file download fails' do
      before do
        allow(DataUploads::Download).to receive(:call).and_raise(StandardError)
      end

      it 'does not process files' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUploads::Daily::Authhist).not_to have_received(:call)
        expect(DataUploads::Daily::Insurance).not_to have_received(:call)
      end

      it 'creates failure record for data upload' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUpload.last).to(
          have_attributes(
            file_names: '20220713/authhist, 20220713/carrier, 20220713/insur', type: 'daily', status: 'failure'
          )
        )
      end
    end
  end
end
