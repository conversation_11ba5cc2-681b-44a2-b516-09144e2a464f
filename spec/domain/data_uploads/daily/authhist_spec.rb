require 'rails_helper'

RSpec.describe DataUploads::Daily::Authhist do
  subject(:uploader) { described_class.new(data:) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/authhist.csv').open('r') }

  describe '#call' do
    let(:operating_authority) { create :operating_authority, docket_number: 'MC014286' }

    before do
      create(:authhist, operating_authority:)
      create(:authhist)
    end

    it 'inserts new records' do
      uploader.call
      authhist = Authhist.find_by docket_number: 'MC001872'
      expect(authhist).to have_attributes common: nil, contract: Date.new(2004, 7, 9), broker: Date.new(2017, 7, 27)
    end

    it 'updates existing records' do
      uploader.call
      authhist = Authhist.find_by docket_number: 'MC014286'
      expect(authhist).to have_attributes common: Date.new(1992, 10, 8), contract: Date.new(1986, 4, 30),
                                          broker: Date.new(2015, 7, 21)
    end

    it 'does not remove existing records' do
      uploader.call
      expect(Authhist.count).to eq 3
    end
  end
end
