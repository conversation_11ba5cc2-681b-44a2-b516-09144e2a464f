require 'rails_helper'

RSpec.describe DataUploads::Download do
  subject(:downloader) { described_class.new(key:, target:) }

  let(:key) { 'daily/20220713/parsed/authhist.csv' }
  let(:target) { 'tmp/authhist.csv' }

  before do
    stub_request(:get, 'https://carriersource-serverless-uploads.s3.amazonaws.com/daily/20220713/parsed/authhist.csv')
      .to_return(status: 200, body: '', headers: {})
  end

  describe '#call' do
    it 'downloads file from S3' do
      downloader.call

      expect(WebMock).to have_requested(
        :get, 'https://carriersource-serverless-uploads.s3.amazonaws.com/daily/20220713/parsed/authhist.csv'
      )
    end
  end
end
