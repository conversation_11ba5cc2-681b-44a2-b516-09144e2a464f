require 'rails_helper'

RSpec.describe DataUploads::Monthly::Violation do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/monthly/violation.csv').open('r') }

  describe '#call' do
    it 'inserts new records' do
      expect { uploader.call }.to change(Violation, :count).by(4)
    end

    it 'sets correct attributes' do
      uploader.call
      violation = Violation.find_by unique_id: '638227125'
      expect(violation).to have_attributes dot_number: 1_585_627, insp_date: '2020-09-30', viol_code: '3939',
                                           basic_desc: 'VEHICLE_MAINTENANCE'
    end
  end
end
