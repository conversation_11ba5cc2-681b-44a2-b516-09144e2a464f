require 'rails_helper'

RSpec.describe DataUploads::Monthly::BasicsMeasure do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/monthly/basics.csv').open('r') }

  describe '#call' do
    it 'inserts new records' do
      expect { uploader.call }.to change(BasicsMeasure, :count).by(4)
    end

    it 'sets correct attributes' do
      uploader.call
      basics = BasicsMeasure.find_by dot_number: 198
      expect(basics).to have_attributes unsafe_driv_measure: 0.6, veh_maint_measure: 2.92
    end
  end
end
