require 'rails_helper'

RSpec.describe DataUploads::Monthly::Processor do
  subject(:processor) { described_class.new(date, dir:) }

  let(:date) { Date.new(2022, 7, 13) }
  let(:dir) { Rails.root.join('spec/fixtures/data_upload/monthly').to_s }

  describe '#run' do
    before do
      allow(DataUploads::Monthly::BasicsMeasure).to receive(:call).and_call_original
      allow(DataUploads::Monthly::Crash).to receive(:call).and_call_original
      allow(DataUploads::Monthly::Inspection).to receive(:call).and_call_original
      allow(DataUploads::Monthly::Violation).to receive(:call).and_call_original
    end

    context 'when file download is successful' do
      before do
        allow(DataUploads::Download).to receive(:call)
      end

      it 'processes all files' do
        processor.run

        expect(DataUploads::Monthly::BasicsMeasure).to have_received(:call)
        expect(DataUploads::Monthly::Crash).to have_received(:call)
        expect(DataUploads::Monthly::Inspection).to have_received(:call)
        expect(DataUploads::Monthly::Violation).to have_received(:call)
      end

      it 'creates success record for data upload' do
        processor.run

        expect(DataUpload.last).to(
          have_attributes(file_names: '20220713/basics, 20220713/crash, 20220713/inspection, 20220713/violation',
                          type: 'monthly', status: 'success')
        )
      end
    end

    context 'when file download fails' do
      before do
        allow(DataUploads::Download).to receive(:call).and_raise(StandardError)
      end

      it 'does not process files' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUploads::Monthly::BasicsMeasure).not_to have_received(:call)
        expect(DataUploads::Monthly::Crash).not_to have_received(:call)
        expect(DataUploads::Monthly::Inspection).not_to have_received(:call)
        expect(DataUploads::Monthly::Violation).not_to have_received(:call)
      end

      it 'creates failure record for data upload' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUpload.last).to(
          have_attributes(file_names: '20220713/basics, 20220713/crash, 20220713/inspection, 20220713/violation',
                          type: 'monthly', status: 'failure')
        )
      end
    end
  end
end
