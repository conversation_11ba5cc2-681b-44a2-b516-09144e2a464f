require 'rails_helper'

RSpec.describe DataUploads::Monthly::Inspection do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/monthly/inspection.csv').open('r') }

  describe '#call' do
    it 'inserts new records' do
      expect { uploader.call }.to change(Inspection, :count).by(4)
    end

    it 'sets correct attributes' do
      uploader.call
      inpsection = Inspection.find_by dot_number: 111
      expect(inpsection).to have_attributes inspections: 2, driver_insp: 2, driver_oos: 0, vehicle_insp: 0,
                                            vehicle_oos: 0, states: 'WV+GA'
    end
  end
end
