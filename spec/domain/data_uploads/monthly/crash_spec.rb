require 'rails_helper'

RSpec.describe DataUploads::Monthly::Crash do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/monthly/crash.csv').open('r') }

  describe '#call' do
    it 'inserts new records' do
      expect { uploader.call }.to change(Crash, :count).by(4)
    end

    it 'sets correct attributes' do
      uploader.call
      crash = Crash.find_by dot_number: 8294
      expect(crash).to have_attributes date: '2020-12-15', fatalities: 0, injuries: 0, tow_away: true
    end
  end
end
