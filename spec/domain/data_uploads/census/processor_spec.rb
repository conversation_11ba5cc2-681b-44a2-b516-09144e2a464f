require 'rails_helper'

RSpec.describe DataUploads::Census::Processor do
  subject(:processor) do
    described_class.new(Date.new(2022, 9, 9), dir: Rails.root.join('spec/fixtures/data_upload').to_s, target:)
  end

  let(:target) { Rails.root.join('spec/fixtures/data_upload/census.csv').to_s }

  before do
    allow(DataUploads::Download).to receive :call
  end

  describe '#run' do
    it 'downloads file' do
      processor.run
      expect(DataUploads::Download).to have_received(:call)
                                         .with(key: 'census/20220909/census.csv',
                                               target: Rails.root.join('spec/fixtures/data_upload/census.csv').to_s)
    end

    it 'imports into census table' do
      expect { processor.run }.to change(Census, :count).by(3)
    end

    it 'creates data upload entry' do
      expect { processor.run }.to change(DataUpload, :count).by(1)
      expect(DataUpload.last).to have_attributes type: 'census', status: 'success'
    end

    context 'when import fails' do
      it 'updates data upload status to failure' do
        allow(DataUploads::Census::ImportCensus).to receive(:call).and_raise ArgumentError
        expect { processor.run }.to raise_error ArgumentError
        expect(DataUpload.last).to have_attributes type: 'census', status: 'failure'
      end

      context 'when csv file format is invalid' do
        let(:target) { Rails.root.join('spec/fixtures/data_upload/invalid-census.csv').to_s }

        it 'raises an error' do
          expect { processor.run }.to raise_error ArgumentError, 'Invalid file format'
        end

        it 'does not create data upload entry' do
          expect { processor.run }.to raise_error ArgumentError
          expect(DataUpload.count).to eq 0
        end
      end
    end

    context 'with companies' do
      before do
        allow(Companies::UpsertJob).to receive(:batch_load)
      end

      it 'queues up jobs' do
        processor.run
        expect(Companies::UpsertJob).to have_received(:batch_load).with(model: Census)
      end
    end
  end
end
