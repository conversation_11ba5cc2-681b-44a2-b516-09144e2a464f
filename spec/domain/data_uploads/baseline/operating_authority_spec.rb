require 'rails_helper'

RSpec.describe DataUploads::Baseline::OperatingAuthority do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/carrier.csv').open('r') }

  describe '#call' do
    before do
      create(:operating_authority, docket_number: 'MC048521')
      create(:operating_authority)
    end

    context 'when csv is blank' do
      let(:data) do
        <<~CSV
          docket_number,dot_number,common_authority,contract_authority,broker_authority,pending_common_authority
        CSV
      end

      it 'does not insert any records' do
        uploader.call
        expect(OperatingAuthority.count).to eq 0
      end
    end

    it 'inserts records' do
      uploader.call
      authority = OperatingAuthority.find_by docket_number: 'MC116712'
      expect(authority).to have_attributes dot_number: 66_066, common_authority: 'active', contract_authority: 'active',
                                           broker_authority: 'none', pending_common_authority: false,
                                           pending_contract_authority: false, pending_broker_authority: false,
                                           common_authority_revocation: false, contract_authority_revocation: false,
                                           broker_authority_revocation: false, freight_flag: false,
                                           household_goods: false, bipd_required: 5000, cargo_required: false,
                                           bond_required: false, bipd_on_file: 5000, cargo_on_file: false,
                                           bond_on_file: false, dba_name: nil, legal_name: 'MID-AMERICAN COACHES, INC.'
    end

    it 'removes existing records' do
      uploader.call
      expect(OperatingAuthority.count).to eq 3
    end
  end
end
