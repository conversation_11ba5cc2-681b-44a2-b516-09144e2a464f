require 'rails_helper'

RSpec.describe DataUploads::Baseline::Insurance do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/insur.csv').open('r') }

  describe '#call' do
    let(:operating_authority) { create :operating_authority, docket_number: 'MC1097931' }
    let(:filer) { create :filer }

    before do
      create :insurance, :cargo, operating_authority:, insurance_company: filer
      create :insurance, :bipd
    end

    it 'inserts records' do
      uploader.call
      insurance = Insurance.find_by docket_number: 'MC1045010', insurance_type: 'BIPD_PRIMARY'
      expect(insurance).to have_attributes effective_date: Date.new(2022, 6, 19), bi_pd_max_limit: 2000
    end

    it 'removes existing records' do
      uploader.call
      expect(Insurance.count).to eq 5
    end
  end
end
