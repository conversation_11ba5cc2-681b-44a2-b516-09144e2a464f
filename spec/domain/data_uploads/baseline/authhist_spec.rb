require 'rails_helper'

RSpec.describe DataUploads::Baseline::Authhist do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/authhist.csv').open('r') }

  describe '#call' do
    let(:operating_authority) { create :operating_authority, docket_number: 'MC014286' }

    before do
      create(:authhist, operating_authority:)
      create(:authhist)
    end

    it 'inserts records' do
      uploader.call
      authhist = Authhist.find_by docket_number: 'MC001872'
      expect(authhist).to have_attributes common: nil, contract: Date.new(2004, 7, 9), broker: Date.new(2017, 7, 27)
    end

    it 'removes existing records' do
      uploader.call
      expect(Authhist.count).to eq 2
    end
  end
end
