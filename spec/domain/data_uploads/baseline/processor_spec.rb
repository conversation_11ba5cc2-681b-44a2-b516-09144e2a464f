require 'rails_helper'

RSpec.describe DataUploads::Baseline::Processor, :elasticsearch do
  subject(:processor) { described_class.new(date, dir:) }

  let(:date) { Date.new(2022, 7, 13) }
  let(:dir) { Rails.root.join('spec/fixtures/data_upload/daily').to_s }

  before do
    create :company, :carrier, :with_city, :with_authority
    create :company, :broker, :with_city, :with_authority
  end

  describe '#run' do
    before do
      allow(DataUploads::Baseline::Authhist).to receive(:call).and_call_original
      allow(DataUploads::Baseline::OperatingAuthority).to receive(:call).and_call_original
      allow(DataUploads::Baseline::Insurance).to receive(:call).and_call_original
    end

    context 'when file download is successful' do
      before do
        allow(DataUploads::Download).to receive(:call)
      end

      it 'processes all files' do
        processor.run

        expect(DataUploads::Baseline::Authhist).to have_received(:call)
        expect(DataUploads::Baseline::OperatingAuthority).to have_received(:call)
        expect(DataUploads::Baseline::Insurance).to have_received(:call)
      end

      it 'creates success record for data upload' do
        processor.run

        expect(DataUpload.last).to(
          have_attributes(file_names: '20220713/authhist, 20220713/carrier, 20220713/insur',
                          type: 'baseline', status: 'success')
        )
      end

      it 'reindexes models' do
        processor.run
        expect(Elastic::ReindexJob.jobs.size).to be_positive
      end
    end

    context 'when file download fails' do
      before do
        allow(DataUploads::Download).to receive(:call).and_raise(StandardError)
      end

      it 'does not process files' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUploads::Baseline::Authhist).not_to have_received(:call)
        expect(DataUploads::Baseline::Insurance).not_to have_received(:call)
      end

      it 'creates failure record for data upload' do
        expect { processor.run }.to raise_error StandardError
        expect(DataUpload.last).to(
          have_attributes(file_names: '20220713/authhist, 20220713/carrier, 20220713/insur',
                          type: 'baseline', status: 'failure')
        )
      end
    end
  end
end
