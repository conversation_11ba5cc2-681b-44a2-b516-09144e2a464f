require 'rails_helper'

RSpec.describe DataUploads::Safety::Crash do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/crash.csv').open('r') }

  describe '#call' do
    it 'sets correct attributes' do
      uploader.call
      crash = Crash.find_by crash_id: 34_193
      expect(crash).to have_attributes report_number: 'TX0012009959', date: '1992-01-15', dot_number: 316_362,
                                       fatalities: 0, injuries: 0, tow_away: true
    end

    it 'inserts all rows' do
      uploader.call
      expect(Crash.count).to eq 3
    end
  end
end
