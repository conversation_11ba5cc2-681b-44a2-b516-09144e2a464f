require 'rails_helper'

RSpec.describe DataUploads::Safety::Inspection do
  subject(:uploader) { described_class.new(data) }

  let(:data) { Rails.root.join('spec/fixtures/data_upload/daily/inspection.csv').open('r') }

  describe '#call' do
    it 'sets correct attributes' do
      uploader.call
      inspection = Inspection.find_by dot_number: 1
      expect(inspection).to have_attributes inspections: 4, driver_insp: 4, driver_oos: 0, vehicle_insp: 2,
                                            vehicle_oos: 2
    end

    it 'inserts all rows' do
      uploader.call
      expect(Inspection.count).to eq 4
    end
  end
end
