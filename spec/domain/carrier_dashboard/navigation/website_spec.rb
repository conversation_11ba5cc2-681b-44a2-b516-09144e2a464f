require 'rails_helper'

RSpec.describe CarrierDashboard::Navigation::Website do
  subject(:page) { described_class.new(carrier:, user:, params: {}) }

  let(:profile) { create :carrier_profile }
  let(:carrier) { profile.carrier }
  let(:user) { build_stubbed :user }

  describe 'page urls' do
    let(:user) { build_stubbed :user, admin: true }

    it 'returns correct urls' do
      expect(page.children.map(&:url)).to(
        eq(
          [
            Routes.carrier_dashboard_website_styles_url(carrier),
            Routes.carrier_dashboard_website_sections_url(carrier),
            Routes.carrier_dashboard_website_settings_url(carrier),
            Routes.carrier_dashboard_website_socials_url(carrier)
          ]
        )
      )
    end
  end

  describe '#children' do
    context 'when carrier is premium' do
      before do
        allow(profile).to receive(:access_to_feature?).with(:website).and_return(true)
      end

      it 'returns all children' do
        expect(page.children.map(&:key)).to eq %w(styles sections settings socials)
      end
    end

    context 'when carrier is not premium' do
      it 'returns empty array' do
        expect(page.children).to be_empty
      end
    end

    context 'when user is admin' do
      let(:user) { build_stubbed :user, admin: true }

      it 'returns all children' do
        expect(page.children.map(&:key)).to eq %w(styles sections settings socials)
      end
    end
  end
end
