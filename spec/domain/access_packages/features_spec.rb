require 'rails_helper'

RSpec.describe AccessPackages::Features do
  describe '::for' do
    context 'with branding package' do
      let(:access_package) { build_stubbed :access_package, :carrier, packages: %w(branding) }

      it 'returns the correct features' do
        features = described_class.for(access_package)
        expect(features).to eq %w(featured_review assets sponsored_content banner capacity)
      end
    end

    context 'with unknown package' do
      let(:access_package) { build_stubbed :access_package, :carrier, packages: ['unknown'] }

      it 'returns an empty array' do
        features = described_class.for(access_package)
        expect(features).to be_empty
      end
    end
  end
end
