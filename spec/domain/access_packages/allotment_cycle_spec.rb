require 'rails_helper'

RSpec.describe AccessPackages::AllotmentCycle do
  describe '#range' do
    subject(:billing_cycle) { described_class.new(start_date) }

    shared_examples 'a billing cycle' do |date, range|
      it 'returns the billing cycle range' do
        expect(billing_cycle.range(date)).to eq range
      end
    end

    context 'when start date is beginning of month' do
      let(:start_date) { Date.new(2022, 11, 1) }

      it_behaves_like 'a billing cycle', Date.new(2022, 9, 15), Date.new(2022, 9, 1)...Date.new(2022, 10, 1)
      it_behaves_like 'a billing cycle', Date.new(2022, 10, 20), Date.new(2022, 10, 1)...Date.new(2022, 11, 1)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 1), Date.new(2022, 11, 1)...Date.new(2022, 12, 1)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 5), Date.new(2022, 11, 1)...Date.new(2022, 12, 1)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 30), Date.new(2022, 11, 1)...Date.new(2022, 12, 1)
      it_behaves_like 'a billing cycle', Date.new(2023, 1, 7), Date.new(2023, 1, 1)...Date.new(2023, 2, 1)
    end

    context 'when start date is not beginning of month' do
      let(:start_date) { Date.new(2022, 11, 15) }

      it_behaves_like 'a billing cycle', Date.new(2022, 10, 15), Date.new(2022, 10, 15)...Date.new(2022, 11, 15)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 1), Date.new(2022, 10, 15)...Date.new(2022, 11, 15)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 15), Date.new(2022, 11, 15)...Date.new(2022, 12, 15)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 30), Date.new(2022, 11, 15)...Date.new(2022, 12, 15)
      it_behaves_like 'a billing cycle', Date.new(2023, 1, 7), Date.new(2022, 12, 15)...Date.new(2023, 1, 15)
    end

    context 'when start date is end of month' do
      let(:start_date) { Date.new(2022, 11, 30) }

      it_behaves_like 'a billing cycle', Date.new(2022, 11, 1), Date.new(2022, 10, 31)...Date.new(2022, 11, 30)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 15), Date.new(2022, 10, 31)...Date.new(2022, 11, 30)
      it_behaves_like 'a billing cycle', Date.new(2022, 11, 30), Date.new(2022, 11, 30)...Date.new(2022, 12, 31)
      it_behaves_like 'a billing cycle', Date.new(2023, 1, 7), Date.new(2022, 12, 31)...Date.new(2023, 1, 31)
      it_behaves_like 'a billing cycle', Date.new(2023, 2, 27), Date.new(2023, 1, 31)...Date.new(2023, 2, 28)
      it_behaves_like 'a billing cycle', Date.new(2023, 2, 28), Date.new(2023, 2, 28)...Date.new(2023, 3, 31)
      it_behaves_like 'a billing cycle', Date.new(2023, 3, 15), Date.new(2023, 2, 28)...Date.new(2023, 3, 31)
      it_behaves_like 'a billing cycle', Date.new(2023, 3, 30), Date.new(2023, 2, 28)...Date.new(2023, 3, 31)
      it_behaves_like 'a billing cycle', Date.new(2023, 4, 1), Date.new(2023, 3, 31)...Date.new(2023, 4, 30)
    end
  end
end
