require 'rails_helper'

RSpec.describe Functions::NormalizeEmail do
  shared_examples 'a normalized email' do |email, expected|
    it "returns #{expected} for #{email}" do
      expect(described_class.call(email)).to eq(expected)
    end
  end

  it_behaves_like 'a normalized email', '', nil
  it_behaves_like 'a normalized email', 'foo@bar', nil
  it_behaves_like 'a normalized email', '<EMAIL>', '<EMAIL>'
  it_behaves_like 'a normalized email', '<EMAIL>; <EMAIL>', '<EMAIL>'
  it_behaves_like 'a normalized email', '<EMAIL>; ', '<EMAIL>'
end
