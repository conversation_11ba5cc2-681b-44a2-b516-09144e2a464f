require 'rails_helper'

RSpec.describe Functions::AppendToUrl do
  describe '#call' do
    subject(:function) { described_class.new(url, params) }

    let(:url) { 'http://example.com' }
    let(:params) { { foo: 'bar' } }

    it 'appends params to url' do
      expect(function.call).to eq 'http://example.com?foo=bar'
    end

    context 'when url already has params' do
      let(:url) { 'http://example.com?baz=qux' }

      it 'appends params to url' do
        expect(function.call).to eq 'http://example.com?baz=qux&foo=bar'
      end
    end
  end
end
