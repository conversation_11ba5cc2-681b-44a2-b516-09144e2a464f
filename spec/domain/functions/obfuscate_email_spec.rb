require 'rails_helper'

RSpec.describe Functions::ObfuscateEmail do
  shared_examples 'obfuscates email' do |email, expected|
    it "obfuscates #{email} with half of the characters" do
      expect(described_class.call(email)).to eq(expected)
    end
  end

  it_behaves_like 'obfuscates email', '<EMAIL>', 'ha***@carriersource.io'
  it_behaves_like 'obfuscates email', '', nil
  it_behaves_like 'obfuscates email', nil, nil
  it_behaves_like 'obfuscates email', '<EMAIL>', 'smarf.d*******@gmail.com'
end
