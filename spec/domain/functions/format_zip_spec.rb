require 'rails_helper'

RSpec.describe Functions::FormatZip do
  shared_examples 'a valid zip code' do |country, zip, expected|
    it "formats the zip code for #{country}" do
      expect(described_class.call(zip:, country:)).to eq(expected)
    end
  end

  context 'when the country is the United States' do
    it_behaves_like 'a valid zip code', 'US', '12345', '12345'
    it_behaves_like 'a valid zip code', 'US', '12345-6789', '12345'
    it_behaves_like 'a valid zip code', 'US', '12345-1234', '12345'
    it_behaves_like 'a valid zip code', 'US', '123451234', nil
    it_behaves_like 'a valid zip code', 'US', '1234', nil
    it_behaves_like 'a valid zip code', 'US', '12345-678', nil
    it_behaves_like 'a valid zip code', 'US', '12345-67890', nil
  end

  context 'when the country is Canada' do
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1A1', 'A1A 1A1'
    it_behaves_like 'a valid zip code', 'CA', 'L6R 3K7', 'L6R 3K7'
    it_behaves_like 'a valid zip code', 'CA', 'Z1A 1A1', nil
    it_behaves_like 'a valid zip code', 'CA', 'W1A 1A1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1D1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1F1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1I1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1O1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1Q1', nil
    it_behaves_like 'a valid zip code', 'CA', 'ABC 1A1', nil
    it_behaves_like 'a valid zip code', 'CA', 'A1A 1A1A', nil
  end

  context 'when the country is Mexico' do
    it_behaves_like 'a valid zip code', 'MX', '12345', '12345'
    it_behaves_like 'a valid zip code', 'MX', '123451234', nil
    it_behaves_like 'a valid zip code', 'MX', '1234', nil
    it_behaves_like 'a valid zip code', 'MX', '123456', nil
  end

  context 'when the country is not supported' do
    it_behaves_like 'a valid zip code', 'GB', '90210', nil
    it_behaves_like 'a valid zip code', 'AU', 'L6R 3K7', nil
  end
end
