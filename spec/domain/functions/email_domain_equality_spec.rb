require 'rails_helper'

RSpec.describe Functions::EmailDomainEquality do
  describe '#call' do
    subject { described_class.call(left, right) }

    context 'when the domains are the same' do
      let(:left) { '<EMAIL>' }
      let(:right) { '<EMAIL>' }

      it { is_expected.to be_truthy }
    end

    context 'when domains are different' do
      let(:left) { '<EMAIL>' }
      let(:right) { '<EMAIL>' }

      it { is_expected.to be_falsey }
    end

    context 'when email is incomplete' do
      let(:left) { 'hamed@' }
      let(:right) { '<EMAIL>' }

      it { is_expected.to be_falsey }
    end
  end
end
