require 'rails_helper'

RSpec.describe Functions::FormatPhone do
  subject(:formatter) { described_class.new(phone:, country:) }

  let(:phone) { '8885551234' }
  let(:country) { 'US' }

  describe '#call' do
    context 'when phone number does not exist' do
      let(:phone) { nil }

      it 'returns nil' do
        expect(formatter.call).to be_nil
      end
    end

    context 'when phone number exists' do
      it 'returns formatted phone number' do
        expect(formatter.call).to eq '******-555-1234'
      end
    end

    context 'when country is not default' do
      let(:country) { 'MX' }

      it 'uses correct international code' do
        expect(formatter.call).to eq '+52-************'
      end
    end
  end
end
