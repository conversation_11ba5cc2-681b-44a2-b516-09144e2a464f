require 'rails_helper'

RSpec.describe Functions::BatchRanges do
  subject(:function) { described_class.new(max:, step:, start:) }

  let(:step) { 5 }
  let(:start) { 1 }

  context 'when max is not a boundary' do
    let(:max) { 23 }

    it 'returns array of ranges' do
      expect(function.call).to eq [[1, 5], [6, 10], [11, 15], [16, 20], [21, 23]]
    end
  end

  context 'when max is one more than boundary' do
    let(:max) { 21 }

    it 'returns array of ranges' do
      expect(function.call).to eq [[1, 5], [6, 10], [11, 15], [16, 20], [21, 21]]
    end
  end

  context 'when max is a boundary' do
    let(:max) { 25 }

    it 'returns array of ranges' do
      expect(function.call).to eq [[1, 5], [6, 10], [11, 15], [16, 20], [21, 25]]
    end
  end

  context 'when start is not 1' do
    let(:max) { 23 }
    let(:start) { 6 }

    it 'returns array of ranges' do
      expect(function.call).to eq [[6, 10], [11, 15], [16, 20], [21, 23]]
    end
  end
end
