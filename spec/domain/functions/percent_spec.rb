require 'rails_helper'

RSpec.describe Functions::Percent do
  subject(:function) { described_class.new(value, total) }

  describe '#call' do
    let(:value) { 2 }

    context 'when total is positive' do
      let(:total) { 5 }

      it 'returns correct value' do
        expect(function.call).to eq 40
      end
    end

    context 'when total is zero' do
      let(:total) { 0 }

      it 'returns 0' do
        expect(function.call).to eq 0
      end
    end
  end
end
