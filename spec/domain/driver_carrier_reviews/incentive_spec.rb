require 'rails_helper'

RSpec.describe DriverCarrierReviews::Incentive do
  subject(:incentive) { described_class.new(campaign) }

  describe '#value' do
    context 'when campaign matches' do
      let(:campaign) { 'starbs10wrgc' }

      it 'returns 10' do
        expect(incentive.value).to eq 10
      end
    end

    context 'when campaign does not match' do
      let(:campaign) { 'foo' }

      it 'returns 5' do
        expect(incentive.value).to eq 5
      end
    end
  end
end
