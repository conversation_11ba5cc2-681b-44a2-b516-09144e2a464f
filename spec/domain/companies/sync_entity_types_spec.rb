require 'rails_helper'

RSpec.describe Companies::SyncEntityTypes do
  subject(:syncer) { described_class.new(company) }

  let(:census) { create :census, carship: 'B;C' }
  let(:company) { create :company, census: }

  context 'when there are no types to delete' do
    it 'syncs correctly' do
      syncer.call
      expect(company.reload.entity_types.pluck(:entity_type)).to match_array %w(broker carrier)
    end
  end

  context 'when there are no types to add' do
    before do
      company.entity_types.create(entity_type: 'broker')
      company.entity_types.create(entity_type: 'carrier')
      company.entity_types.create(entity_type: 'shipper')
    end

    it 'syncs correctly' do
      syncer.call
      expect(company.reload.entity_types.pluck(:entity_type)).to match_array %w(broker carrier)
    end
  end

  context 'when there are additions and deletions' do
    before do
      company.entity_types.create(entity_type: 'carrier')
      company.entity_types.create(entity_type: 'shipper')
    end

    it 'syncs correctly' do
      syncer.call
      expect(company.reload.entity_types.pluck(:entity_type)).to match_array %w(broker carrier)
    end
  end
end
