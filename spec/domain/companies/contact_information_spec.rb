require 'rails_helper'

RSpec.describe Companies::ContactInformation do
  subject(:contact_information) { described_class.new(entity) }

  context 'when the entity is a carrier' do
    let(:entity) { contact.carrier_profile.carrier }
    let(:contact) { create :carrier_profile_contact, phone: '8005551234' }

    describe '#phone' do
      it 'returns the phone number' do
        expect(contact_information.phone).to eq('******-555-1234')
      end
    end
  end

  context 'when the entity is a brokerage' do
    let(:entity) { contact.brokerage_profile.brokerage }
    let(:contact) { create :brokerage_profile_contact, phone: '8005551234' }

    describe '#phone' do
      it 'returns the phone number' do
        expect(contact_information.phone).to eq('******-555-1234')
      end
    end
  end
end
