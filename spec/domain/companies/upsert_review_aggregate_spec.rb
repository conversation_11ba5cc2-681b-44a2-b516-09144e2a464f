require 'rails_helper'

RSpec.describe Companies::UpsertReviewAggregate do
  subject(:updater) { described_class.new(carrier) }

  describe '#call' do
    let(:company) { create :company }
    let(:carrier) { company.as_entity(:carrier) }
    let!(:review) { create :review, :approved, company: }
    let(:sentiment) { create :sentiment, :positive, label: 'On Time Pickup' }

    before do
      review.review_sentiments.create sentiment:
    end

    context 'when aggregate record exists' do
      before do
        ReviewsAggregate.create company:, star_rating: 4, review_count: 3
      end

      it 'updates existing record' do
        expect { updater.call }.not_to change(ReviewsAggregate, :count)
        expect(ReviewsAggregate.find_by(company:)).to have_attributes star_rating: 5, review_count: 1,
                                                                      sentiments: { sentiment.id.to_s => 1 }
      end

      it 'queues job to generate email signature' do
        expect { updater.call }.to change(CompanyProfiles::StoreEmailSignatureJob.jobs, :size).by(1)
      end

      context 'when review is rejected' do
        before do
          review.rejection_reason = 'LACK_DETAIL'
          review.reject!
        end

        it 'updates existing record' do
          expect { updater.call }.not_to change(ReviewsAggregate, :count)
          expect(ReviewsAggregate.find_by(company:)).to have_attributes star_rating: nil, review_count: 0,
                                                                        sentiments: {}
        end
      end
    end

    context 'when aggregage record does not exist' do
      it 'creates new record' do
        expect { updater.call }.to change(ReviewsAggregate, :count).by(1)
        expect(ReviewsAggregate.find_by(company:)).to have_attributes star_rating: 5, review_count: 1,
                                                                      sentiments: { sentiment.id.to_s => 1 }
      end
    end
  end
end
