require 'rails_helper'

RSpec.describe Companies::SyncFreightsJob do
  let(:carrier) { create :company, :carrier }
  let(:broker) { create :company, :broker }

  describe '::batch_load' do
    it 'only queues jobs for carriers' do
      described_class.batch_load model: Company, finish: [carrier.id, broker.id].max
      expect(described_class.jobs.size).to eq 1
      expect(described_class.jobs.pick('args')).to eq [carrier.id]
    end
  end

  describe '#perform' do
    subject(:job) { described_class.new }

    before do
      allow(Companies::SyncFreights).to receive(:call)
    end

    it 'delegates' do
      job.perform(carrier.id)
      expect(Companies::SyncFreights).to have_received(:call)
    end
  end
end
