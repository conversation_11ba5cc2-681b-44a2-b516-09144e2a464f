require 'rails_helper'

RSpec.describe Companies::NameAssumptions do
  describe '::profile_attributes' do
    shared_examples 'infers truck type from name' do |name, truck_type|
      it 'returns correct truck type' do
        company = build_stubbed :company, legal_name: name
        attributes = described_class.profile_attributes(company)
        expect(attributes[:truck_types]).to eq [truck_types(truck_type).id]
      end
    end

    it_behaves_like 'infers truck type from name', 'Jameson Auto Transportation', :auto_carrier
    it_behaves_like 'infers truck type from name', "<PERSON>'s Refrigerated Goods", :reefer
    it_behaves_like 'infers truck type from name', "<PERSON>'s Frozen Goods", :reefer
    it_behaves_like 'infers truck type from name', 'Reefers LLC', :reefer
  end
end
