require 'rails_helper'

RSpec.describe Companies::BookmarkContext do
  subject(:context) { described_class.new(user, entities) }

  let(:user) { create :user }
  let(:list) { create :list, user: }

  describe '::Query' do
    context 'with bookmarked carriers' do
      let(:company) { create :company }
      let(:carrier) { company.as_entity(:carrier) }

      before do
        create :bookmark, :carrier, company:, list:
      end

      it 'returns bookmarked carriers' do
        expect(described_class::Query.call(Carrier, user, [carrier])).to eq [carrier.id]
      end
    end

    context 'with bookmarked brokerages' do
      let(:company) { create :company }
      let(:brokerage) { company.as_entity(:broker) }

      before do
        create :bookmark, :broker, company:, list:
      end

      it 'returns bookmarked brokerages' do
        expect(described_class::Query.call(Brokerage, user, [brokerage])).to eq [brokerage.id]
      end
    end
  end

  describe '#bookmarked?' do
    let(:company_one) { create :company }
    let(:carrier_one) { company_one.as_entity(:carrier) }
    let(:brokerage_one) { company_one.as_entity(:broker) }
    let(:company_two) { create :company }
    let(:carrier_two) { company_two.as_entity(:carrier) }
    let(:brokerage_two) { company_two.as_entity(:broker) }
    let(:entities) { [carrier_one, carrier_two, brokerage_one, brokerage_two] }

    context 'when user is blank' do
      let(:user) { nil }

      it 'returns false' do
        expect(context.bookmarked?(carrier_one)).to be false
      end
    end

    context 'when user is present' do
      before do
        create(:bookmark, :carrier, company: company_one, list:)
        create(:bookmark, :broker, company: company_two, list:)
      end

      it 'returns bookmark status' do
        expect(context.bookmarked?(carrier_one)).to be true
        expect(context.bookmarked?(carrier_two)).to be false
        expect(context.bookmarked?(brokerage_one)).to be false
        expect(context.bookmarked?(brokerage_two)).to be true
      end
    end
  end
end
