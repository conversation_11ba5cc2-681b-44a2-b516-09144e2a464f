require 'rails_helper'

RSpec.describe Companies::PreferredCity do
  describe '#name' do
    subject(:preferred_city) { described_class.new(company) }

    let(:city) { create :city, name: 'New York', state_code: 'NY' }
    let(:postal_code) { create :postal_code, code: '10001', acceptable_cities: }
    let(:company) { create :company, :with_city, city:, city_name: 'Brooklyn', postal_code: }
    let(:acceptable_cities) { [] }

    context 'when acceptable city is present' do
      let(:acceptable_cities) { ['Brooklyn'] }

      it 'returns acceptable city name' do
        expect(preferred_city.name).to eq('Brooklyn')
      end
    end

    context 'when acceptable city is not present' do
      it 'returns city name' do
        expect(preferred_city.name).to eq('New York')
      end
    end
  end
end
