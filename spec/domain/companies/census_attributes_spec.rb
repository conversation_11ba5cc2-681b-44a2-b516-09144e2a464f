require 'rails_helper'

RSpec.describe Companies::CensusAttributes do
  describe 'dba_name' do
    context 'when not applicable' do
      let(:census) { build_stubbed :census, dba_name: 'NA' }

      it 'returns nil' do
        expect(described_class.for(census)).to match hash_including('dba_name' => nil)
      end
    end

    context 'when present' do
      let(:census) { build_stubbed :census, dba_name: 'Elastic Transport' }

      it 'returns correct value' do
        expect(described_class.for(census)).to match hash_including('dba_name' => 'Elastic Transport')
      end
    end
  end

  describe 'carrier_operation' do
    context 'when crrhmintra is B' do
      let(:census) { build_stubbed :census, carrier_operation: 'B' }

      it 'returns correct value' do
        expect(described_class.for(census)).to match hash_including('carrier_operation' => 'intrastate')
      end
    end

    context 'when crrintra is C' do
      let(:census) { build_stubbed :census, carrier_operation: 'C' }

      it 'returns correct value' do
        expect(described_class.for(census)).to match hash_including('carrier_operation' => 'intrastate')
      end
    end

    context 'when crrinter is A' do
      let(:census) { build_stubbed :census, carrier_operation: 'A' }

      it 'returns correct value' do
        expect(described_class.for(census)).to match hash_including('carrier_operation' => 'interstate')
      end
    end
  end

  describe 'hazmat_indicator' do
    context 'when hm_ind is Y' do
      let(:census) { build_stubbed :census, hm_ind: 'Y' }

      it 'returns true' do
        expect(described_class.for(census)).to match hash_including('hazmat_indicator' => true)
      end
    end

    context 'when crrhmintra is B' do
      let(:census) { build_stubbed :census, carrier_operation: 'B' }

      it 'returns true' do
        expect(described_class.for(census)).to match hash_including('hazmat_indicator' => true)
      end
    end

    context 'when hm_ind is N' do
      let(:census) { build_stubbed :census, hm_ind: 'N' }

      it 'returns true' do
        expect(described_class.for(census)).to match hash_including('hazmat_indicator' => false)
      end
    end
  end

  describe 'safety_rating_date' do
    context 'when nil' do
      let(:census) { build_stubbed :census }

      it 'returns nil' do
        expect(described_class.for(census)).to match hash_including('safety_rating_date' => nil)
      end
    end

    context 'when present' do
      let(:census) { build_stubbed :census, safety_rating_date: '20221024' }

      it 'returns date' do
        expect(described_class.for(census)).to match hash_including('safety_rating_date' => Date.new(2022, 10, 24))
      end
    end
  end

  describe 'mcs_150_mileage' do
    context 'when nil' do
      let(:census) { build_stubbed :census }

      it 'returns nil' do
        expect(described_class.for(census)).to match hash_including('mcs_150_mileage' => nil)
      end
    end

    context 'when present' do
      let(:census) { build_stubbed :census, mcs150_mileage: '2567' }

      it 'returns decimal value' do
        expect(described_class.for(census)).to match hash_including('mcs_150_mileage' => BigDecimal('2567'))
      end
    end
  end
end
