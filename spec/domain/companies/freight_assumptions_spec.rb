require 'rails_helper'

RSpec.describe Companies::FreightAssumptions do
  shared_examples 'a registered freight' do |truck: nil, shipment: nil|
    it 'returns attributes for freight' do
      attributes = described_class.profile_attributes(company)
      expect(attributes[:truck_types]).to eq Array.wrap(truck).map { |t| truck_types(t).id }.presence
      expect(attributes[:shipment_types]).to eq Array.wrap(shipment).map { |s| shipment_types(s).id }.presence
    end
  end

  describe '::profile_attributes' do
    it_behaves_like 'a registered freight', truck: :tanker, shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_liqgas: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', truck: :auto_carrier, shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_motoveh: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', truck: :container, shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_intermodal: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', truck: :reefer do
      let(:census) { build_stubbed :census, crgo_meat: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', truck: :reefer do
      let(:census) { build_stubbed :census, crgo_coldfood: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_passengers: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', shipment: :parcel do
      let(:census) { build_stubbed :census, crgo_usmail: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_chem: 'X' }
      let(:company) { build_stubbed :company, census: }
    end

    it_behaves_like 'a registered freight', shipment: :ftl do
      let(:census) { build_stubbed :census, crgo_waterwell: 'X' }
      let(:company) { build_stubbed :company, census: }
    end
  end
end
