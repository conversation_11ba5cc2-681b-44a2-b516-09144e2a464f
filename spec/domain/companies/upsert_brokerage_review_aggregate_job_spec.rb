require 'rails_helper'

RSpec.describe Companies::UpsertBrokerageReviewAggregateJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:brokerage) { company.as_entity(:broker) }

    it 'delegates' do
      allow(Companies::UpsertBrokerageReviewAggregate).to receive(:call)
      described_class.perform_inline(brokerage.to_gid.to_s)
      expect(Companies::UpsertBrokerageReviewAggregate).to have_received(:call).with(brokerage)
    end
  end
end
