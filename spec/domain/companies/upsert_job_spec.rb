require 'rails_helper'

RSpec.describe Companies::UpsertJob do
  describe '::batch_load' do
    let!(:census_one) { create :census, mcs150_date: Time.zone.now }
    let!(:census_two) { create :census, mcs150_date: 1.day.ago }
    let!(:census_three) { create :census, mcs150_date: 2.days.ago }

    let!(:company_one) { create :company, census: census_one, census_change_date: census_one.mcs150_date }
    let!(:company_two) { create :company, census: census_two }

    it 'enqueues job for outdated companies' do
      described_class.batch_load(model: Census, finish: Census.maximum(:id))
      expect(described_class.jobs.pluck('args').flatten).to contain_exactly(census_two.id, census_three.id)
    end
  end

  describe '#perform' do
    let(:census) { create :census }

    before do
      allow(Companies::Upsert).to receive(:call)
    end

    it 'delegates to service object' do
      described_class.perform_inline(census.id)
      expect(Companies::Upsert).to have_received(:call).with(census)
    end
  end
end
