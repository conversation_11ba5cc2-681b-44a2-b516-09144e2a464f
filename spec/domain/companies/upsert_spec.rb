require 'rails_helper'

RSpec.describe Companies::Upsert, :elasticsearch do
  describe '#call' do
    subject(:callable) { described_class.new(census) }

    let(:census) do
      create :census, legal_name: 'SHIPEX', phy_zip: '84105-1234', carship: 'B;C', mcs150_date: '20240211 1205'
    end

    let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
    let(:postal_code) { create :postal_code, code: '84105' }

    before do
      create :cities_postal_code, city:, postal_code:
    end

    context 'when company does not exist' do
      it 'inserts new record' do
        expect { callable.call }.to change(Company, :count).by(1)
      end

      it 'creates carrier profile' do
        expect { callable.call }.to change(CarrierProfile, :count).by(1)
      end

      it 'creates brokerage profile' do
        expect { callable.call }.to change(BrokerageProfile, :count).by(1)
      end

      it 'sets correct attributes' do
        callable.call
        company = Company.find_by(dot_number: census.dot_number)
        expect(company).to have_attributes(name: 'Shipex', slug: 'shipex', zip: '84105', city:)
        expect(company.entity_types.pluck(:entity_type)).to match_array %w(broker carrier)
      end

      it 'triggers callbacks', :wisper do
        expect { callable.call }.not_to raise_error
      end
    end

    context 'when company exists' do
      let!(:company) { create :company, census:, legal_name: 'FR8 DOG' }

      before do
        company.entity_types.create(entity_type: 'carrier')
        company.entity_types.create(entity_type: 'shipper')
      end

      it 'does not create new record' do
        expect { callable.call }.not_to change(Company, :count)
      end

      it 'syncs entity types' do
        callable.call
        expect(company.entity_types.pluck(:entity_type)).to match_array %w(broker carrier)
      end
    end
  end
end
