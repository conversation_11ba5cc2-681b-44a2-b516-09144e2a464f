require 'rails_helper'

RSpec.describe Companies::UpsertRelatedJob do
  let(:carrier_profile) { create :carrier_profile }
  let(:carrier) { carrier_profile.carrier }

  describe '::batch_load' do
    it 'queues jobs' do
      described_class.batch_load model: Carrier, finish: carrier.id
      expect(described_class.jobs.size).to eq 1
      expect(described_class.jobs.pick('args')).to eq [carrier.id]
    end
  end

  describe '#perform' do
    before do
      allow(Companies::UpsertRelated).to receive(:call)
    end

    it 'calls Companies::UpsertRelated' do
      described_class.perform_inline(carrier.id)
      expect(Companies::UpsertRelated).to have_received(:call).with(carrier)
    end
  end
end
