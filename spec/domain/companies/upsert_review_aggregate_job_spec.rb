require 'rails_helper'

RSpec.describe Companies::UpsertReviewAggregateJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:carrier) { company.as_entity(:carrier) }

    it 'delegates' do
      allow(Companies::UpsertReviewAggregate).to receive(:call)
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(Companies::UpsertReviewAggregate).to have_received(:call).with(carrier)
    end
  end
end
