require 'rails_helper'

RSpec.describe Companies::InitializeBlankProfileContacts do
  describe '#call' do
    subject(:service) { described_class.new(company) }

    let(:census) { create :census, carship: }
    let(:company) { create :company, census: }

    before do
      allow(CarrierProfileContacts::InitializeBlankProfile).to receive(:call)
      allow(BrokerageProfileContacts::InitializeBlankProfile).to receive(:call)
    end

    context 'when the company is a carrier' do
      let(:carship) { 'C' }
      let(:carrier) { company.as_entity(:carrier) }

      it 'initializes blank carrier profile contacts' do
        service.call
        expect(CarrierProfileContacts::InitializeBlankProfile).to have_received(:call).with(carrier)
      end
    end

    context 'when the company is a broker' do
      let(:carship) { 'B' }
      let(:brokerage) { company.as_entity(:broker) }

      it 'initializes blank brokerage profile contacts' do
        service.call
        expect(BrokerageProfileContacts::InitializeBlankProfile).to have_received(:call).with(brokerage)
      end
    end
  end
end
