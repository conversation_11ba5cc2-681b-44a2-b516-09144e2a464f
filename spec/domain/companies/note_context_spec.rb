require 'rails_helper'

RSpec.describe Companies::NoteContext do
  subject(:context) { described_class.new(user, entities) }

  describe '#note?' do
    let(:company_one) { create :company }
    let(:carrier_one) { company_one.as_entity(:carrier) }
    let(:company_two) { create :company }
    let(:carrier_two) { company_two.as_entity(:carrier) }
    let(:entities) { [carrier_one, carrier_two] }

    before do
      CompanyNote.create company: company_one, user:, entity_type: 'carrier'
    end

    context 'when user is blank' do
      let(:user) { nil }

      it 'returns false' do
        expect(context.note?(carrier_one)).to be false
      end
    end

    context 'when user is present' do
      let(:user) { create :user }

      it 'returns true for bookmarked carrier' do
        expect(context.note?(carrier_one)).to be true
      end

      it 'returns false for other carrier' do
        expect(context.note?(carrier_two)).to be false
      end
    end
  end
end
