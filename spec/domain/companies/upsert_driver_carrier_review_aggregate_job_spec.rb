require 'rails_helper'

RSpec.describe Companies::UpsertDriverCarrierReviewAggregateJob do
  describe '#perform' do
    let(:company) { create :company }
    let(:carrier) { company.as_entity(:carrier) }

    it 'delegates' do
      allow(Companies::UpsertDriverCarrierReviewAggregate).to receive(:call)
      described_class.perform_inline(carrier.to_gid.to_s)
      expect(Companies::UpsertDriverCarrierReviewAggregate).to have_received(:call).with(carrier)
    end
  end
end
