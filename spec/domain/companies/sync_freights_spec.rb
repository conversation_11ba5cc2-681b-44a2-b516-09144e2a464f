require 'rails_helper'

RSpec.describe Companies::SyncFreights do
  subject(:syncer) { described_class.new(company) }

  let(:company) { create :company, dot_number: }

  before do
    company.companies_freights.create(freight: freights(:genfreight))
    company.companies_freights.create(freight: freights(:livestock))
  end

  context 'when census is missing' do
    let(:dot_number) { Faker::Number.normal }

    it 'removes existing freights' do
      syncer.call
      expect(company.reload.freights).to be_empty
    end
  end

  context 'when census is present' do
    let(:census) { create :census, crgo_genfreight: 'X', crgo_household: 'X' }
    let(:dot_number) { census.dot_number }

    it 'syncs with census' do
      syncer.call
      expect(company.reload.freights).to contain_exactly(freights(:genfreight), freights(:household))
    end

    context 'when census has passengers_only' do
      let(:census) { create :census, crgo_passengers: 'X' }

      it 'sets passengers_only' do
        syncer.call
        expect(company.reload.passengers_only).to be_truthy
      end
    end
  end
end
