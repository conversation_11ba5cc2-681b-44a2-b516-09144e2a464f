require 'rails_helper'

RSpec.describe Companies::SyncBookmarks do
  describe '#call' do
    subject(:syncer) { described_class.new(company:, user:, added_list_ids:, entity_type:) }

    let(:user) { create :user, :broker }
    let(:current_list) { create :list, user: }
    let(:new_list) { create :list, user: }
    let(:added_list_ids) { [current_list.id, new_list.id] }

    context 'when entity_type is carrier' do
      let(:company) { create :company, :carrier }
      let(:entity_type) { 'carrier' }

      before do
        create :bookmark, :carrier, company:, list_id: current_list.id
      end

      it 'creates new bookmark' do
        expect { syncer.call }.to change(Bookmark, :count).by(1)
      end

      it 'sets the correct attributes' do
        syncer.call
        bookmark = Bookmark.find_by(list: new_list, company:, entity_type: 'carrier')
        expect(bookmark).to be_present
      end
    end

    context 'when entity_type is broker' do
      let(:company) { create :company, :broker }
      let(:entity_type) { 'broker' }

      before do
        create :bookmark, :broker, company:, list_id: current_list.id
      end

      it 'creates new bookmark' do
        expect { syncer.call }.to change(Bookmark, :count).by(1)
      end

      it 'sets the correct attributes' do
        syncer.call
        bookmark = Bookmark.find_by(list: new_list, company:, entity_type: 'broker')
        expect(bookmark).to be_present
      end
    end
  end
end
