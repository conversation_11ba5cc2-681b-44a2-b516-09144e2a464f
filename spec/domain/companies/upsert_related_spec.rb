require 'rails_helper'

RSpec.describe Companies::UpsertRelated do
  subject(:upserter) { described_class.new(carrier) }

  let(:company) { create :company }
  let(:carrier) { company.as_entity(:carrier) }

  describe '#call' do
    context 'when there are no related companies' do
      before do
        allow(Carriers::FindRelated).to receive(:call).and_return([])
      end

      it 'does not raise error' do
        expect { upserter.call }.not_to raise_error
      end
    end

    context 'when there are related companies' do
      let(:related_company) { create :company }

      before do
        CompaniesRelatedCompany.create! company:, related_company:
      end

      context 'when the related company is no longer related' do
        before do
          allow(Carriers::FindRelated).to receive(:call).with(carrier).and_return([])
        end

        it 'deletes the related company' do
          expect { upserter.call }.to change(CompaniesRelatedCompany, :count).by(-1)
        end
      end

      context 'when the related company is still related' do
        before do
          allow(Carriers::FindRelated).to receive(:call).with(carrier).and_return([related_company])
        end

        it 'does not delete the related company' do
          expect { upserter.call }.not_to change(CompaniesRelatedCompany, :count)
        end
      end

      context 'when there is a new related company' do
        let(:new_related_company) { create :company }

        before do
          allow(Carriers::FindRelated).to receive(:call).with(carrier)
                                            .and_return([related_company, new_related_company])
        end

        it 'creates the related company' do
          expect { upserter.call }.to change(CompaniesRelatedCompany, :count).by(1)
        end
      end
    end
  end
end
