require 'rails_helper'

RSpec.describe Companies::SyncCity do
  subject(:syncer) { described_class.new(company) }

  let(:company) { create :company, :carrier, zip: '60601', country_code: 'US' }

  let(:chicago) { create :city, name: 'Chicago', state_code: 'IL' }
  let(:boston) { create :city, name: 'Boston', state_code: 'MA' }

  describe '#call' do
    context 'when company city_id is blank' do
      let(:company) { create :company, :carrier, zip: '60601', country_code: 'US', city: nil }

      context 'when city is found by postal code' do
        before do
          create(:postal_code, code: '60601').then do |postal_code|
            create(:cities_postal_code, city: chicago, postal_code:)
          end
        end

        it 'updates company city_id' do
          syncer.call
          expect(company.city_id).to eq(chicago.id)
        end
      end

      context 'when city is not found by postal code' do
        it 'does not update company city_id' do
          syncer.call
          expect(company.city_id).to be_nil
        end
      end
    end

    context 'when company city_id is not blank' do
      before do
        company.update_columns(city_id: boston.id)
      end

      context 'when city is found by postal code' do
        before do
          create(:postal_code, code: '60601').then do |postal_code|
            create(:cities_postal_code, city: chicago, postal_code:)
          end
        end

        it 'updates company city_id' do
          syncer.call
          expect(company.city_id).to eq(chicago.id)
        end
      end

      context 'when city is not found by postal code' do
        it 'nilifies company city_id' do
          syncer.call
          expect(company.city_id).to be_nil
        end
      end
    end

    context 'when company zip is blank' do
      let(:company) { create :company, :carrier, zip: nil, country_code: 'US' }

      it 'nilifies company city_id' do
        syncer.call
        expect(company.city_id).to be_nil
      end
    end

    context 'when company country_code is blank' do
      let(:company) { create :company, :carrier, zip: '60601', country_code: nil }

      it 'nilifies company city_id' do
        syncer.call
        expect(company.city_id).to be_nil
      end
    end
  end
end
