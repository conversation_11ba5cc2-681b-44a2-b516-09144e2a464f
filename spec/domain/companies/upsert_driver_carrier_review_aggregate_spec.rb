require 'rails_helper'

RSpec.describe Companies::UpsertDriverCarrierReviewAggregate do
  subject(:updater) { described_class.new(carrier) }

  describe '#call' do
    let(:company) { create :company }
    let(:carrier) { company.as_entity(:carrier) }
    let!(:review) { create :driver_carrier_review, :approved, company: }

    context 'when aggregate record exists' do
      before do
        DriverCarrierReviewsAggregate.create company:, star_rating: 4, review_count: 3
      end

      it 'updates existing record' do
        expect { updater.call }.not_to change(ReviewsAggregate, :count)
        expect(DriverCarrierReviewsAggregate.find_by(company:)).to have_attributes star_rating: 5, review_count: 1
      end

      context 'when review is rejected' do
        before do
          review.rejection_reason = 'LACK_DETAIL'
          review.reject!
        end

        it 'updates existing record' do
          expect { updater.call }.not_to change(DriverCarrierReviewsAggregate, :count)
          expect(DriverCarrierReviewsAggregate.find_by(company:)).to have_attributes star_rating: nil, review_count: 0
        end
      end
    end

    context 'when aggregate record does not exist' do
      it 'creates new record' do
        expect { updater.call }.to change(DriverCarrierReviewsAggregate, :count).by(1)
        expect(DriverCarrierReviewsAggregate.find_by(company:)).to have_attributes star_rating: 5, review_count: 1
      end
    end
  end
end
