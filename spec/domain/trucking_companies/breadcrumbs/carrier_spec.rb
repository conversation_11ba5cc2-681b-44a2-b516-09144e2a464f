require 'rails_helper'

RSpec.describe TruckingCompanies::Breadcrumbs::Carrier do
  subject(:breadcrumb) { described_class.new(carrier) }

  let(:city) { create :city, name: 'Or<PERSON>', state_code: 'UT' }
  let(:carrier) { instance_double Carrier, name: 'Putnik Express', city: }

  describe '#items' do
    it 'returns all ancestor items including self' do
      expect(breadcrumb.items).to(
        eq(
          [
            { href: 'http://www.lvh.me:3001/trucking-companies', text: 'Trucking Companies' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states', text: 'United States' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah', text: 'Utah' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah/orem', text: 'Orem' },
            { text: 'Putnik Express' }
          ]
        )
      )
    end
  end
end
