require 'rails_helper'

RSpec.describe TruckingCompanies::Breadcrumbs::City do
  subject(:breadcrumb) { described_class.new(city) }

  let(:city) { create :city, name: 'Orem', state_code: 'UT' }

  describe '#items' do
    it 'returns all ancestor items including self' do
      expect(breadcrumb.items).to(
        eq(
          [
            { href: 'http://www.lvh.me:3001/trucking-companies', text: 'Trucking Companies' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states', text: 'United States' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah', text: 'Utah' },
            { href: 'http://www.lvh.me:3001/trucking-companies/united-states/utah/orem', text: 'Orem' }
          ]
        )
      )
    end
  end
end
