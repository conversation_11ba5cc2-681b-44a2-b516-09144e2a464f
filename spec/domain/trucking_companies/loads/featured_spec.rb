require 'rails_helper'

RSpec.describe TruckingCompanies::Loads::Featured do
  describe '#all' do
    context 'when LOAD_SLUGS is set' do
      it 'returns all featured loads' do
        ClimateControl.modify LOAD_SLUGS: 'flatbed,less-than-truckload' do
          expect(described_class.instance.all).to eq([truck_types(:flatbed), shipment_types(:ltl)])
        end
      end
    end

    context 'when LOAD_SLUGS is not set' do
      it 'returns an empty array' do
        expect(described_class.instance.all).to eq([])
      end
    end
  end
end
