require 'rails_helper'

RSpec.describe TruckingCompanies::Metadata do
  subject(:metadata) { described_class.new(seo_name:, seo_type:, location:, city:) }

  let(:seo_name) { nil }
  let(:seo_type) { 'Trucking Companies' }
  let(:location) { 'Chicago, IL' }
  let(:city) { nil }

  describe '#title' do
    it 'returns the title' do
      travel_to Time.zone.local(2025, 3, 1) do
        expect(metadata.title).to eq('Top Trucking Companies in Chicago, IL - Mar 2025 Reviews | CarrierSource')
      end
    end
  end

  describe '#title_html' do
    it 'returns the title' do
      expect(metadata.title_html).to eq('Top <strong></strong> Trucking Companies in <strong>Chicago, IL</strong>')
    end
  end

  describe '#description' do
    context 'when city is present' do
      let(:city) { create :city, name: 'Chicago', state_code: 'IL', country_code: 'US' }

      let(:description) do
        <<~DESCRIPTION.squish
          Chicago, a pivotal hub in regional transportation, excels in trucking services, bolstering industries like
          agriculture, manufacturing, and logistics. Strategically positioned, the city leverages major highways such as
          I-90, I-94, and I-55 to facilitate efficient cargo movement. Its robust trucking network underscores Chicago's
          vital role in connecting the Midwest's economic arteries.
        DESCRIPTION
      end

      before do
        CitiesMetadatum.create(city:, description:)
      end

      it 'returns the description' do
        expect(metadata.description).to eq description
      end
    end

    context 'when city is not present' do
      it 'returns the default description' do
        expect(metadata.description).to eq <<~DESCRIPTION.squish
          List of the top Trucking Companies in Chicago, IL based on real user reviews. Use our advanced search filters
          to prospect for the best carriers.
        DESCRIPTION
      end
    end
  end
end
