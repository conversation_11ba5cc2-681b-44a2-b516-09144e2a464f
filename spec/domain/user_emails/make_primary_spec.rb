require 'rails_helper'

RSpec.describe UserEmails::MakePrimary do
  describe '::run' do
    let(:user) { create :user }
    let(:user_email) { create :user_email, :verified, user: }

    context 'when the email is already in use by another user' do
      before do
        create :user, email: user_email.email
      end

      it 'returns an error' do
        outcome = described_class.run(user_email:)
        expect(outcome).to be_failure
        expect(outcome.failure.errors.full_messages.to_sentence).to eq 'Email has already been taken'
      end
    end

    context 'when the email is not in use by another user' do
      it 'makes the email the primary email' do
        outcome = described_class.run(user_email:)

        expect(outcome).to be_success
        expect(user.reload.email).to eq(user_email.email)
      end
    end

    context 'when the email is not verified' do
      let(:user_email) { create :user_email, user: }

      it 'returns an error' do
        outcome = described_class.run(user_email:)
        expect(outcome).to be_failure
        expect(outcome.failure.errors.full_messages.to_sentence).to eq 'Email must be verified'
      end
    end
  end
end
