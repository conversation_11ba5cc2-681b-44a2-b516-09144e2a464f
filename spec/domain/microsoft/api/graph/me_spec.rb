require 'rails_helper'

RSpec.describe Microsoft::Api::Graph::Me do
  subject(:client) { described_class.new(token:, raise_errors:) }

  let(:token) { 'token' }
  let(:raise_errors) { false }

  describe '#list_teams' do
    before do
      stub_request(:get, 'https://graph.microsoft.com/v1.0/me/joinedTeams')
        .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when request is successful' do
      let(:status) { 200 }
      let(:body) { Rails.root.join('spec/fixtures/microsoft/graph/me/joined_teams.json').read }

      it 'returns teams' do
        expect(client.list_teams.body.to_s).to eq(body)
      end
    end

    context 'when request is not successful' do
      let(:status) { 400 }
      let(:body) { '{}' }

      context 'when raise_errors is true' do
        let(:raise_errors) { true }

        it 'raises an error' do
          expect { client.list_teams }.to raise_error Microsoft::Api::Error
        end
      end

      context 'when raise_errors is false' do
        it 'returns response' do
          expect(client.list_teams).to be_a HTTP::Response
        end
      end
    end
  end
end
