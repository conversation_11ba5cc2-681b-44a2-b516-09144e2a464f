require 'rails_helper'

RSpec.describe Slack::PostEventToIntegration do
  subject(:callable) { described_class.new(notification:, event:) }

  let(:notification) { create :analytics_company_event_feed_notification, :slack }
  let(:event) { create :analytics_shipper_event }

  let(:daily_shipper_message) do
    Analytics::Integrations::DailyShipperMessage.new(notification.integration, event.analytics_company)
  end

  let(:lock_key) { daily_shipper_message.lock_key(event.time) }

  describe '#call?' do
    context 'when the lock is not acquired' do
      it 'returns true' do
        expect(callable.call?).to be true
      end
    end

    context 'when the lock is acquired' do
      before do
        CarrierSource.redlock.lock(lock_key, 6000)
      end

      it 'returns false' do
        expect(callable.call?).to be false
      end
    end
  end

  describe '#call' do
    let(:previous_event) do
      create :analytics_shipper_event, type: 'carrier.profile.viewed', analytics_company: event.analytics_company
    end

    let(:body) { Rails.root.join('spec/fixtures/slack/chat/postMessage.json').read }

    before do
      stub_request(:post, 'https://slack.com/api/chat.postMessage')
        .to_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, 'https://slack.com/api/chat.update')
        .to_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })

      daily_shipper_message.add_event(previous_event)
    end

    context 'when the lock is not acquired' do
      it 'raises error' do
        lock_info = CarrierSource.redlock.lock(lock_key, 6000)
        expect { callable.call }.to raise_error 'Unable to acquire lock'
        CarrierSource.redlock.unlock(lock_info)
      end
    end

    context 'when the lock is acquired' do
      context 'when message has been previously sent' do
        before do
          CarrierSource.redis.call(
            'SET', "#{lock_key}:message_id", '1503435956.000247', 'NX', 'EXAT', event.time.end_of_day.to_i
          )
        end

        it 'updates the message' do
          callable.call
          expect(WebMock).to have_requested(:post, 'https://slack.com/api/chat.update')
        end
      end

      context 'when message has not been previously sent' do
        it 'sends messaage to Slack' do
          callable.call
          expect(WebMock).to have_requested(:post, 'https://slack.com/api/chat.postMessage')
        end
      end
    end

    it 'logs the response' do
      expect { callable.call }.to change(Analytics::CompanyEventFeedNotificationLog, :count).by(1)
    end
  end
end
