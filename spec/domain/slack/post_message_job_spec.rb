require 'rails_helper'

RSpec.describe Slack::PostMessageJob do
  describe '#perform' do
    let(:channel) { '#channel' }
    let(:text) { 'text' }
    let(:attachments) { [] }

    it 'calls Slack::PostMessage' do
      allow(Slack::PostMessage).to receive(:call)
      described_class.perform_inline(channel, text, attachments)
      expect(Slack::PostMessage).to have_received(:call).with(channel:, text:, attachments:)
    end
  end
end
