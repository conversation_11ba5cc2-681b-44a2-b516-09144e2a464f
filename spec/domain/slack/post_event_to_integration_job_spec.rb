require 'rails_helper'

RSpec.describe Slack::PostEventToIntegrationJob do
  describe '#perform' do
    let(:notification) { create :analytics_company_event_feed_notification, :slack }
    let(:event) { create :analytics_shipper_event }
    let(:callable) { instance_double Slack::PostEventToIntegration }

    before do
      allow(Slack::PostEventToIntegration).to receive(:new).and_return(callable)
      allow(callable).to receive(:call)
    end

    context 'when the lock is not acquired' do
      before do
        allow(callable).to receive(:call?).and_return(true)
      end

      it 'calls Slack::PostEventToIntegration' do
        described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
        expect(callable).to have_received(:call)
      end
    end

    context 'when the lock is acquired' do
      before do
        allow(callable).to receive(:call?).and_return(false)
      end

      it 'retries the job after 10 seconds' do
        described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
        expect(callable).not_to have_received(:call)
        expect(described_class.jobs.size).to eq 1
      end
    end
  end
end
