require 'rails_helper'

RSpec.describe CarrierProfileContacts::InitializeBlankProfile do
  subject(:callable) { described_class.new(carrier) }

  describe '#call' do
    let(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, email: }
    let(:carrier) { company.as_entity(:carrier) }
    let(:email) { Faker::Internet.email }

    context 'when the carrier profile is nil' do
      it 'does not create a contact' do
        expect { callable.call }.not_to change(CarrierProfileContact, :count)
      end
    end

    context 'when the carrier profile has no contacts' do
      before { carrier_profile }

      context 'when the company has an email' do
        it 'creates a contact with the company email' do
          expect { callable.call }.to change(CarrierProfileContact, :count).by(1)
        end
      end

      context 'when the company has no email' do
        let(:email) { nil }

        it 'does not create a contact' do
          expect { callable.call }.not_to change(CarrierProfileContact, :count)
        end
      end
    end

    context 'when the carrier profile has contacts' do
      before do
        create :carrier_profile_contact, carrier_profile:
      end

      it 'does not create a contact' do
        expect { callable.call }.not_to change(CarrierProfileContact, :count)
      end
    end
  end
end
