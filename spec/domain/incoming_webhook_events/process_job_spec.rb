require 'rails_helper'

RSpec.describe IncomingWebhookEvents::ProcessJob do
  describe '#perform' do
    let(:data) do
      JSON.parse(
        Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
      )
    end

    let(:event) do
      IncomingWebhookEvent.create source: 'stripe', data:, external_id: data['id']
    end

    before do
      allow(IncomingWebhookEvents::Process).to receive(:call)
    end

    context 'when not in processing state' do
      it 'does not process' do
        described_class.perform_inline(event.id)
        expect(IncomingWebhookEvents::Process).not_to have_received(:call)
      end
    end

    context 'when in processing state' do
      before { event.process! }

      it 'processes' do
        described_class.perform_inline(event.id)
        expect(IncomingWebhookEvents::Process).to have_received(:call)
      end
    end

    context 'when processing fails' do
      it 'marks the event as failed' do
        msg = { 'args' => [event.id] }
        exception = ArgumentError.new('Event not valid')
        event.process!
        described_class::FAILURE_HANDLER.call(msg, exception)
        expect(event.reload).to have_attributes status: 'failed', processing_errors: 'Event not valid'
      end
    end
  end
end
