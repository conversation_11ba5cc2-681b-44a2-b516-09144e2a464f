require 'rails_helper'

RSpec.describe IncomingWebhookEvents::Process do
  let(:data) do
    JSON.parse(
      Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
    )
  end

  let(:event) do
    IncomingWebhookEvent.create source: 'stripe', data:, external_id: data['id'], status: 'processing'
  end

  describe '#call' do
    let(:processor) { instance_double IncomingWebhookEvents::Processors::Stripe }

    before do
      allow(IncomingWebhookEvents::Processors::Stripe).to receive(:new).and_return(processor)
    end

    context 'when processing succeeds' do
      before do
        allow(processor).to receive(:process).and_return(true)
      end

      it 'marks the event as processed' do
        described_class.call(event)
        expect(event).to be_processed
      end
    end

    context 'when processing fails' do
      before do
        allow(processor).to receive(:process).and_raise ArgumentError, 'Event not valid'
      end

      it 'raises error' do
        expect { described_class.call(event) }.to raise_error ArgumentError, 'Event not valid'
      end
    end
  end
end
