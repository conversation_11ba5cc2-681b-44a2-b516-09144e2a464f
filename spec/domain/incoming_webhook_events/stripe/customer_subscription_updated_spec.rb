require 'rails_helper'

RSpec.describe IncomingWebhookEvents::Stripe::CustomerSubscriptionUpdated do
  subject(:webhook) { described_class.new(event) }

  let(:data) do
    JSON.parse(
      Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.updated.json').read
    )
  end

  let(:event) { Stripe::Event.construct_from(data) }

  before do
    allow(Stripe::Product).to receive(:retrieve).with('prod_MQ77Ys8Ehxkvjx').and_return(product)
    allow(Subscriptions::UpdateFromStripeSubscription).to receive :call
  end

  describe '#call' do
    context 'when product has carrier persona' do
      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/carrier/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      it 'updates carrier subscription' do
        webhook.call
        expect(Subscriptions::UpdateFromStripeSubscription).to have_received(:call)
                                                                 .with(subscription: instance_of(Stripe::Subscription))
      end
    end

    context 'when product has broker persona' do
      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/broker/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      it 'updates user subscription' do
        webhook.call
        expect(Subscriptions::UpdateFromStripeSubscription).to have_received(:call)
                                                                 .with(subscription: instance_of(Stripe::Subscription))
      end
    end

    context 'when product persona is not supported' do
      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/data.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      it 'does not raise' do
        expect { webhook.call }.not_to raise_error
      end
    end
  end
end
