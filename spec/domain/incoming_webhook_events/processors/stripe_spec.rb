require 'rails_helper'

RSpec.describe IncomingWebhookEvents::Processors::Stripe do
  subject(:processor) { described_class.new(event) }

  describe '#process' do
    let(:event) do
      IncomingWebhookEvent.create source: 'stripe', data:, external_id: data['id']
    end

    context 'when event type is registered' do
      let(:data) do
        JSON.parse(
          Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
        )
      end

      it 'delegates' do
        allow(IncomingWebhookEvents::Stripe::CustomerSubscriptionCreated).to receive(:call)
        processor.process
        expect(IncomingWebhookEvents::Stripe::CustomerSubscriptionCreated).to have_received :call
      end
    end

    context 'when event type is not registered' do
      let(:data) do
        JSON.parse(Rails.root.join('spec/fixtures/webhooks/stripe/charge.succeeded.json').read)
      end

      it 'does nothing' do
        expect { processor.process }.not_to raise_error
      end
    end
  end
end
