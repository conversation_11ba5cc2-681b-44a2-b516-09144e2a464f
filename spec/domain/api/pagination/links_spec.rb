require 'rails_helper'

RSpec.describe Api::Pagination::Links do
  subject(:links) { described_class.new(response:, original_url:, size:) }

  describe '#build' do
    let(:response) do
      { 'hits' => { 'hits' => [{ 'sort' => [1, 2] }, { 'sort' => [1, 3] }] } }
    end

    let(:original_url) { 'https://www.example.com?a=1' }

    context 'when next_cursor is present' do
      let(:size) { 2 }

      it 'returns the next url' do
        expect(links.build).to eq({ next: 'https://www.example.com?a=1&page%5Bcursor%5D=WzEsM10' })
      end
    end

    context 'when next_cursor is blank' do
      let(:size) { 3 }

      it 'returns empty hash' do
        expect(links.build).to eq({})
      end
    end
  end
end
