require 'rails_helper'

RSpec.describe Api::Params::CarrierFilter do
  subject(:params) { described_class.new(input) }

  let(:input) do
    {
      carrier_operation: 'interstate',
      safety_rating: 'satisfactory',
      freight: 'beverages',
      truck_type: 'dry-van',
      shipment_type: 'full-truckload',
      specialized_service: 'drayage'
    }
  end

  it 'has the correct attributes' do
    expect(params.carrier_operation).to eq(['interstate'])
    expect(params.safety_rating).to eq(['satisfactory'])
    expect(params.freight).to eq([freights(:beverages).id])
    expect(params.truck_type).to eq([truck_types(:van).id])
    expect(params.shipment_type).to eq([shipment_types(:ftl).id])
    expect(params.specialized_service).to eq([specialized_services(:drayage).id])
  end
end
