require 'rails_helper'

RSpec.describe Api::ElasticQuery, :elasticsearch do
  subject(:query) { described_class.new(Carrier, { sort: [{ cs_score: :desc }] }, params) }

  let(:profile_one) { create :carrier_profile, cs_score: 90 }
  let(:profile_two) { create :carrier_profile, cs_score: 80 }
  let(:profile_three) { create :carrier_profile, cs_score: 70 }
  let!(:carrier_one) { profile_one.carrier }
  let!(:carrier_two) { profile_two.carrier }
  let!(:carrier_three) { profile_three.carrier }

  before do
    Carrier.es.import force: true, refresh: true
  end

  describe '#records' do
    context 'with pagination' do
      let(:params) { { page: { size: 2 } } }

      it 'returns records' do
        expect(query.records.to_a).to eq [carrier_one, carrier_two]
      end

      it 'can fetch next page' do
        cursor = Api::Pagination::Cursor.from_response(query.response, size: query.size)
        new_query = described_class.new(Carrier, { sort: [{ cs_score: :desc }] }, page: { cursor:, size: 2 })
        expect(new_query.records.to_a).to eq [carrier_three]
      end
    end
  end
end
