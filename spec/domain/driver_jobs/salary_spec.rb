require 'rails_helper'

RSpec.describe DriverJobs::Salary do
  subject(:salary) { described_class.new(driver_job) }

  describe '#to_s' do
    context 'when salary_min and salary_max are present' do
      let(:driver_job) { build_stubbed(:driver_job, salary_min: 50_000, salary_max: 60_000, salary_unit: 'year') }

      it 'returns a formatted string' do
        expect(salary.to_s).to eq '$50,000 - $60,000 per year'
      end

      context 'when salary_min and salary_max are the same' do
        let(:driver_job) { build_stubbed(:driver_job, salary_min: 50_000, salary_max: 50_000, salary_unit: 'year') }

        it 'returns a formatted string' do
          expect(salary.to_s).to eq '$50,000 per year'
        end
      end
    end

    context 'when salary_min is present' do
      let(:driver_job) { build_stubbed(:driver_job, salary_min: 50_000, salary_max: nil, salary_unit: 'year') }

      it 'returns a formatted string' do
        expect(salary.to_s).to eq 'Starting at $50,000 per year'
      end
    end

    context 'when salary_max is present' do
      let(:driver_job) { build_stubbed(:driver_job, salary_min: nil, salary_max: 60_000, salary_unit: 'year') }

      it 'returns a formatted string' do
        expect(salary.to_s).to eq 'Up to $60,000 per year'
      end
    end

    context 'when salary_min and salary_max are not present' do
      let(:driver_job) { build_stubbed(:driver_job, salary_min: nil, salary_max: nil, salary_unit: 'year') }

      it 'returns nil' do
        expect(salary.to_s).to be_nil
      end
    end
  end
end
