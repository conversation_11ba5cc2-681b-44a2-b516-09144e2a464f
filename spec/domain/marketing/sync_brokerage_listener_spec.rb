require 'rails_helper'

RSpec.describe Marketing::SyncBrokerageListener, :wisper do
  let(:company) { create :company, :broker, :with_city }
  let!(:profile) { create :brokerage_profile, company: }

  context 'when the model is a brokerage profile user' do
    let(:profile_user) { build :brokerage_profile_user, :pending, brokerage_profile: profile }

    context 'when not verified' do
      it 'does not queue job' do
        expect { profile_user.save }.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
      end
    end

    context 'when verified' do
      before { profile_user.save! }

      it 'queues job' do
        expect { profile_user.verify! }.to change { ActiveCampaign::SyncCompanyJob.jobs.size }.by(1)
      end
    end
  end

  context 'when the model is a brokerage profile' do
    context 'when the profile has been claimed' do
      before do
        create :brokerage_profile_user, :verified, brokerage_profile: profile
      end

      context 'when changing a tracked attribute' do
        it 'queues job' do
          expect do
            profile.reload.update(website_url: Faker::Internet.url)
          end.to change { ActiveCampaign::SyncCompanyJob.jobs.size }.by(1)
        end
      end

      context 'when changing an untracked attribute' do
        it 'does not queue job' do
          expect do
            profile.reload.update(cs_score: 13.2)
          end.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
        end
      end
    end

    context 'when the profile has not been claimed' do
      it 'does not queue job' do
        expect do
          profile.reload.update(cs_score: 14.8)
        end.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
      end
    end
  end

  context 'when the model is a company' do
    context 'when the profile has been claimed' do
      before do
        create :brokerage_profile_user, :verified, brokerage_profile: profile
      end

      it 'queues job' do
        expect do
          company.reload.update(name: Faker::Company.name)
        end.to change { ActiveCampaign::SyncCompanyJob.jobs.size }.by(1)
      end
    end

    context 'when the profile has not been claimed' do
      it 'does not queue job' do
        expect do
          company.reload.update(name: Faker::Company.name)
        end.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
      end
    end

    context 'when the company is not indexable' do
      let(:company) { create :company, :broker, city: nil }

      before do
        create :brokerage_profile_user, :verified, brokerage_profile: profile
      end

      it 'does not queue job' do
        expect do
          company.reload.update(name: Faker::Company.name)
        end.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
      end
    end
  end

  context 'when the model is a reviews aggregate' do
    it 'queues job' do
      expect do
        BrokerageReviewAggregate.create(company:, review_count: 1, sentiments: {})
      end.to change { ActiveCampaign::SyncCompanyJob.jobs.size }.by(1)
    end
  end

  context 'when the model is not registered' do
    with_model :test_model, superclass: ApplicationRecord do
      model do
        include Wisper::ActiveRecord::Publisher
      end
    end

    it 'does not queue job' do
      expect { TestModel.create! }.not_to(change { ActiveCampaign::SyncCompanyJob.jobs.size })
    end
  end
end
