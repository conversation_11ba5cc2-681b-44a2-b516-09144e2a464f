require 'rails_helper'

RSpec.describe Marketing::SyncCarrierListener, :wisper do
  let!(:carrier_profile) { create :carrier_profile, company: }
  let!(:company) { create :company, :carrier, :with_city, power_units: 100 }

  context 'when the model is a carrier profile user' do
    let(:profile_user) { build :carrier_profile_user, :pending, carrier_profile: }

    context 'when not verified' do
      it 'does not queue job' do
        expect { profile_user.save }.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
      end
    end

    context 'when verified' do
      before { profile_user.save }

      it 'queues job' do
        expect { profile_user.verify! }.to change { Hubspot::SyncCompanyJob.jobs.size }.by(1)
      end
    end
  end

  context 'when the model is a carrier profile' do
    context 'when the profile has been claimed' do
      before do
        create :carrier_profile_user, :verified, carrier_profile:
      end

      context 'when changing a tracked attribute' do
        it 'queues job' do
          expect do
            carrier_profile.reload.update(website_url: Faker::Internet.url)
          end.to change { Hubspot::SyncCompanyJob.jobs.size }.by(1)
        end
      end

      context 'when changing an untracked attribute' do
        it 'does not queue job' do
          expect do
            carrier_profile.reload.update(phone: Faker::PhoneNumber.phone_number)
          end.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
        end
      end
    end

    context 'when the profile has not been claimed' do
      it 'does not queue job' do
        expect do
          carrier_profile.reload.update(phone: Faker::PhoneNumber.phone_number)
        end.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
      end
    end
  end

  context 'when the model is a company' do
    context 'when the profile has been claimed' do
      before do
        create :carrier_profile_user, :verified, carrier_profile:
      end

      it 'queues job' do
        expect do
          company.reload.update(name: Faker::Company.name)
        end.to change { Hubspot::SyncCompanyJob.jobs.size }.by(1)
      end

      context 'when carrier has fewer than 21 power units' do
        let!(:company) { create :company, :carrier, :with_city, power_units: 20 }

        it 'does not queue job' do
          expect do
            company.reload.update(name: Faker::Company.name)
          end.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
        end
      end
    end

    context 'when the profile has not been claimed' do
      it 'does not queue job' do
        expect do
          company.reload.update(name: Faker::Company.name)
        end.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
      end
    end
  end

  context 'when the model is a reviews aggregate' do
    it 'queues job' do
      expect do
        ReviewsAggregate.create(company:, review_count: 1, sentiments: {})
      end.to change { Hubspot::SyncCompanyJob.jobs.size }.by(1)
    end

    context 'when the company is not a carrier' do
      let!(:company) { create :company }

      it 'does not queue job' do
        expect do
          ReviewsAggregate.create(company:, review_count: 1, sentiments: {})
        end.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
      end
    end
  end

  context 'when the model is a driver reviews aggregate' do
    it 'queues job' do
      expect do
        DriverCarrierReviewsAggregate.create(company:, review_count: 1)
      end.to change { Hubspot::SyncCompanyJob.jobs.size }.by(1)
    end
  end

  context 'when the model is not registered' do
    with_model :test_model, superclass: ApplicationRecord do
      model do
        include Wisper::ActiveRecord::Publisher
      end
    end

    it 'does not queue job' do
      expect { TestModel.create! }.not_to(change { Hubspot::SyncCompanyJob.jobs.size })
    end
  end
end
