require 'rails_helper'

RSpec.describe Heroku::CreateDomain do
  let(:hostname) { 'www.example.com' }

  before do
    stub_request(:post, 'https://api.heroku.com/apps/cs-website-test/domains')
      .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
  end

  describe '#call' do
    context 'when the domain is created' do
      let(:status) { 201 }
      let(:body) { Rails.root.join('spec/fixtures/heroku/create_domain/success.json').read }

      it 'returns the response' do
        response = described_class.call(hostname:)
        expect(response).to eq JSON.parse(body)
      end
    end

    context 'when the domain is not created' do
      let(:status) { 422 }
      let(:body) { Rails.root.join('spec/fixtures/heroku/create_domain/failure.json').read }

      it 'raises an error' do
        expect { described_class.call(hostname:) }.to raise_error RuntimeError, 'Domain already added to this app.'
      end
    end
  end
end
