require 'rails_helper'

RSpec.describe Forms::Companies::Signature do
  let(:form) { described_class.new(params) }

  let(:params) { { logo:, phone:, email:, website: } }

  let(:logo) { nil }
  let(:phone) { nil }
  let(:email) { nil }
  let(:website) { nil }

  describe '#colspan' do
    subject { form.colspan }

    context 'when all fields are empty' do
      it { is_expected.to eq(2) }
    end

    context 'when only logo is present' do
      let(:logo) { '1' }

      it { is_expected.to eq(3) }
    end

    context 'when only contact fields are present' do
      let(:phone) { '1234567890' }

      it { is_expected.to eq(3) }
    end

    context 'when logo and contact fields are present' do
      let(:logo) { '1' }
      let(:phone) { '1234567890' }

      it { is_expected.to eq(4) }
    end
  end
end
