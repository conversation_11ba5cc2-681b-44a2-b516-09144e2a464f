require 'rails_helper'

RSpec.describe Forms::CarrierLane do
  subject(:form) { described_class.new(params) }

  let(:params) { {} }

  describe '#attributes' do
    context 'with states present' do
      let(:params) { { state_ids: ['united-states:utah'] } }

      it 'returns states' do
        expect(form.attributes).to eq operation_state_ids: ['united-states:utah']
      end
    end

    context 'with no params' do
      it 'returns empty array' do
        expect(form.attributes).to eq operation_state_ids: []
      end
    end
  end
end
