require 'rails_helper'

RSpec.describe Forms::Carrier do
  subject(:form) { described_class.new(params) }

  let(:params) { {} }

  describe '#entity_type' do
    context 'when input is invalid' do
      let(:params) { { entity_type: 'shipper' } }

      it 'returns empty array' do
        expect(form.entity_type).to be_empty
      end
    end

    context 'when input is valid' do
      let(:params) { { entity_type: 'carrier' } }

      it 'returns entity_type' do
        expect(form.entity_type).to eq ['carrier']
      end
    end
  end

  describe '#to_query' do
    let(:params) { { radius: 50 } }

    it 'serializes form data for query string' do
      expect(form.to_query).to eq 'carrier%5Bradius%5D=50'
    end
  end

  describe '#to_es_filters' do
    context 'when city_id is present' do
      let(:city) { create :city }

      context 'with radius' do
        let(:params) { { city_id: city.id, radius: 50 } }

        it 'includes location data' do
          expect(form.to_es_filters).to(
            match(
              hash_including(
                location: {
                  radius: 50,
                  lat: be_within(0.01).of(city.latitude),
                  lon: be_within(0.01).of(city.longitude)
                }
              )
            )
          )
        end
      end

      context 'without radius' do
        let(:params) { { city_id: city.id } }

        it 'includes city' do
          expect(form.to_es_filters).to match hash_including(city_id: city.id)
        end
      end
    end

    context 'when insurance is present' do
      let(:params) { { insurance: 5000 } }

      it 'returns insurance amount' do
        expect(form.to_es_filters).to match hash_including(insurance: 5000)
      end
    end

    context 'when insurance is missing' do
      it 'excludes insurance' do
        expect(form.to_es_filters).to be_empty
      end
    end

    context 'with authority_maintained' do
      let(:params) { { authority_maintained: 30 } }

      it 'includes authority_date' do
        travel_to Date.new(2022, 7, 31) do
          expect(form.to_es_filters).to match hash_including(authority_date: '2022-07-01')
        end
      end
    end
  end

  describe '#to_es_shoulds' do
    let(:params) { { query: 'dart' } }

    it 'returns query options' do
      expect(form.to_es_shoulds).to eq(query_best_fields: 'dart', query_phrase_prefix: 'dart')
    end
  end

  describe '#sort_options' do
    context 'with radius' do
      let(:params) { { radius: 50, city_id: 1234 } }

      it 'includes distance' do
        expect(form.sort_options).to eq %i(cs_score distance fleet_size authority)
      end
    end

    context 'without radius' do
      it 'does not include distance' do
        expect(form.sort_options).to eq %i(cs_score fleet_size authority)
      end
    end
  end
end
