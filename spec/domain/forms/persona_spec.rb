require 'rails_helper'

RSpec.describe Forms::Persona do
  subject(:form) { described_class.new(user:, **attrs) }

  let(:user) { create :user, email: '<EMAIL>' }

  context 'when persona is broker' do
    context 'when docket number is missing' do
      let(:attrs) { { persona: 'broker' } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when brokerage domain exists' do
      let(:company) { create :company }
      let(:attrs) { { persona: 'broker', dot_number: company.dot_number } }

      context 'when email matches' do
        before do
          BrokerageDomain.create! company:, email_domain: 'carriersource.io'
        end

        it 'is valid' do
          expect(form).to be_valid
        end
      end

      context 'when email does not match' do
        before do
          BrokerageDomain.create! company:, email_domain: 'carriers.com'
        end

        it 'is not valid' do
          expect(form).not_to be_valid
        end
      end
    end

    context 'when required attributes are present' do
      let(:company) { create :company }
      let(:attrs) { { persona: 'broker', dot_number: company.dot_number } }

      it 'is valid' do
        expect(form).to be_valid
      end
    end
  end

  context 'when persona is shipper' do
    let(:city) { create :city }

    context 'when company is missing' do
      let(:attrs) { { persona: 'shipper', city_id: city.id } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when city is missing' do
      let(:attrs) { { persona: 'shipper', company: Faker::Company.name } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when required attributes are present' do
      let(:attrs) { { persona: 'shipper', company: Faker::Company.name, city_id: city.id } }

      it 'is valid' do
        expect(form).to be_valid
      end
    end
  end

  context 'when persona is dispatcher' do
    let(:city) { create :city }

    context 'when company is missing' do
      let(:attrs) { { persona: 'dispatcher', city_id: city.id } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when city is missing' do
      let(:attrs) { { persona: 'dispatcher', company: Faker::Company.name } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when required attributes are present' do
      let(:attrs) { { persona: 'dispatcher', company: Faker::Company.name, city_id: city.id } }

      it 'is valid' do
        expect(form).to be_valid
      end
    end
  end

  context 'when persona is seller' do
    let(:city) { create :city }

    context 'when company is missing' do
      let(:attrs) { { persona: 'seller', city_id: city.id } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when city is missing' do
      let(:attrs) { { persona: 'seller', company: Faker::Company.name } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when required attributes are present' do
      let(:attrs) { { persona: 'seller', company: Faker::Company.name, city_id: city.id } }

      it 'is valid' do
        expect(form).to be_valid
      end
    end
  end

  context 'when persona is carrier' do
    let(:company) { create :company }

    context 'when dot_number is missing' do
      let(:attrs) { { persona: 'carrier' } }

      it 'is not valid' do
        expect(form).not_to be_valid
      end
    end

    context 'when required attributes are present' do
      let(:attrs) do
        { persona: 'carrier', dot_number: company.dot_number, phone: Faker::PhoneNumber.phone_number }
      end

      it 'is valid' do
        expect(form).to be_valid
      end
    end
  end
end
