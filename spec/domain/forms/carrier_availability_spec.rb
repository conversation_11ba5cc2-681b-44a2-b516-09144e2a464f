require 'rails_helper'

RSpec.describe Forms::CarrierAvailability do
  describe '#to_es_query' do
    subject(:form) { described_class.new(**params) }

    let(:city) do
      create :city, name: 'Chicago', state_code: 'IL', country_code: 'US', latitude: 41.87, longitude: -87.63
    end

    context 'when origin_city_id is present' do
      let(:params) { { origin_city_id: city.id } }

      it 'returns origin filters' do
        expect(form.to_es_query[:filters]).to include(origin: {
                                                        location: { lat: 41.87, lon: -87.63 },
                                                        state_id: 'united-states:illinois',
                                                        region_id: 'united-states:midwest'
                                                      })
      end
    end

    context 'when destination_city_id is present' do
      let(:params) { { destination_city_id: city.id } }

      it 'returns origin filters' do
        expect(form.to_es_query[:filters]).to include(destination: {
                                                        location: { lat: 41.87, lon: -87.63 },
                                                        state_id: 'united-states:illinois',
                                                        region_id: 'united-states:midwest'
                                                      })
      end
    end
  end
end
