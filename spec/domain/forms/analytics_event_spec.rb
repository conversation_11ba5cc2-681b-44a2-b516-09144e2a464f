require 'rails_helper'

RSpec.describe Forms::AnalyticsEvent do
  subject(:form) { described_class.new(attributes) }

  describe 'AnalyticsCompany' do
    let(:company) { create :analytics_company }

    describe '#records' do
      context 'when id is present' do
        let(:attributes) { { analytics_company: { id: { value: company.id } } } }

        it 'returns a collection of companies' do
          expect(form.analytics_company.records).to contain_exactly(company)
        end
      end

      context 'when id is not present' do
        let(:attributes) { { analytics_company: { id: { value: [] } } } }

        it 'returns an empty collection' do
          expect(form.analytics_company.records).to be_empty
        end
      end
    end
  end

  describe '#to_es_query' do
    context 'when origin location_type is city' do
      let(:city) { create :city, latitude: 50, longitude: 50 }
      let(:attributes) { { search: true, origin: { location_type: 'city', city_ids: city.id } } }

      it 'returns a hash with city_ids' do
        expect(form.to_es_query).to(
          include(filters: hash_including(origin: { type: 'city', values: [{ lat: 50, lon: 50, radius: 50 }] }))
        )
      end
    end

    context 'when origin location_type is state' do
      let(:attributes) do
        { search: true, origin: { location_type: 'state', state_ids: 'united-states:utah' } }
      end

      it 'returns a hash with state_ids' do
        expect(form.to_es_query).to(
          include(filters: hash_including(origin: { type: 'state', values: ['united-states:utah'] }))
        )
      end
    end

    context 'when origin location_type is region' do
      let(:attributes) do
        { search: true, origin: { location_type: 'region', region_ids: 'united-states:west' } }
      end

      it 'returns a hash with region_ids' do
        expect(form.to_es_query).to(
          include(filters: hash_including(origin: { type: 'region', values: ['united-states:west'] }))
        )
      end
    end

    context 'when origin location_type is country' do
      let(:attributes) do
        { search: true, origin: { location_type: 'country', country_ids: 'united-states' } }
      end

      it 'returns a hash with country_ids' do
        expect(form.to_es_query).to(
          include(filters: hash_including(origin: { type: 'country', values: ['united-states'] }))
        )
      end
    end

    context 'with direct signals' do
      let(:company) { build_stubbed :company }
      let(:attributes) { { direct_signals: 'carrier.profile.viewed', target_company_id: company.id } }

      it 'returns a hash with direct signals' do
        expect(form.to_es_query).to include(direct: { target_company_id: company.id,
                                                      types: { 'carrier.profile.viewed' => 3 } })
      end
    end

    context 'with search filters' do
      let(:city) { build_stubbed :city }
      let(:attributes) { { search: true, origin: { location_type: 'city', city_ids: [city.id] } } }

      it 'returns a hash with search filters' do
        expect(form.to_es_query).to include(filters: hash_including(origin: { type: 'city' }))
      end
    end
  end

  describe '#cities' do
    context 'when location_type is city' do
      let(:city) { create :city }
      let(:attributes) { { origin: { location_type: 'city', city_ids: [city.id] } } }

      it 'returns a collection of cities' do
        expect(form.origin.cities).to eq([city])
      end
    end

    context 'when location_type is not city' do
      let(:attributes) { { origin: { location_type: 'state', state_ids: 'united-states:utah' } } }

      it 'returns an empty collection' do
        expect(form.origin.cities).to eq([])
      end
    end
  end

  describe '#industry_id' do
    context 'when industry_id is valid' do
      let(:industry_id) { analytics_industries(:accounting).id }
      let(:attributes) { { analytics_company: { industry_id: { value: industry_id } } } }

      it 'returns the industry_id' do
        expect(form.analytics_company.industry_id.value).to eq [industry_id]
      end
    end

    context 'when industry_id is invalid' do
      let(:attributes) { { analytics_company: { industry_id: { value: 'invalid' } } } }

      it 'returns empty array' do
        expect(form.analytics_company.industry_id.value).to eq []
      end
    end
  end
end
