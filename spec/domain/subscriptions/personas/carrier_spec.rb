require 'rails_helper'

RSpec.describe Subscriptions::Personas::Carrier do
  describe '#eql?' do
    let(:data) do
      JSON.parse(
        Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
      )
    end

    let(:event) { Stripe::Event.construct_from(data) }
    let(:subscription) { event.data.object }

    it 'returns true when subscription is the same' do
      carrier = described_class.new(subscription:)
      other = described_class.new(subscription:)

      expect(carrier.eql?(other)).to be true
    end
  end
end
