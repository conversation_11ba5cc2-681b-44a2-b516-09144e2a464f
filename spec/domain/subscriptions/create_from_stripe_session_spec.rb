require 'rails_helper'

RSpec.describe Subscriptions::CreateFromStripeSession do
  subject(:creator) { described_class.new(session_id:) }

  let(:session_id) { 'bps_1LhlyuAh05kH1vv07hvb5kx1' }

  let(:session) { double }
  let(:subscription) { double }

  describe '#call' do
    before do
      allow(Subscriptions::CreateFromStripeSubscription).to receive(:call)
      allow(Stripe::Checkout::Session).to receive(:retrieve).and_return(session)
      allow(session).to receive(:subscription).and_return('sub_123')
      allow(Stripe::Subscription).to receive(:retrieve).and_return(subscription)
    end

    it 'creates subscription' do
      creator.call
      expect(Subscriptions::CreateFromStripeSubscription).to have_received(:call)
    end
  end
end
