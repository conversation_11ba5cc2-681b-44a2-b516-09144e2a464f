require 'rails_helper'

RSpec.describe Subscriptions::CreateFromStripeSubscription do
  subject(:creator) { described_class.new(subscription:) }

  let(:event) { Stripe::Event.construct_from(data) }
  let(:subscription) { event.data.object }

  before do
    allow(Stripe::Subscription).to receive(:retrieve).and_return(subscription)
    allow(Stripe::Product).to receive(:retrieve).and_return(product)
  end

  describe '#call' do
    context 'with broker persona' do
      let(:data) do
        Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/broker/customer.subscription.created.json').read
          .then { |data| ERB.new(data).result(binding.tap { |b| b.local_variable_set(:user_id, user.try(:id)) }) }
          .then { |json| JSON.parse(json) }
      end

      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/broker/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      context 'when user does not exist' do
        let(:user) { nil }

        it 'does not create subscription' do
          expect { creator.call }.not_to change(Subscription, :count)
        end
      end

      context 'when user exists' do
        let!(:user) { create :user }

        it 'creates new subscription' do
          expect { creator.call }.to change(Subscription, :count).by(1)
        end

        it 'creates access package', :wisper do
          creator.call
          access_package = AccessPackage.find_by(resource: user)
          expect(access_package).to have_attributes active: true, packages: ['premium'], features: []
        end

        it 'sets correct attributes' do
          creator.call
          expect(Subscription.last).to have_attributes resource: user, status: 'active',
                                                       external_id: 'sub_1LXTNkAh05kH1vv0csrT4hCR'
        end

        it 'updates user stripe_customer_id' do
          expect { creator.call }.to change { user.reload.stripe_customer_id }.from(nil).to('cus_MFzMLZJeflrSOj')
        end
      end
    end

    context 'with carrier persona' do
      let(:data) do
        JSON.parse(
          Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
        )
      end

      let(:census) { create :census, dot_number: 1_614_362 }
      let(:company) { create :company, census: }
      let!(:carrier_profile) { create :carrier_profile, company: }

      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/carrier/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      it 'creates subscription' do
        expect { creator.call }.to change(Subscription, :count).by(1)
      end

      it 'creates access package', :wisper do
        creator.call
        access_package = AccessPackage.find_by(resource: carrier_profile)
        expect(access_package).to have_attributes active: true, packages: ['branding'],
                                                  features: %w(featured_review assets sponsored_content banner capacity)
      end

      it 'sets correct attributes' do
        creator.call
        expect(carrier_profile.subscription).to have_attributes external_id: 'sub_1LXTNkAh05kH1vv0csrT4hCR'
      end
    end

    context 'with brokerage persona' do
      let(:data) do
        JSON.parse(
          Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/brokerage/customer.subscription.created.json')
            .read
        )
      end

      let(:census) { create :census, dot_number: 1_614_362 }
      let(:company) { create :company, census: }
      let!(:brokerage_profile) { create :brokerage_profile, company: }

      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/brokerage/startup.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      it 'creates subscription' do
        expect { creator.call }.to change(Subscription, :count).by(1)
      end

      it 'creates access package', :wisper do
        creator.call
        access_package = AccessPackage.find_by(resource: brokerage_profile)
        expect(access_package).to have_attributes active: true, packages: ['branding'],
                                                  features: %w(featured_review assets sponsored_content banner)
      end

      it 'sets correct attributes' do
        creator.call
        expect(brokerage_profile.subscription).to have_attributes external_id: 'sub_1PnqAxAh05kH1vv06GbOGd0v'
      end
    end
  end
end
