require 'rails_helper'

RSpec.describe Subscriptions::UpdateFromStripeSubscription do
  subject(:updater) { described_class.new(subscription: event.data.object) }

  let(:event) { Stripe::Event.construct_from(data) }

  before do
    allow(Stripe::Product).to receive(:retrieve).and_return(product)
  end

  describe '#call' do
    context 'with broker persona' do
      let(:data) do
        Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/broker/customer.subscription.updated.json').read
          .then { |data| ERB.new(data).result(binding.tap { |b| b.local_variable_set(:user_id, user_id) }) }
          .then { |json| JSON.parse(json) }
      end

      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/broker/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      context 'when subscription does not exist' do
        let(:user_id) { nil }

        it 'is a no-op' do
          expect { updater.call }.not_to raise_error
        end
      end

      context 'when subscription exists' do
        let(:subscription) do
          create :subscription, :broker, external_id: 'sub_1LheqLAh05kH1vv0Jeo9h2eA', status: 'trialing'
        end

        let(:user_id) { subscription.resource.id }

        it 'updates status' do
          updater.call
          expect(subscription.reload.status).to eq 'active'
          expect(subscription.reload.canceled_at).to be_present
        end
      end
    end

    context 'with carrier persona' do
      let(:data) do
        JSON.parse(
          Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.updated.json').read
        )
      end

      let(:product) do
        Rails.root.join('spec/fixtures/stripe/products/carrier/premium.json').read
          .then { |json| JSON.parse(json) }
          .then { |data| Stripe::Product.construct_from(data) }
      end

      context 'when carrier subscription does not exist' do
        context 'when company not found' do
          it 'is a no-op' do
            expect { updater.call }.not_to raise_error
          end
        end

        context 'when company found' do
          let(:census) { create :census, dot_number: 1_614_362 }

          before do
            create :carrier_profile, company: create(:company, census:)
          end

          it 'creates subscription' do
            expect { updater.call }.to change(Subscription, :count).by(1)
          end
        end
      end

      context 'when carrier subscription exists' do
        let!(:carrier_subscription) do
          create :subscription, :carrier, external_id: 'sub_1LheqLAh05kH1vv0Jeo9h2eA'
        end

        it 'updates status' do
          updater.call
          expect(carrier_subscription.reload).to have_attributes status: 'active'
        end
      end
    end
  end
end
