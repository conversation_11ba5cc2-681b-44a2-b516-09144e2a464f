require 'rails_helper'

RSpec.describe Subscriptions::CreateCheckoutSession do
  subject(:creator) { described_class.new(user:, interval:, tier:) }

  let(:interval) { 'month' }
  let(:tier) { 'premium' }

  describe '#call' do
    before do
      allow(Stripe::Checkout::Session).to receive(:create)
    end

    context 'when stripe customer exists' do
      let(:user) { create :user, stripe_customer_id: 'cus_MFzMLZJeflrSOj' }

      it 'creates stripe checkout session' do
        creator.call
        expect(Stripe::Checkout::Session).to(
          have_received(:create)
            .with(
              line_items: [{ quantity: 1, price: 'price_1LhGtOAh05kH1vv0i5y53xQM' }],
              customer: 'cus_MFzMLZJeflrSOj', mode: 'subscription',
              success_url: 'http://www.lvh.me:3001/dashboard/subscription/success?session_id={CHECKOUT_SESSION_ID}',
              cancel_url: 'http://www.lvh.me:3001/dashboard/subscription',
              subscription_data: { metadata: { user_id: user.id } }, allow_promotion_codes: true,
              billing_address_collection: 'required', consent_collection: { terms_of_service: 'required' }
            )
        )
      end
    end

    context 'when stripe customer does not exist' do
      let(:user) { create :user }

      it 'creates stripe checkout session' do
        creator.call
        expect(Stripe::Checkout::Session).to(
          have_received(:create)
            .with(
              line_items: [{ quantity: 1, price: 'price_1LhGtOAh05kH1vv0i5y53xQM' }],
              customer_email: user.email, mode: 'subscription',
              success_url: 'http://www.lvh.me:3001/dashboard/subscription/success?session_id={CHECKOUT_SESSION_ID}',
              cancel_url: 'http://www.lvh.me:3001/dashboard/subscription',
              subscription_data: { metadata: { user_id: user.id } }, allow_promotion_codes: true,
              billing_address_collection: 'required', consent_collection: { terms_of_service: 'required' }
            )
        )
      end
    end
  end
end
