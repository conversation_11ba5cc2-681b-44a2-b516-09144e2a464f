require 'rails_helper'

RSpec.describe Users::SendVerificationReminder do
  subject(:reminder) { described_class.new(date: Time.zone.today) }

  describe '#call' do
    context 'when user is verified' do
      let!(:user) { create :user, :carrier }

      it 'does not send reminder' do
        expect { reminder.call }.not_to have_enqueued_mail UserMailer, :remind_unverified_user
      end
    end

    context 'when user is not verified' do
      let!(:user) { create :user, :carrier, verified: false }

      it 'sends reminder to unverified user' do
        expect { reminder.call }.to have_enqueued_mail UserMailer, :remind_unverified_user
      end
    end
  end
end
