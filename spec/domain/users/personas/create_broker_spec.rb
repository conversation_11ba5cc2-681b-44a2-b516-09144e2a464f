require 'rails_helper'

RSpec.describe Users::Personas::CreateBroker do
  subject(:creator) { described_class.new(user, **attributes) }

  let(:company) { create :company }
  let(:user) { create :user }
  let(:attributes) { { dot_number: company.dot_number, persona: 'broker' } }

  describe '#call' do
    it 'creates a broker' do
      expect { creator.call }.to change(Persona, :count).by(1)
    end

    it 'creates a broker with the correct attributes' do
      expect(creator.call).to have_attributes user:, company:, city: company.city
    end

    it 'updates the user company' do
      expect { creator.call }.to change { user.reload.company }.to(company.name)
    end

    it 'creates a default list' do
      expect { creator.call }.to change { user.lists.count }.by(1)
    end

    it 'sends a welcome email' do
      creator.call
      expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with('UserMailer', 'welcome', any_args)
    end
  end
end
