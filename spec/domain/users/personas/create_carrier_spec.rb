require 'rails_helper'

RSpec.describe Users::Personas::CreateCarrier do
  subject(:creator) { described_class.new(user, **attributes) }

  let(:carrier_profile) { create :carrier_profile }
  let(:company) { carrier_profile.company }
  let(:attributes) { { dot_number: company.dot_number, persona: 'carrier' } }

  describe '#call' do
    context 'when carrier email matches' do
      let(:user) { create :user, email: company.email }

      it 'creates carrier persona' do
        expect { creator.call }.to change(Persona, :count).by(1)
      end

      it 'verifies persona' do
        expect(creator.call).to be_verified
      end
    end

    context 'when carrier email does not match' do
      let(:user) { create :user }

      it 'creates carrier persona' do
        expect { creator.call }.to change(Persona, :count).by(1)
      end

      it 'does not verify persona' do
        expect(creator.call).not_to be_verified
      end
    end

    context 'when carrier email is missing' do
      let(:user) { create :user }
      let(:company) { create :company, email: nil }

      it 'creates carrier persona' do
        expect { creator.call }.to change(Persona, :count).by(1)
      end

      it 'does not verify persona' do
        expect(creator.call).not_to be_verified
      end
    end
  end
end
