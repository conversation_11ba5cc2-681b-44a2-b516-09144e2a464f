require 'rails_helper'

RSpec.describe Users::AttachAvatarJob do
  subject(:job) { described_class.new }

  let(:user) { create :user }
  let(:url) { 'https://www.carriersource.io/image.png' }
  let(:status) { 200 }
  let(:body) { Rails.root.join('spec/fixtures/images/check.png').read.to_s }

  before do
    stub_request(:get, url).to_return(status:, body:)
  end

  describe '#perform' do
    it 'does not process blank url' do
      job.perform(user.id, nil)
      expect(user.reload.avatar).not_to be_present
    end

    context 'when url is present' do
      context 'when request fails' do
        let(:status) { 404 }

        it 'does not attach avatar' do
          job.perform(user.id, url)
          expect(user.reload.avatar).not_to be_present
        end
      end

      context 'when request succeeds' do
        it 'attaches avatar' do
          job.perform(user.id, url)
          expect(user.reload.avatar).to be_present
        end
      end
    end
  end
end
