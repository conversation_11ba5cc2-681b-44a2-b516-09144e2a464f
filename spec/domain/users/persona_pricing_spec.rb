require 'rails_helper'

RSpec.describe Users::PersonaPricing do
  subject(:pricing) { described_class.new(user) }

  let(:user) { create(:user) }

  describe '#template' do
    context 'when user is present' do
      let(:user) { create(:user, :broker) }

      it 'returns the correct template' do
        expect(pricing.template).to eq('broker')
      end
    end

    context 'when user is not present' do
      let(:user) { nil }

      it 'returns the correct template' do
        expect(pricing.template).to eq('carrier')
      end
    end
  end

  describe '#options' do
    context 'when user is present' do
      let(:user) { create(:user, :broker) }

      it 'returns the correct options' do
        expect(pricing.options).to eq([:broker])
      end
    end

    context 'when user is not present' do
      let(:user) { nil }

      it 'returns the correct options' do
        expect(pricing.options).to eq(%i(carrier brokerage broker dispatcher))
      end
    end
  end
end
