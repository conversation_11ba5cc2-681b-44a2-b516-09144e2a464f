require 'rails_helper'

RSpec.describe ActiveAdmin::RoleAdapter do
  subject(:adapter) { described_class.new(resource, user) }

  let(:user) { create :user }

  before do
    role = Role.create name: 'Customer Success'
    role.role_actions.create(subject: 'companies', action: 'read')
    user.user_roles.create(role:)
  end

  describe '#authorized?' do
    let(:resource) { create :company }

    context 'when environment is development' do
      before do
        allow(Rails).to receive(:env) { 'development'.inquiry }
      end

      it 'returns true' do
        expect(adapter.authorized?(ActiveAdmin::Auth::READ, resource)).to be true
      end
    end

    context 'with wildcard subject' do
      let(:resource) { build_stubbed(:review) }

      before do
        role = Role.create name: 'Reader'
        role.role_actions.create(subject: '*', action: 'read')
        user.user_roles.create(role:)
      end

      it 'returns true' do
        expect(adapter.authorized?(ActiveAdmin::Auth::READ, resource)).to be true
      end
    end

    context 'with wildcard action' do
      before do
        role = Role.create name: 'Admin'
        role.role_actions.create(subject: 'companies', action: '*')
        user.user_roles.create(role:)
      end

      it 'returns true' do
        expect(adapter.authorized?(ActiveAdmin::Auth::DESTROY, resource)).to be true
      end
    end

    context 'when user has access' do
      it 'returns true' do
        expect(adapter.authorized?(ActiveAdmin::Auth::READ, resource)).to be true
      end
    end

    context 'when user does not have access' do
      it 'returns false' do
        expect(adapter.authorized?(ActiveAdmin::Auth::CREATE, resource)).to be false
      end
    end
  end
end
