require 'rails_helper'

RSpec.describe ActiveAdmin::RoleActionAuthorization do
  subject(:authorization) { described_class.new(role_action, sub, action) }

  let(:role_action) { instance_double RoleAction, subject: 'companies', action: 'read' }

  describe '#authorized?' do
    context 'with model subject' do
      let(:sub) { build_stubbed :company }
      let(:action) { :read }

      it { is_expected.to be_authorized }
    end

    context 'with string subject' do
      let(:sub) { 'companies' }
      let(:action) { :read }

      it { is_expected.to be_authorized }
    end
  end
end
