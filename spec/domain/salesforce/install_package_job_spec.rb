require 'rails_helper'

RSpec.describe Salesforce::InstallPackageJob do
  let(:integration) { create :analytics_integration, :salesforce, :carrier }
  let(:response) { instance_double HTTP::Response, parse: { 'id' => '1' } }

  before do
    allow(Salesforce::InstallPackage).to receive(:call).and_return(response)
  end

  describe '#perform' do
    it 'queues AwaitPackageInstallSuccessJob' do
      described_class.load(integration)
      described_class.drain
      expect(Salesforce::AwaitPackageInstallSuccessJob.jobs.pick('args')).to eq [integration.id, '1']
    end
  end
end
