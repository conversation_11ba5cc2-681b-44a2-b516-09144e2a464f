require 'rails_helper'

RSpec.describe Salesforce::QueryPackageInstall do
  subject(:query) { described_class.new(integration, package_install_request_id) }

  let(:integration) { create :analytics_integration, :salesforce, :carrier }
  let(:package_install_request_id) { '1' }

  before do
    stub_request(:get, 'https://na111.salesforce.com/services/data/v62.0/tooling/sobjects/PackageInstallRequest/1')
      .and_return(status:, body:, headers: { 'Content-Type': 'application/json' })
  end

  describe '#call' do
    context 'when request is successful' do
      let(:status) { 200 }
      let(:body) { { 'Id' => '1', 'Status' => 'IN_PROGRESS' }.to_json }

      it 'returns response' do
        expect(query.call.parse).to eq('Id' => '1', 'Status' => 'IN_PROGRESS')
      end
    end

    context 'when request is unsuccessful' do
      let(:status) { 400 }
      let(:body) { { 'Id' => '1', 'Status' => 'ERROR', 'Errors' => ['error'] }.to_json }

      it 'raises error' do
        expect { query.call }.to raise_error Salesforce::Api::Error
      end
    end
  end
end
