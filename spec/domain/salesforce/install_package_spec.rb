require 'rails_helper'

RSpec.describe Salesforce::InstallPackage do
  subject(:installer) { described_class.new(integration) }

  let(:integration) { create :analytics_integration, :salesforce, :carrier }

  before do
    stub_request(:post, 'https://na111.salesforce.com/services/data/v62.0/tooling/sobjects/PackageInstallRequest')
      .and_return(status:, body:, headers: { 'Content-Type': 'application/json' })
  end

  describe '#call' do
    context 'when request is successful' do
      let(:status) { 201 }
      let(:body) { { 'id' => '1', 'success' => true, 'errors' => [] }.to_json }

      it 'returns response' do
        expect(installer.call.parse).to eq('id' => '1', 'success' => true, 'errors' => [])
      end
    end

    context 'when request is unsuccessful' do
      let(:status) { 400 }
      let(:body) { { 'id' => '1', 'success' => false, 'errors' => ['error'] }.to_json }

      it 'raises error' do
        expect { installer.call }.to raise_error Salesforce::Api::Error
      end
    end
  end
end
