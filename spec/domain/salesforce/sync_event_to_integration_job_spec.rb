require 'rails_helper'

RSpec.describe Salesforce::SyncEventToIntegrationJob do
  describe '#perform' do
    let(:notification) { create :analytics_company_event_feed_notification, :salesforce }
    let(:visit) { create :analytics_visit, :geolocated, :completed }

    let!(:request_stub) do
      stub_request(:post, 'https://na111.salesforce.com/services/apexrest/carriersource/events')
        .and_return(status: 201, headers: { 'Content-Type' => 'application/json' }, body: { 'id' => '123' }.to_json)
    end

    context 'when event is Analytics::ShipperEvent' do
      let!(:event) { create :analytics_shipper_event, visit:, company: notification.integration.company }

      it 'calls Salesforce::SyncEventToIntegration' do
        described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
        expect(request_stub).to have_been_requested
      end
    end

    context 'when event is Analytics::AggregateShipperEvent' do
      let(:event) { create :analytics_aggregate_shipper_event, visit:, company: notification.integration.company }

      it 'calls Salesforce::SyncEventToIntegration' do
        described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
        expect(request_stub).to have_been_requested
      end
    end

    context 'when transportation provider is missing' do
      let(:event) { create :analytics_shipper_event, visit:, company: nil }

      it 'calls Salesforce::SyncEventToIntegration' do
        described_class.perform_inline(notification.to_gid.to_s, event.to_gid.to_s)
        expect(request_stub).to have_been_requested
      end
    end
  end
end
