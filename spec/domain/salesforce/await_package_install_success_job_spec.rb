require 'rails_helper'

RSpec.describe Salesforce::AwaitPackageInstallSuccessJob do
  let(:integration) { create :analytics_integration, :salesforce, :carrier }
  let(:response) { instance_double HTTP::Response, parse: { 'Status' => status } }

  before do
    allow(Salesforce::QueryPackageInstall).to receive(:call).and_return(response)
    Sidekiq::Batch.new.tap { |batch| batch.jobs { described_class.perform_async(integration.id, '1') } }
  end

  describe '#perform' do
    context 'when package install is in progress' do
      let(:status) { 'IN_PROGRESS' }

      it 'queues AwaitPackageInstallSuccessJob' do
        described_class.perform_one
        expect(described_class.jobs.pick('args')).to eq [integration.id, '1']
      end
    end

    context 'when package install is errored' do
      let(:status) { 'ERROR' }

      it 'raises error' do
        expect { described_class.perform_one }.to raise_error Salesforce::PackageInstallError
      end
    end

    context 'when package install is successful' do
      let(:status) { 'SUCCESS' }

      it 'does nothing' do
        expect { described_class.perform_one }.not_to raise_error
        expect(described_class.jobs.size).to eq 0
      end
    end
  end
end
