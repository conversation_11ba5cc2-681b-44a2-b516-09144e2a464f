require 'rails_helper'

RSpec.describe Webhooks::DeliverEvent do
  describe '#call' do
    let(:event) { create :webhooks_event, type: 'carrier.updated', data: { foo: 'bar' } }
    let(:subscription) { create :webhooks_subscription }

    before :each, :response do
      stub_request(:post, subscription.url).to_return(status:, body: '', headers: {})
    end

    context 'when response succeeds', :response do
      let(:status) { 200 }

      it 'sends the event to the subscription' do
        expect { described_class.call(event, subscription) }.not_to raise_error
      end
    end

    context 'when the response fails', :response do
      let(:status) { 500 }

      it 'raises an error' do
        expect { described_class.call(event, subscription) }.to raise_error Webhooks::DeliverEvent::Error
      end
    end

    context 'when the request times out' do
      before do
        stub_request(:post, subscription.url).to_timeout
      end

      it 'raises an error' do
        expect { described_class.call(event, subscription) }.to raise_error HTTP::TimeoutError
      end
    end
  end
end
