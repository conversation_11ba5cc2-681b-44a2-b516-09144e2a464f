require 'rails_helper'

RSpec.describe Webhooks::DeliverEventJob do
  describe '#perform' do
    let(:event) { create :webhooks_event, type: 'carrier.updated', data: { foo: 'bar' } }
    let(:subscription) { create :webhooks_subscription }

    before do
      allow(Webhooks::DeliverEvent).to receive(:call)
    end

    it 'delivers the event to the subscription' do
      described_class.perform_inline(event.to_gid.to_s, subscription.to_gid.to_s)
      expect(Webhooks::DeliverEvent).to have_received(:call).with(event, subscription)
    end
  end
end
