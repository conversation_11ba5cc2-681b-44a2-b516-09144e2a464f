require 'rails_helper'

RSpec.describe Rollups do
  let(:brokerage) { create :company }
  let(:carrier) { create :company }
  let(:broker) { create :persona, :broker, :verified }
  let(:user) { broker.user }

  describe '::generate' do
    before do
      create :analytics_event, name: 'Page Viewed', user:, properties: { id: carrier.id, route: 'carriers#show' }
      create :analytics_event, name: 'Page Viewed', user:, properties: { id: brokerage.id, route: 'brokerages#show' }
      create :analytics_event, name: 'Carrier Contact Viewed', user:, properties: { id: carrier.id }
      create :analytics_event, name: 'Brokerage Contact Viewed', user:, properties: { id: brokerage.id }
    end

    it 'creates rollup record' do
      described_class.generate

      expect(Rollup.pluck(:name, :interval, :dimensions, :value)).to(
        contain_exactly(
          ['brokerage_contact_views', 'day', { 'brokerage_id' => brokerage.id }, 1],
          ['brokerage_profile_views', 'day', { 'brokerage_id' => brokerage.id }, 1],
          ['carrier_contact_views', 'day', { 'carrier_id' => carrier.id }, 1],
          [
            'carrier_contact_views_by_brokerage', 'day',
            { 'carrier_id' => carrier.id, 'brokerage_id' => broker.company.id }, 1
          ],
          [
            'carrier_profile_views_by_brokerage', 'day',
            { 'carrier_id' => carrier.id, 'brokerage_id' => broker.company.id }, 1
          ]
        )
      )
    end
  end
end
