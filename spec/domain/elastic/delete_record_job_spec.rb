require 'rails_helper'

RSpec.describe Elastic::DeleteRecordJob, :elasticsearch do
  let!(:record) { create :carrier_availability }
  let(:model) { CarrierAvailability }

  before do
    model.es.import force: true, refresh: true, query: -> { es_import_query }
  end

  describe '#perform' do
    it 'deletes the record from the index' do
      expect(model.es.client.exists?(index: model.index_name, id: record.id)).to be true
      described_class.perform_inline(record.to_gid.to_s)
      expect(model.es.client.exists?(index: model.index_name, id: record.id)).to be false
    end
  end
end
