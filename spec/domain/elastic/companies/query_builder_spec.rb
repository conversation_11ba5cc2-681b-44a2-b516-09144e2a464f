require 'rails_helper'

RSpec.describe Elastic::Companies::QueryBuilder do
  subject(:builder) { described_class.new(filters:, shoulds:, must_nots:, order: sort, **options) }

  let(:filters) { {} }
  let(:must_nots) { {} }
  let(:shoulds) { {} }
  let(:sort) { [:cs_score, {}] }
  let(:options) { {} }

  describe 'filters' do
    let(:node) { builder.build.to_hash.dig(:query, :bool, :filter) }

    context 'with authority_date filter' do
      let(:filters) { { authority_date: '2020-05-15' } }

      it 'filters by authority date' do
        expect(node).to include range: { authority_date: { lte: '2020-05-15' } }
      end
    end

    context 'with authorities filter' do
      let(:filters) { { authorities: %w(common contract) } }

      it 'filters by authorities' do
        expect(node).to include terms: { authorities: %w(common contract) }
      end
    end

    context 'with carrier_operation filter' do
      let(:filters) { { carrier_operation: %w(intrastate interstate) } }

      it 'filters by carrier operation' do
        expect(node).to include terms: { carrier_operation: %w(intrastate interstate) }
      end
    end

    context 'with city_id filter' do
      let(:filters) { { city_id: 7143 } }

      it 'filters by city' do
        expect(node).to include term: { city_id: 7143 }
      end
    end

    context 'with dot_number filter' do
      let(:filters) { { dot_number: 123 } }

      it 'filters by dot number' do
        expect(node).to include term: { 'dot_number.raw' => 123 }
      end
    end

    context 'with freight_ids filter' do
      let(:filters) { { freight_ids: [2, 3] } }

      it 'filters by freight id' do
        expect(node).to include terms: { freight_ids: [2, 3] }
      end
    end

    context 'with truck_type_ids filter' do
      let(:filters) { { truck_type_ids: [2, 3] } }

      it 'filters by truck type id' do
        expect(node).to include terms: { truck_type_ids: [2, 3] }
      end
    end

    context 'with shipment_type_ids filter' do
      let(:filters) { { shipment_type_ids: [2, 3] } }

      it 'filters by freight id' do
        expect(node).to include terms: { shipment_type_ids: [2, 3] }
      end
    end

    context 'with specialized_service_ids filter' do
      let(:filters) { { specialized_service_ids: [2, 3] } }

      it 'filters by specialized service id' do
        expect(node).to include terms: { specialized_service_ids: [2, 3] }
      end
    end

    context 'with star_rating filter' do
      let(:filters) { { star_rating: { min: 3.5, max: 5 } } }

      it 'filters by star rating' do
        expect(node).to include range: { star_rating: { gte: 3.5, lte: 5 } }
      end

      context 'with min only' do
        let(:filters) { { star_rating: { min: 3 } } }

        it 'filters by fleet size' do
          expect(node).to include range: { star_rating: { gte: 3 } }
        end
      end

      context 'with max only' do
        let(:filters) { { star_rating: { max: 5 } } }

        it 'filters by fleet size' do
          expect(node).to include range: { star_rating: { lte: 5 } }
        end
      end
    end

    context 'with fleet_size filter' do
      let(:filters) { { fleet_size: { min: 3, max: 20 } } }

      it 'filters by fleet size' do
        expect(node).to include range: { fleet_size: { gte: 3, lte: 20 } }
      end

      context 'with min only' do
        let(:filters) { { fleet_size: { min: 3 } } }

        it 'filters by fleet size' do
          expect(node).to include range: { fleet_size: { gte: 3 } }
        end
      end

      context 'with max only' do
        let(:filters) { { fleet_size: { max: 20 } } }

        it 'filters by fleet size' do
          expect(node).to include range: { fleet_size: { lte: 20 } }
        end
      end
    end

    context 'with insurance filter' do
      let(:filters) { { insurance: 1_500_000 } }

      it 'filters by minimum insurance' do
        expect(node).to include range: { insurance: { gte: 1_500_000 } }
      end
    end

    context 'with location filter' do
      let(:filters) { { location: { lat: 50, lon: 30, radius: 10 } } }

      it 'filters by location' do
        expect(node).to include geo_distance: { distance: '10mi', location: { lat: 50, lon: 30 } }
      end
    end

    context 'with safety_rating filter' do
      let(:filters) { { safety_rating: %w(satisfactory) } }

      it 'filters by safety rating' do
        expect(node).to include terms: { safety_rating: %w(satisfactory) }
      end
    end
  end

  describe 'must_nots' do
    context 'with docket_number' do
      let(:query_must_not) { builder.build.to_hash.dig(:query, :bool, :must_not) }
      let(:must_nots) { { docket_number: 'MC0128743' } }

      it 'excludes docket_number' do
        expect(query_must_not).to eq [{ term: { 'docket_number.raw' => 'MC0128743' } }]
      end
    end
  end

  describe 'shoulds' do
    let(:node) { builder.build.to_hash.dig(:query, :bool, :should) }

    context 'with query_best_fields' do
      let(:shoulds) { { query_best_fields: 'dart' } }

      it 'performs multi-match best_fields query' do
        expect(node).to(
          eq(
            [
              {
                multi_match: {
                  query: 'dart',
                  type: 'best_fields',
                  fields: %w(docket_number.raw plain_docket_number.raw dot_number.raw name.raw)
                }
              }
            ]
          )
        )
      end
    end

    context 'with query_phrase_prefix' do
      let(:shoulds) { { query_phrase_prefix: 'dart' } }

      it 'performs multi-match phrase_prefix query' do
        expect(node).to(
          eq(
            [
              {
                multi_match: {
                  query: 'dart',
                  type: 'phrase_prefix',
                  fields: %w(docket_number plain_docket_number dot_number name.suggest)
                }
              }
            ]
          )
        )
      end
    end
  end

  describe 'sort' do
    let(:node) { builder.build.to_hash[:sort] }

    context 'when blank' do
      let(:sort) { [] }

      it 'does not sort' do
        expect(node).to be_nil
      end
    end

    context 'with cs_score' do
      it 'orders by cs_score' do
        expect(node).to eq [{ authorized: { order: :desc } }, { _score: { order: :desc } },
                            { cs_score: { order: :desc } }, { claimed: { order: :desc } }, { id: { order: :asc } }]
      end
    end

    context 'with rfp' do
      let(:sort) { [:rfp, {}] }

      it 'orders by rfp' do
        expect(node).to eq [{ upgraded: { order: :desc } }, { claimed: { order: :desc } },
                            { authorized: { order: :desc } }, { _score: { order: :desc } },
                            { cs_score: { order: :desc } }, { id: { order: :asc } }]
      end
    end

    context 'with distance' do
      let(:sort) { [:distance, { lat: 30, lon: 50 }] }

      it 'orders by distance' do
        expect(node).to eq [{ _geo_distance: { location: { lat: 30, lon: 50 }, unit: 'mi', order: :asc } }]
      end
    end

    context 'with fleet_size' do
      let(:sort) { [:fleet_size, {}] }

      it 'orders by fleet_size' do
        expect(node).to eq [{ fleet_size: { order: :desc } }, { id: { order: :asc } }]
      end
    end

    context 'with authority' do
      let(:sort) { [:authority, {}] }

      it 'orders by authority' do
        expect(node).to eq [{ authority_date: { order: :asc } }, { id: { order: :asc } }]
      end
    end
  end
end
