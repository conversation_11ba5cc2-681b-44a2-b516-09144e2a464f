require 'rails_helper'

RSpec.describe Elastic::ReindexJob, :elasticsearch do
  describe '::load' do
    let!(:company_one) { create :company }
    let!(:company_two) { create :company }

    it 'queues up jobs in sidekiq' do
      travel_to Date.new(2022, 7, 11) do
        described_class.load('Carrier')
        expect(described_class.jobs.pick('args')).to(
          eq ['Carrier', 'carriers_test_20220711000000', 1, [company_two.reload.id, 1000].min]
        )
      end
    end
  end

  describe '#perform' do
    before do
      create :company, :carrier, :with_city, :with_authority
    end

    context 'with valid batch' do
      it 'indexes documents' do
        described_class.load('Carrier')
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks :success
        response = Carrier.es.client.search(index: 'carriers_test')
        expect(response.dig('hits', 'total', 'value')).to eq 1
      end

      it 'reindexes records that were added while the batch was running' do
        response_body = Elastic::CreateOfflineIndex.call(Carrier, suffix: 'initial')
        index = response_body['index']
        Elastic::PromoteOfflineIndex.call(Carrier, index)

        described_class.load('Carrier')
        described_class.load('City')
        described_class.drain
        carrier = create(:company, :carrier, :with_city, :with_authority).as_entity(:carrier)
        Elastic::SyncRecordJob.perform_async(carrier.to_gid.to_s)
        Elastic::SyncRecordJob.drain
        Sidekiq::TestBatch.execute_callbacks :success
        expect(Elastic::SyncRecordJob.jobs.pick('args')).to eq [carrier.to_gid.to_s]
      end
    end

    context 'with invalid batch' do
      it 'does not index documents' do
        batch = described_class.load('Carrier')
        batch.invalidate_all
        described_class.drain
        Sidekiq::TestBatch.execute_callbacks :success
        expect(Carrier.es.client.indices.exists(index: 'carriers_test')).to be false
      end
    end
  end
end
