require 'rails_helper'

RSpec.describe Elastic::PromoteOfflineIndex, :elasticsearch do
  subject(:promoter) { described_class.new(Carrier, 'carriers_test_20220712') }

  describe '#call' do
    context 'when index is already promoted' do
      before do
        Elastic::CreateOfflineIndex.call(Carrier, suffix: '20220712', aliased: true)
      end

      it 'does not update aliases' do
        allow(Carrier.es.client.indices).to receive(:update_aliases)
        promoter.call
        expect(Carrier.es.client.indices).not_to have_received(:update_aliases)
      end
    end

    context 'when index has not been promoted' do
      before do
        Elastic::CreateOfflineIndex.call(Carrier, suffix: '20220712')
      end

      context 'with no existing index' do
        it 'promotes new index' do
          promoter.call
          aliased_index = Carrier.es.client.cat.aliases(name: 'carriers_test', format: :json).dig(0, 'index')
          expect(aliased_index).to eq 'carriers_test_20220712'
        end
      end

      context 'with existing index' do
        before do
          Elastic::CreateOfflineIndex.call(Carrier, suffix: '20220711', aliased: true)
        end

        it 'promotes the new index' do
          promoter.call
          aliased_index = Carrier.es.client.cat.aliases(name: 'carriers_test', format: :json).dig(0, 'index')
          expect(aliased_index).to eq 'carriers_test_20220712'
        end

        it 'removes existing one' do
          promoter.call
          expect(Carrier.es.client.indices.exists(index: 'carriers_test_20220711')).to be false
        end
      end
    end
  end
end
