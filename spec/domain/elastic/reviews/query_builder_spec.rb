require 'rails_helper'

RSpec.describe Elastic::Reviews::QueryBuilder do
  subject(:builder) { described_class.new(filters:, order: sort) }

  let(:filters) { {} }
  let(:sort) { [:id] }

  describe 'filters' do
    let(:node) { builder.build.to_hash.dig(:query, :bool, :filter) }

    context 'with company_id filter' do
      let(:filters) { { company_id: 1 } }

      it 'filters by company id' do
        expect(node).to include term: { company_id: 1 }
      end
    end

    context 'with freight_ids filter' do
      let(:filters) { { freight_ids: [2, 3] } }

      it 'filters by freight id' do
        expect(node).to include terms: { freight_ids: [2, 3] }
      end
    end

    context 'with truck_type_ids filter' do
      let(:filters) { { truck_type_ids: [2, 3] } }

      it 'filters by truck type id' do
        expect(node).to include terms: { truck_type_ids: [2, 3] }
      end
    end

    context 'with shipment_type_ids filter' do
      let(:filters) { { shipment_type_ids: [2, 3] } }

      it 'filters by freight id' do
        expect(node).to include terms: { shipment_type_ids: [2, 3] }
      end
    end
  end

  describe 'sort' do
    let(:node) { builder.build.to_hash[:sort] }

    context 'when blank' do
      let(:sort) { [] }

      it 'does not sort' do
        expect(node).to be_nil
      end
    end

    context 'with id' do
      it 'orders by id' do
        expect(node).to eq [{ _score: { order: :desc } }, { id: { order: :desc } }]
      end
    end
  end
end
