require 'rails_helper'

RSpec.describe Elastic::CreateOfflineIndex, :elasticsearch do
  subject(:creator) { described_class.new(Carrier, suffix: '20220711', aliased:, force:) }

  let(:aliased) { false }
  let(:force) { false }

  describe '#call' do
    context 'when index has not been created' do
      it 'creates index' do
        creator.call
        expect(Carrier.es.client.indices.exists(index: 'carriers_test_20220711')).to be true
      end

      context 'with aliased true' do
        let(:aliased) { true }

        it 'sets alias' do
          creator.call
          expect(Carrier.es.client.indices.exists(index: 'carriers_test')).to be true
        end
      end

      context 'with aliased false' do
        it 'does not set alias' do
          creator.call
          expect(Carrier.es.client.indices.exists(index: 'carriers_test')).to be false
        end
      end
    end

    context 'when index has already been created' do
      before do
        Carrier.es.create_index! index: 'carriers_test_20220711'
      end

      context 'with force true' do
        let(:force) { true }

        it 'recreates the index' do
          creator.call
          expect(Carrier.es.client.indices.exists(index: 'carriers_test_20220711')).to be true
        end
      end

      context 'with force false' do
        it 'raises error' do
          expect { creator.call }.to raise_error Elastic::Transport::Transport::Errors::BadRequest
        end
      end
    end
  end
end
