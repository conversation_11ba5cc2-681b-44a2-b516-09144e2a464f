require 'rails_helper'

RSpec.describe Routes::BuildConstraints do
  subject(:builder) { described_class.new(subdomain:) }

  let(:subdomain) { 'www' }

  describe '#call' do
    context 'when environment variable is present' do
      it 'returns a hash with the subdomain' do
        ClimateControl.modify('WWW_SUBDOMAIN' => 'staging') do
          expect(builder.call).to eq(subdomain: 'staging')
        end
      end
    end

    context 'when environment variable is not present' do
      it 'returns a hash with the subdomain' do
        expect(builder.call).to eq(subdomain: 'www')
      end
    end
  end
end
