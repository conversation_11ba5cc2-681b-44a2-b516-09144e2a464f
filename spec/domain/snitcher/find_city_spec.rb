require 'rails_helper'

RSpec.describe Snitcher::FindCity do
  subject(:service) { described_class.new(data) }

  let(:data) do
    {
      'company' => {
        'geo' => {
          'country_code' => 'US',
          'state' => 'Indiana',
          'city' => 'Fort Wayne'
        }
      }
    }
  end

  describe '#call' do
    context 'when geo data is incomplete' do
      let(:data) { {} }

      it 'returns a null object' do
        expect(service.call).not_to be_present
      end
    end

    context 'when the city exists' do
      let!(:city) { create :city, name: 'Fort Wayne', state_code: 'IN', country_code: 'US' }

      it 'returns the city' do
        expect(service.call).to eq(city)
      end
    end

    context 'when the city does not exist' do
      it 'returns a null object' do
        expect(service.call).not_to be_present
      end
    end
  end
end
