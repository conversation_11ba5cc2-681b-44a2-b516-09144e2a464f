require 'rails_helper'

RSpec.describe Snitcher::FindCompany do
  subject(:finder) { described_class.new(ip:) }

  describe '#call' do
    let(:ip) { Faker::Internet.ip_v4_address }

    context 'when disabled' do
      before do
        Analytics::Redis[:pool].call('SET', Snitcher::FindCompany::DISABLED_KEY, 'true')
      end

      it 'returns nil' do
        expect(finder.call).to be_nil
      end
    end

    context 'when enabled' do
      before do
        stub_request(:post, %r{https://app.snitcher.com/api/company/find})
          .to_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'with 200 response' do
        let(:status) { 200 }

        context 'when company is identified' do
          let(:body) { Rails.root.join('spec/fixtures/snitcher/company/find/identified.json').read }

          it 'returns company data' do
            expect(finder.call).to include('domain' => 'circledelivers.com')
          end
        end

        context 'when company is not identified' do
          let(:body) { Rails.root.join('spec/fixtures/snitcher/company/find/non-identified.json').read }

          it 'returns nil' do
            expect(finder.call).to be_nil
          end

          it 'stores missing domain' do
            finder.call
            payload = Analytics::Redis[:pool].call('GET', format(Snitcher::FindCompany::MISSING_DOMAIN_KEY, ip:))
            expect(payload).to be_present
          end
        end
      end

      context 'with 403 response' do
        let(:status) { 403 }
        let(:body) { '{"message":"You have exceeded your allowed quota"}' }

        it 'sets disabled key' do
          expect { finder.call }.to change {
            Analytics::Redis[:pool].call('GET', Snitcher::FindCompany::DISABLED_KEY)
          }.from(nil).to('true')
        end

        it 'returns nil' do
          expect(finder.call).to be_nil
        end
      end

      context 'with 429 response' do
        let(:status) { 429 }
        let(:body) { '{"message":"Too many attempts"}' }

        it 'returns nil' do
          expect(finder.call).to be_nil
        end
      end
    end
  end
end
