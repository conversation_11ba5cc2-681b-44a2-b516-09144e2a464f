require 'rails_helper'

RSpec.describe Personas::DispatcherAccess do
  subject(:access) { described_class.new(request, env: ActiveSupport::EnvironmentInquirer.new('production')) }

  let(:request) { ActionDispatch::Request.empty }
  let(:clearance_session) { instance_double Clearance::Session, current_user: user }

  before do
    allow(request).to receive(:env).and_return(clearance: clearance_session)
  end

  describe '#allowed?' do
    context 'when user is logged in' do
      context 'when user is a dispatcher' do
        let(:user) { create :user, :dispatcher }

        it 'returns false' do
          expect(access).not_to be_allowed
        end
      end

      context 'when user is a broker' do
        let(:user) { create :user, :broker }

        it 'returns true' do
          expect(access).to be_allowed
        end
      end
    end

    context 'when user is logged out' do
      let(:user) { nil }

      context 'when user is from US' do
        before do
          request.set_header 'HTTP_CF_IPCOUNTRY', 'US'
        end

        it 'returns true' do
          expect(access).to be_allowed
        end
      end

      context 'when user is from PK' do
        before do
          request.set_header 'HTTP_CF_IPCOUNTRY', 'PK'
        end

        it 'returns false' do
          expect(access).not_to be_allowed
        end
      end
    end
  end
end
