require 'rails_helper'

RSpec.describe CarrierProfiles::GenerateLanesMap do
  subject(:generator) { described_class.new(profile) }

  let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
  let(:company) { create :company, :with_city, city: }
  let(:profile) { create :carrier_profile, company: }

  describe '#call' do
    before do
      allow(Open3).to receive(:capture2).and_return(['svg', double])
      CarrierOperationState.create(carrier_profile: profile, state_id: 'united-states:utah')
      CarrierProfileTerminal.create(carrier_profile: profile, city:)
    end

    it 'calls javascript function with correct arguments' do
      generator.call
      expect(Open3).to have_received(:capture2).with(match('app/javascript/maps/generate'))
    end
  end
end
