require 'rails_helper'

RSpec.describe CarrierProfiles::VectorSearchAssets::Upsert do
  subject(:upsert) { described_class.new(carrier_profile) }

  let!(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, :with_city }
  let(:carrier) { carrier_profile.carrier }

  describe '#call' do
    let(:describe_index_body) { Rails.root.join('spec/fixtures/pinecone/indexes/describe/success.json').read }
    let(:embed_body) { Rails.root.join('spec/fixtures/openai/embeddings/success.json').read }

    let!(:upsert_stub) do
      stub_request(:post, 'https://carrier-profile-data-test.pinecone.io/vectors/upsert')
        .and_return(status: 200).times(4)
    end

    let(:terminal) { create :city, name: 'Chicago', state_code: 'IL' }

    before do
      stub_request(:get, 'https://api.pinecone.io/indexes/carrier-profile-data-test')
        .and_return(status: 200, body: describe_index_body, headers: { 'Content-Type' => 'application/json' })

      stub_request(:post, 'https://carrier-profile-data-test.pinecone.io/vectors/delete').and_return(status: 200)

      stub_request(:post, 'https://api.openai.com/v1/embeddings')
        .and_return(status: 200, body: embed_body, headers: { 'Content-Type' => 'application/json' })
    end

    context 'with all assets' do
      before do
        # Create flatbed truck type
        carrier_profile.update(truck_types: [truck_types(:flatbed).id])
        Carriers::SyncTruckTypes.call(carrier)

        # Create terminal
        CarrierProfileTerminal.create!(carrier_profile:, city: terminal)

        # Create review
        create(:review, :approved, company:)

        # Create document
        CarrierProfileAsset.create(carrier_profile:, file: fixture_file_upload('blank.pdf', 'application/pdf'))
      end

      it 'upserts all vectors' do
        upsert.call
        expect(upsert_stub).to have_been_requested.times(4)
      end
    end

    context 'with no assets' do
      it 'upserts some vectors' do
        upsert.call
        expect(upsert_stub).to have_been_requested.times(2)
      end
    end
  end
end
