require 'rails_helper'

RSpec.describe CarrierProfiles::StoreLanesMapJob do
  subject(:job) { described_class.new }

  describe '#perform' do
    let(:carrier_profile) { create :carrier_profile }

    before do
      allow(CarrierProfiles::StoreLanesMap).to receive(:call)
    end

    it 'delegates' do
      job.perform(carrier_profile.id)
      expect(CarrierProfiles::StoreLanesMap).to have_received(:call)
    end
  end
end
