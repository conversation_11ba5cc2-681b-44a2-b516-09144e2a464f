require 'rails_helper'

RSpec.describe CarrierProfiles::SyncOperationStates do
  subject(:syncer) { described_class.new(carrier_profile:, state_ids:) }

  let(:carrier_profile) { create :carrier_profile }

  before do
    carrier_profile.operation_states.create(state_id: 'united-states:illinois')
    carrier_profile.operation_states.create(state_id: 'united-states:utah')
  end

  describe '#call' do
    let(:state_ids) { %w(united-states:illinois united-states:utah) }

    context 'when the states have not changed' do
      it 'keeps the existing states' do
        syncer.call
        expect(carrier_profile.reload.operation_states.pluck(:state_id)).to match_array state_ids
      end
    end

    context 'when there are states to add and delete' do
      let(:state_ids) { %w(united-states:arizona united-states:utah) }

      it 'syncs states' do
        syncer.call
        expect(carrier_profile.reload.operation_states.pluck(:state_id)).to match_array state_ids
      end
    end
  end
end
