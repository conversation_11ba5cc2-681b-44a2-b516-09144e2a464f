require 'rails_helper'

RSpec.describe CarrierProfiles::StoreLanesMap do
  subject(:storage) { described_class.new(profile) }

  let(:profile) { create :carrier_profile }

  describe '#call' do
    before do
      allow(CarrierProfiles::GenerateLanesMap).to receive(:call).and_return('<svg></svg>')
      CarrierOperationState.create(carrier_profile: profile, state_id: 'united-states:utah')
    end

    context 'when operation states are present' do
      it 'attaches svg' do
        storage.call
        expect(profile.reload.lanes_map).to be_attached
      end
    end

    context 'when operation states are missing' do
      it 'purges attachment' do
        storage.call
        profile.operation_states.destroy_all
        expect { storage.call }.to have_enqueued_job ActiveStorage::PurgeJob
      end
    end
  end
end
