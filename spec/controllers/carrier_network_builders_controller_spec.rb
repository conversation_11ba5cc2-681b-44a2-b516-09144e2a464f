require 'rails_helper'

RSpec.describe CarrierNetworkBuildersController, type: :controller do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }
  let(:valid_attributes) { { name: 'Test Network Builder' } }
  let(:invalid_attributes) { { name: '' } }

  before { sign_in_as(user) }

  describe 'GET #index' do
    it 'returns a success response' do
      get :index
      expect(response).to be_successful
    end

    it 'assigns the user\'s carrier network builders' do
      carrier_network_builder # create the record
      get :index
      expect(assigns(:carrier_network_builders)).to eq([carrier_network_builder])
    end
  end

  describe 'GET #show' do
    it 'returns a success response' do
      get :show, params: { id: carrier_network_builder.to_param }
      expect(response).to be_successful
    end
  end

  describe 'GET #new' do
    it 'returns a success response' do
      get :new
      expect(response).to be_successful
    end
  end

  describe 'GET #edit' do
    it 'returns a success response' do
      get :edit, params: { id: carrier_network_builder.to_param }
      expect(response).to be_successful
    end
  end

  describe 'POST #create' do
    context 'with valid params' do
      it 'creates a new CarrierNetworkBuilder' do
        expect {
          post :create, params: { carrier_network_builder: valid_attributes }
        }.to change(CarrierNetworkBuilder, :count).by(1)
      end

      it 'redirects to the created carrier_network_builder' do
        post :create, params: { carrier_network_builder: valid_attributes }
        expect(response).to redirect_to(CarrierNetworkBuilder.last)
      end
    end

    context 'with invalid params' do
      it 'returns a success response (i.e. to display the \'new\' template)' do
        post :create, params: { carrier_network_builder: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PUT #update' do
    context 'with valid params' do
      let(:new_attributes) { { name: 'Updated Network Builder' } }

      it 'updates the requested carrier_network_builder' do
        put :update, params: { id: carrier_network_builder.to_param, carrier_network_builder: new_attributes }
        carrier_network_builder.reload
        expect(carrier_network_builder.name).to eq('Updated Network Builder')
      end

      it 'redirects to the carrier_network_builder' do
        put :update, params: { id: carrier_network_builder.to_param, carrier_network_builder: new_attributes }
        expect(response).to redirect_to(carrier_network_builder)
      end
    end

    context 'with invalid params' do
      it 'returns a success response (i.e. to display the \'edit\' template)' do
        put :update, params: { id: carrier_network_builder.to_param, carrier_network_builder: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'DELETE #destroy' do
    it 'destroys the requested carrier_network_builder' do
      carrier_network_builder # create the record
      expect {
        delete :destroy, params: { id: carrier_network_builder.to_param }
      }.to change(CarrierNetworkBuilder, :count).by(-1)
    end

    it 'redirects to the carrier_network_builders list' do
      delete :destroy, params: { id: carrier_network_builder.to_param }
      expect(response).to redirect_to(carrier_network_builders_url)
    end
  end

  context 'when user is not signed in' do
    before { sign_out }

    it 'redirects to sign in page' do
      get :index
      expect(response).to redirect_to(sign_in_url)
    end
  end

  context 'when accessing another user\'s network builder' do
    let(:other_user) { create(:user) }
    let(:other_builder) { create(:carrier_network_builder, user: other_user) }

    it 'raises ActiveRecord::RecordNotFound' do
      expect {
        get :show, params: { id: other_builder.to_param }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
end
