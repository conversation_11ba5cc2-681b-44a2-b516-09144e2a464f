require 'system_helper'

RSpec.describe 'Trucking Companies', :elasticsearch do
  describe 'GET /trucking_companies/{country}/{state}/{city}' do
    let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
    let(:postal_code) { create :postal_code, code: '60601' }
    let!(:carrier_profile) { create :carrier_profile, company: }
    let!(:company) { create :company, :carrier, :with_city, city:, postal_code:, legal_name: 'Planet Logistics' }

    before do
      Carrier.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    it 'renders the trucking companies page' do
      visit city_trucking_companies_path(*city.path)
      expect(page).to have_text('Top Trucking Companies in Chicago, IL')
      expect(page).to have_text('Planet Logistics')
    end

    context 'with filters' do
      it 'filters the results' do
        visit city_trucking_companies_path(*city.path)
        check 'Dry Van'
        expect(page).to have_text('Selected Filters (1)')
        expect(page).to have_no_text('Planet Logistics')

        click_on 'Clear All'
        expect(page).to have_text('Planet Logistics')

        fill_in id: 'carrier_query', with: 'trucks'
        expect(page).to have_no_text('Planet Logistics')
      end
    end
  end
end
