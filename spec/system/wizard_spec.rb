require 'system_helper'

RSpec.describe 'Wizard', :elasticsearch do
  describe 'Claim Carrier' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let!(:company) { create :company, :carrier, :with_city, legal_name: 'Putnik Express' }

    before do
      Carrier.es.import refresh: true, force: true
    end

    it 'renders the wizard page' do
      visit carrier_path(company)
      expect(page).to have_text('Claim this profile')

      click_link 'Claim this profile'
      expect(page).to have_current_path(sign_up_path)

      fill_in 'Email', with: '<EMAIL>'
      fill_in 'First Name', with: '<PERSON>'
      fill_in 'Last Name', with: 'Do<PERSON>'
      fill_in 'Password', with: 'Password@123'
      find('.x-btn-sign-up').click
      expect(page).to have_text 'Verification Required'

      click_link 'Simulate email click'
      expect(page).to have_text 'Email Verification'

      click_button 'Click here to verify'
      expect(page).to have_current_path(new_carrier_claim_path(carrier_id: 'putnik-express'))
      click_button 'Claim My Page'
    end
  end

  describe 'Email Claim Carrier' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let!(:company) { create :company, :carrier, :with_city, legal_name: 'Putnik Express' }

    it 'renders the wizard page' do
      visit wizard_url('email_claim_carrier', carrier_id: company.to_param, token: company.claim_token)
      expect(page).to have_current_path carrier_email_claim_path(company, company.claim_token)

      fill_in 'Email', with: '<EMAIL>'
      fill_in 'First Name', with: 'John'
      fill_in 'Last Name', with: 'Doe'
      fill_in 'Password', with: 'Password@123'
      find('.x-btn-sign-up').click

      expect(page).to have_current_path(new_carrier_claim_path(carrier_id: 'putnik-express'))

      click_button 'Claim My Page'
    end
  end

  describe 'Claim Brokerage' do
    let!(:brokerage_profile) { create :brokerage_profile, company: }
    let!(:company) { create :company, :broker, :with_city, legal_name: 'Cooler Logistics' }

    it 'renders the wizard page' do
      visit brokerage_path(company)
      expect(page).to have_text('Claim this profile')

      click_link 'Claim this profile'
      expect(page).to have_current_path(sign_up_path)

      fill_in 'Email', with: '<EMAIL>'
      fill_in 'First Name', with: 'John'
      fill_in 'Last Name', with: 'Doe'
      fill_in 'Password', with: 'Password@123'
      find('.x-btn-sign-up').click
      expect(page).to have_text 'Verification Required'

      click_link 'Simulate email click'
      expect(page).to have_text 'Email Verification'

      click_button 'Click here to verify'
      expect(page).to have_current_path new_brokerage_claim_path(company)
      click_button 'Claim My Page'
    end
  end
end
