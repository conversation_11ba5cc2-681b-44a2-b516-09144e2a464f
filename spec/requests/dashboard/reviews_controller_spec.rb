require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::ReviewsController, :elasticsearch do
  describe 'GET show' do
    let(:url) { dashboard_reviews_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as shipper' do
      let(:user) { create :user, :shipper }
      let(:url) { dashboard_reviews_url(as: user.to_param) }

      before do
        create :review, :approved, user: user
        create :brokerage_review, :shipper, :approved, user: user

        Review.es.import force: true, refresh: true
        BrokerageReview.es.import force: true, refresh: true
      end

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
