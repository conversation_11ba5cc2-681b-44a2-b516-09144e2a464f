require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::ListsController do
  describe 'GET index' do
    let(:url) { dashboard_lists_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:user) { create :user, :broker }
      let(:url) { dashboard_lists_url(as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:user) { create :user, :broker }
    let(:list) { create :list, user: }
    let(:url) { dashboard_list_url(list) }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:url) { dashboard_list_url(list, as: user.to_param) }

      before do
        create :bookmark, :carrier, list:
      end

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:user) { create :user, :broker }
    let(:list) { create :list, user: }
    let(:url) { edit_dashboard_list_url(list) }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:url) { edit_dashboard_list_url(list, as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:user) { create :user, :broker }
    let(:list) { create :list, user: }
    let(:url) { dashboard_list_url(list) }

    it_behaves_like 'a dashboard controller action', :patch

    context 'when user is logged in as broker' do
      let(:url) { dashboard_list_url(list, as: user.to_param) }

      context 'when update succeeds' do
        it 'redirects to show' do
          patch url, params: { list: { name: 'New name' } }
          expect(response).to redirect_to dashboard_list_url(list)
        end
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(List).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { list: { name: 'New name' } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:user) { create :user, :broker }
    let(:list) { create :list, user: }
    let(:url) { dashboard_list_url(list) }

    it_behaves_like 'a dashboard controller action', :delete

    context 'when user is logged in as broker' do
      let(:url) { dashboard_list_url(list, as: user.to_param) }

      context 'when destroy succeeds' do
        it 'redirects to index' do
          delete url
          expect(response).to redirect_to dashboard_lists_url
        end
      end

      context 'when destroy fails' do
        before do
          allow_any_instance_of(List).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end
end
