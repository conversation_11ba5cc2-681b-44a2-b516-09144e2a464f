require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::BookmarksController do
  describe 'DELETE destroy' do
    let(:user) { create :user, :broker }
    let(:list) { create :list, user: }
    let(:bookmark) { create :bookmark, :carrier, list: }
    let(:url) { dashboard_bookmark_url(bookmark) }

    it_behaves_like 'a dashboard controller action', :delete

    context 'when user is logged in' do
      let(:url) { dashboard_bookmark_url(bookmark, as: user.to_param) }

      it 'redirects to list' do
        delete url
        expect(response).to redirect_to dashboard_list_url(bookmark.list)
      end
    end
  end
end
