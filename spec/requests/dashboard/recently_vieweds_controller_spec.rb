require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::RecentlyViewedsController do
  describe 'GET show' do
    let(:url) { dashboard_recently_viewed_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:user) { create :user, :broker }
      let(:url) { dashboard_recently_viewed_url(as: user.to_param) }
      let(:company) { create :company, :carrier, :with_city }

      before do
        RecentlyViewed.create! user: user, company:
      end

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
