require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::SubscriptionsController do
  let(:user) { create :user, :broker }

  describe 'POST create' do
    let(:url) { dashboard_subscription_url }

    it_behaves_like 'a dashboard controller action', :post

    context 'when user is logged in as broker' do
      before do
        get root_url(as: user.to_param)
      end

      context 'when user subscription exists' do
        before do
          create :subscription, resource: user
        end

        it 'redirects to show page' do
          post url
          expect(response).to redirect_to dashboard_subscription_url
        end
      end

      context 'when user subscription does not exist' do
        let(:session) { double }

        before do
          allow(Subscriptions::CreateCheckoutSession).to receive(:call).and_return(session)
          allow(session).to receive(:url).and_return 'https://dashboard.stripe.com'
        end

        it 'redirects to stripe checkout session' do
          post url
          expect(response).to redirect_to 'https://dashboard.stripe.com'
        end
      end
    end
  end

  describe 'GET success' do
    let(:url) { success_dashboard_subscription_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:url) { success_dashboard_subscription_url(as: user.to_param) }

      before do
        allow(Subscriptions::CreateFromStripeSession).to receive(:call)
      end

      it 'creates subscription from stripe session' do
        get url
        expect(Subscriptions::CreateFromStripeSession).to have_received :call
        expect(response).to redirect_to dashboard_subscription_url
      end

      it 'sends welcome mailer' do
        expect do
          get url
        end.to have_enqueued_mail SubscriptionMailer, :welcome
      end
    end
  end

  describe 'GET show' do
    let(:url) { dashboard_subscription_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:url) { dashboard_subscription_url(as: user.to_param) }

      context 'when user subscription exits' do
        before do
          create :subscription, resource: user
        end

        it 'renders success status' do
          get url
          expect(response).to have_http_status :ok
        end
      end

      context 'when user subscription does not exist' do
        it 'redirects to pricing page' do
          get url
          expect(response).to redirect_to broker_pricing_url
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_dashboard_subscription_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:url) { edit_dashboard_subscription_url(as: user.to_param) }

      context 'when subscription exists' do
        let(:session) { double }
        let(:stripe_subscription) { double }

        before do
          allow(Stripe::BillingPortal::Session).to receive(:create).and_return(session)
          allow(session).to receive(:url).and_return 'https://api.stripe.com'
          allow(Stripe::Subscription).to receive(:retrieve).and_return(stripe_subscription)
          allow(stripe_subscription).to receive(:customer).and_return('cus_123')
          create :subscription, resource: user
        end

        it 'redirects to stripe session' do
          get url
          expect(response).to redirect_to 'https://api.stripe.com'
        end
      end
    end
  end
end
