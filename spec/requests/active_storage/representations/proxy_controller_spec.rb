require 'rails_helper'

RSpec.describe ActiveStorage::Representations::ProxyController, type: :request do
  describe 'GET /rails/active_storage/representations/:signed_id/*filename' do
    let(:profile) { create :carrier_profile, logo: fixture_file_upload(*logo) }
    let(:logo) { %w(logo.png image/png) }

    it 'deletes response cookies' do
      get rails_storage_proxy_url(profile.logo.variant(convert: :webp))

      expect(response).to have_http_status(:ok)
      expect(response.headers['Set-Cookie']).to be_nil
    end
  end
end
