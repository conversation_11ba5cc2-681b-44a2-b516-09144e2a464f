require 'rails_helper'

RSpec.describe Ahoy::EventsController do
  describe 'POST #create' do
    let(:event_params) do
      {
        visit_token: SecureRandom.uuid, visitor_token: SecureRandom.uuid,
        events_json: [{ name: 'Page Viewed', time: Time.zone.now.to_i }].to_json
      }
    end

    context 'with valid authenticity token' do
      it 'returns http success' do
        post '/ahoy/events', params: event_params
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid authenticity token', :allow_forgery_protection do
      it 'returns http bad request' do
        post '/ahoy/events', params: event_params
        expect(response).to have_http_status(:bad_request)
      end
    end
  end
end
