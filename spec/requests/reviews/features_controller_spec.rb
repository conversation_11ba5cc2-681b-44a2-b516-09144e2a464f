require 'rails_helper'
require_relative 'shared_examples'
require_relative '../carrier_dashboard/shared_contexts'

RSpec.describe Reviews::FeaturesController do
  include_context 'with verified carrier profile user'

  let(:review) { create :review, :approved, company: }

  describe 'PATCH update' do
    let(:url) { review_feature_url(review) }

    it_behaves_like 'a protected carrier review controller action', :patch

    context 'when carrier user is authorized' do
      let(:url) { review_feature_url(review, as: user.to_param) }

      it 'redirects to review' do
        patch url
        expect(response).to redirect_to review_url(review)
        expect(review.reload.featured).to be true
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(Review).to receive(:update!).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { review_feature_url(review) }

    it_behaves_like 'a protected carrier review controller action', :delete

    context 'when carrier user is authorized' do
      let(:url) { review_feature_url(review, as: user.to_param) }

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to review_url(review)
        expect(review.reload.featured).to be false
      end

      context 'when delete fails' do
        before do
          allow_any_instance_of(Review).to receive(:update!).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end
end
