RSpec.shared_examples 'a protected carrier review controller action' do |method|
  context 'when user not logged in' do
    it 'redirects to login' do
      public_send method, url
      expect(response).to redirect_to sign_in_url
    end
  end

  context 'when user is logged in' do
    context 'when user is not a carrier' do
      let(:user) { create :user, :broker }

      it 'redirects back to root' do
        get root_url(as: user.to_param)
        public_send method, url
        expect(response).to redirect_to root_url
      end
    end

    context 'when carrier user is different than review carrier' do
      let(:user) { create(:persona, :carrier).user }

      it 'redirects back to root' do
        get root_url(as: user.to_param)
        public_send method, url
        expect(response).to redirect_to root_url
      end
    end
  end
end
