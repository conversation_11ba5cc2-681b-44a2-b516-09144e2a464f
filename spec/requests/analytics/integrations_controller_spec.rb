require 'rails_helper'

RSpec.describe Analytics::IntegrationsController do
  describe 'GET oauth_redirect' do
    context 'with hubspot provider' do
      let(:integration) { create :analytics_integration, :carrier, :hubspot }

      let(:body) { Rails.root.join('spec/fixtures/hubspot/oauth/token/success.json').read }

      before do
        stub_request(:post, %r{https://api.hubapi.com/oauth/v1/token})
          .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when token exchange is valid' do
        let(:status) { 200 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when token exchange is invalid' do
        let(:status) { 400 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when code is not returned' do
        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end
    end

    context 'with salesforce provider' do
      let(:integration) { create :analytics_integration, :carrier, :salesforce }

      let(:body) { Rails.root.join('spec/fixtures/salesforce/oauth/token/success.json').read }

      before do
        stub_request(:post, %r{https://test.salesforce.com/services/oauth2/token})
          .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when token exchange is valid' do
        let(:status) { 200 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when token exchange is invalid' do
        let(:status) { 400 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when code is not returned' do
        context 'when error is OAUTH_EC_APP_NOT_FOUND' do
          it 'redirects to package install url' do
            get '/analytics/integrations/oauth-redirect',
                params: { error: 'OAUTH_EC_APP_NOT_FOUND', state: integration.to_param }

            expect(response).to(
              redirect_to('https://test.salesforce.com/packaging/installPackage.apexp?p0=04taj0000006NQ9AAM')
            )
          end
        end

        context 'when error is not OAUTH_EC_APP_NOT_FOUND' do
          it 'redirects to carrier dashboard' do
            get '/analytics/integrations/oauth-redirect', params: { state: integration.to_param }
            expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
          end
        end
      end
    end

    context 'with slack provider' do
      let(:integration) { create :analytics_integration, :carrier, :slack }

      let(:body) { Rails.root.join('spec/fixtures/slack/oauth/token/success.json').read }

      before do
        stub_request(:post, %r{https://slack.com/api/oauth.v2.access})
          .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when token exchange is valid' do
        let(:status) { 200 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when token exchange is invalid' do
        let(:status) { 400 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when code is not returned' do
        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end
    end

    context 'with teams provider' do
      let(:integration) { create :analytics_integration, :carrier, :teams }

      let(:body) { Rails.root.join('spec/fixtures/microsoft/oauth/token/success.json').read }

      before do
        stub_request(:post, %r{https://login.microsoftonline.com/common/oauth2/v2.0/token})
          .and_return(status:, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when token exchange is valid' do
        let(:status) { 200 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when token exchange is invalid' do
        let(:status) { 400 }

        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { code: '123', state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end

      context 'when code is not returned' do
        it 'redirects to carrier dashboard' do
          get '/analytics/integrations/oauth-redirect', params: { state: integration.to_param }
          expect(response).to redirect_to carrier_dashboard_analytics_integrations_url(integration.company)
        end
      end
    end
  end
end
