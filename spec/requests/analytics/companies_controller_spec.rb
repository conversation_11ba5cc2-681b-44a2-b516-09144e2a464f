require 'rails_helper'

RSpec.describe Analytics::CompaniesController, :elasticsearch do
  describe 'GET #search' do
    let!(:company) { create :analytics_company, name: 'Walmart', domain: 'walmart.com' }

    before do
      Analytics::Company.es.import force: true, refresh: true
    end

    it 'returns a list of companies' do
      get search_analytics_companies_url(format: :json), params: { query: 'walm' }
      expect(JSON.parse(response.body)).to eq([{ 'name' => 'Walmart', 'id' => company.id, 'domain' => 'walmart.com' }])
    end
  end
end
