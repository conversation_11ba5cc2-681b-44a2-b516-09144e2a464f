require 'rails_helper'

RSpec.describe Analytics::FeedsController do
  let!(:feed) { create :analytics_event_feed, name: 'My Event Feed' }
  let(:user) { create :user, :broker, admin: true }

  describe 'GET index' do
    context 'when user is not logged in' do
      it 'redirects to login' do
        get analytics_feeds_url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      context 'when user is not admin' do
        let(:user) { create :user, :broker, admin: false }

        it 'redirects to root' do
          get analytics_feeds_url(as: user.to_param)
          expect(response).to redirect_to root_url
        end
      end

      context 'when user is admin' do
        it 'responds with ok' do
          get analytics_feeds_url(as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET show' do
    it 'responds with ok' do
      get analytics_feed_url(feed, as: user.to_param)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET new' do
    it 'responds with ok' do
      get new_analytics_feed_url(as: user.to_param)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET edit' do
    it 'responds with ok' do
      get edit_analytics_feed_url(feed, as: user.to_param)
      expect(response).to have_http_status :ok
    end
  end

  describe 'POST create' do
    context 'with valid params' do
      it 'creates a feed' do
        expect do
          post analytics_feeds_url(as: user.to_param), params: { analytics_event_feed: { name: 'Feed' } }
        end.to change(Analytics::EventFeed, :count).by(1)
      end

      it 'redirects to index' do
        post analytics_feeds_url(as: user.to_param), params: { analytics_event_feed: { name: 'Feed' } }
        expect(response).to redirect_to analytics_feeds_url
      end
    end

    context 'with invalid params' do
      it 'does not create a feed' do
        expect do
          post analytics_feeds_url(as: user.to_param), params: { analytics_event_feed: { name: nil } }
        end.not_to change(Analytics::EventFeed, :count)
      end
    end
  end

  describe 'PATCH update' do
    context 'with valid params' do
      it 'updates the feed' do
        patch analytics_feed_url(feed, as: user.to_param), params: { analytics_event_feed: { name: 'Feed' } }
        expect(feed.reload.name).to eq 'Feed'
      end

      it 'redirects to index' do
        patch analytics_feed_url(feed, as: user.to_param), params: { analytics_event_feed: { name: 'Feed' } }
        expect(response).to redirect_to analytics_feeds_url
      end
    end

    context 'with invalid params' do
      it 'does not update the feed' do
        patch analytics_feed_url(feed, as: user.to_param), params: { analytics_event_feed: { name: '' } }
        expect(feed.reload.name).to eq 'My Event Feed'
      end
    end
  end

  describe 'DELETE destroy' do
    it 'destroys the feed' do
      expect do
        delete analytics_feed_url(feed, as: user.to_param)
      end.to change(Analytics::EventFeed, :count).by(-1)
    end

    it 'redirects to index' do
      delete analytics_feed_url(feed, as: user.to_param)
      expect(response).to redirect_to analytics_feeds_url
    end
  end

  describe 'POST export' do
    it 'enqueues a job' do
      expect do
        post export_analytics_feed_url(feed, as: user.to_param)
      end.to change(Analytics::ExportEventFeedJob.jobs, :size).by(1)
    end

    it 'redirects to show' do
      post export_analytics_feed_url(feed, as: user.to_param)
      expect(response).to redirect_to analytics_feed_url(feed)
    end
  end
end
