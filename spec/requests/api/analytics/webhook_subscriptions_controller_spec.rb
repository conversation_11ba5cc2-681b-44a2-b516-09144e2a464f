require 'rails_helper'
require_relative '../shared_context'

RSpec.describe Api::Analytics::WebhookSubscriptionsController do
  include_context 'with authorized request'

  let(:authorization) { developer_account_user.api_token }

  describe 'POST create' do
    let(:url) { api_analytics_company_event_feed_webhook_subscriptions_url(feed.uuid) }
    let(:feed) { create :analytics_company_event_feed, :carrier }

    it 'creates a new subscription' do
      expect do
        post url, headers: { 'Authorization' => "Bearer #{authorization}",
                             'Content-Type' => 'application/vnd.api+json', 'Accept' => 'application/vnd.api+json' },
                  params: { data: { type: 'analytics_company_event_feed_notifications',
                                    attributes: { url: Faker::Internet.url } } }.to_json
      end.to change(Analytics::CompanyEventFeedNotification, :count).by(1)
    end

    context 'when subscription is invalid' do
      before do
        allow_any_instance_of(Analytics::CompanyEventFeedNotification).to receive(:save).and_return(false)
      end

      it 'responds with unprocessable entity' do
        post url, headers: { 'Authorization' => "Bearer #{authorization}",
                             'Content-Type' => 'application/vnd.api+json', 'Accept' => 'application/vnd.api+json' },
                  params: { data: { type: 'analytics_company_event_feed_notifications',
                                    attributes: { url: Faker::Internet.url } } }.to_json
        expect(response).to have_http_status :unprocessable_entity
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { api_analytics_company_event_feed_webhook_subscription_url(feed.uuid, subscription.uuid) }
    let(:feed) { create :analytics_company_event_feed, :carrier }
    let!(:subscription) { create :analytics_company_event_feed_notification, :webhook, feed: }

    it 'destroys the subscription' do
      expect do
        delete url, headers: { 'Authorization' => "Bearer #{authorization}",
                               'Content-Type' => 'application/vnd.api+json', 'Accept' => 'application/vnd.api+json' }
      end.to change(Analytics::CompanyEventFeedNotification, :count).by(-1)
    end
  end
end
