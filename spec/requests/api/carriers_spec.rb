require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/carriers', :jsonapi, :elasticsearch do
  path '/carriers' do
    get 'List Carriers' do
      tags 'Carriers'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'filter[carrier_operation]', in: :query, description: 'Filter by carrier operation',
                schema: { type: :string, enum: Company::CARRIER_OPERATIONS }, required: false

      parameter name: 'filter[safety_rating]', in: :query, description: 'Filter by safety rating',
                schema: { type: :string, enum: Company::SAFETY_RATINGS }, required: false

      parameter name: 'filter[freight]', in: :query, description: 'Filter by freight',
                schema: { type: :string, enum: Records[:freights].all.map(&:slug) }, required: false

      parameter name: 'filter[truck_type]', in: :query, description: 'Filter by truck type',
                schema: { type: :string, enum: Records[:truck_types].all.map(&:slug) }, required: false

      parameter name: 'filter[shipment_type]', in: :query, description: 'Filter by shipment type',
                schema: { type: :string, enum: Records[:shipment_types].all.map(&:slug) }, required: false

      parameter name: 'filter[authority]', in: :query, description: 'Filter by authority type',
                schema: { type: :string, enum: %w(common contract broker) }, required: false

      parameter name: 'page[cursor]', in: :query, description: 'Cursor for pagination',
                schema: { type: :string }, required: false

      parameter name: 'page[size]', in: :query, description: 'Number of records per page',
                schema: { type: :integer, minimum: 1, maximum: 100, default: 10 }, required: false

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(operating_authorities freights truck_types shipment_types) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/carrier' } }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create(:company, :carrier, :with_authority, :with_city) }
        let(:on_time_pickup) { create :sentiment, :positive, label: 'On Time Pickup' }
        let(:on_time_delivery) { create :sentiment, :positive, label: 'On Time Delivery' }

        before do
          Carrier.es.import force: true, refresh: true
          create(:carrier_profile_contact, carrier_profile:, default: true)
          ReviewsAggregate.create(company:, cleanliness: 9.4, communication: 9.7, timeliness: 8.5,
                                  sentiments: { on_time_pickup.id => 3, on_time_delivery.id => 2 })
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/carriers/{id}' do
    parameter name: :id, in: :path, type: :string,
              description: 'The unique identifier of the carrier (id, dot_number, slug)'

    get 'Get Carrier' do
      tags 'Carriers'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(operating_authorities freights truck_types shipment_types) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/carrier' }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create(:company, :carrier, :with_authority, :with_city) }
        let(:id) { company.uuid }
        let(:on_time_pickup) { create :sentiment, :positive, label: 'On Time Pickup' }
        let(:on_time_delivery) { create :sentiment, :positive, label: 'On Time Delivery' }

        before do
          create(:carrier_profile_contact, carrier_profile:, default: true)
          ReviewsAggregate.create(company:, cleanliness: 9.4, communication: 9.7, timeliness: 8.5,
                                  sentiments: { on_time_pickup.id => 3, on_time_delivery.id => 2 })
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
