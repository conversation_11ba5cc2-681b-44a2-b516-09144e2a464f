require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/truck_types', :jsonapi do
  path '/truck_types' do
    get 'List Truck Types' do
      tags 'Truck Types'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/truck_type' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/truck_types/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the truck type'

    get 'Get Truck Type' do
      tags 'Truck Types'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/truck_type' }
               }

        include_context 'with authorized request'

        let(:id) { truck_types(:van).id }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
