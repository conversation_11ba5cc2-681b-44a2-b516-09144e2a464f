require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/reviews', :jsonapi, :elasticsearch do
  path '/carriers/{carrier_id}/reviews' do
    parameter name: :carrier_id, in: :path, type: :string,
              description: 'The unique identifier of the carrier (id, dot_number, slug)'

    get 'List Carrier Reviews' do
      tags 'Carriers'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'filter[freight]', in: :query, description: 'Filter by freight',
                schema: { type: :string, enum: Records[:freights].all.map(&:slug) }, required: false

      parameter name: 'filter[truck_type]', in: :query, description: 'Filter by truck type',
                schema: { type: :string, enum: Records[:truck_types].all.map(&:slug) }, required: false

      parameter name: 'filter[shipment_type]', in: :query, description: 'Filter by shipment type',
                schema: { type: :string, enum: Records[:shipment_types].all.map(&:slug) }, required: false

      parameter name: 'page[cursor]', in: :query, description: 'Cursor for pagination',
                schema: { type: :string }, required: false

      parameter name: 'page[size]', in: :query, description: 'Number of records per page',
                schema: { type: :integer, minimum: 1, maximum: 100, default: 10 }, required: false

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(sentiments) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/review' } }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :with_city }
        let!(:review) { create :review, :approved, company: }
        let(:carrier_id) { company.uuid }

        before do
          Review.es.import force: true, refresh: true
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let!(:review) { create :review, :approved }
        let(:carrier_id) { review.company.uuid }
      end
    end
  end

  path '/reviews/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the review'

    get 'Get Carrier Review' do
      tags 'Carriers'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(sentiments) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/review' }
               }

        include_context 'with authorized request'

        let(:review) { create :review, :approved }
        let(:id) { review.uuid }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
