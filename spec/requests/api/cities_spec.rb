require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/cities', :jsonapi, :elasticsearch do
  path '/cities' do
    get 'List Cities' do
      tags 'Cities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'filter[query]', in: :query, description: 'Filter by name or zip code',
                schema: { type: :string }, required: false

      parameter name: 'page[cursor]', in: :query, description: 'Cursor for pagination',
                schema: { type: :string }, required: false

      parameter name: 'page[size]', in: :query, description: 'Number of records per page',
                schema: { type: :integer, minimum: 1, maximum: 100, default: 10 }, required: false

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(state country) }, required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/city' } }
               }

        include_context 'with authorized request'

        before do
          city = create :city, name: 'Chicago', state_code: 'IL'
          postal_code = create :postal_code, code: '60601'
          create(:cities_postal_code, city:, postal_code:)
          City.es.import force: true, refresh: true
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/cities/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the city'

    get 'Get City' do
      tags 'Cities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/city' }
               }

        include_context 'with authorized request'

        let!(:city) { create :city, name: 'Chicago', state_code: 'IL' }
        let(:id) { city.full_slug }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let!(:city) { create :city, name: 'Chicago', state_code: 'IL' }
        let(:id) { city.full_slug }
      end

      it_behaves_like 'a not found response' do
        let(:id) { 'united-states:illinois:naperville' }
      end
    end
  end
end
