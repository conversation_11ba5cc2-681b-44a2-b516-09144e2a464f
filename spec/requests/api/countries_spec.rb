require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/countries', :jsonapi, :elasticsearch do
  path '/countries' do
    get 'List Countries' do
      tags 'Countries'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/country' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/countries/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the country'

    get 'Get Region' do
      tags 'Countries'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/country' }
               }

        include_context 'with authorized request'

        let(:id) { 'united-states' }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { 'united-states' }
      end

      it_behaves_like 'a not found response' do
        let(:id) { 'germany' }
      end
    end
  end
end
