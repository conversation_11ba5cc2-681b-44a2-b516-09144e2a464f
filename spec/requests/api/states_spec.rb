require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/states', :jsonapi, :elasticsearch do
  path '/states' do
    get 'List States' do
      tags 'States'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/state' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/states/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the state'

    get 'Get State' do
      tags 'States'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/state' }
               }

        include_context 'with authorized request'

        let(:id) { 'united-states:illinois' }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { 'united-states:illinois' }
      end

      it_behaves_like 'a not found response' do
        let(:id) { 'canada:illinois' }
      end
    end
  end
end
