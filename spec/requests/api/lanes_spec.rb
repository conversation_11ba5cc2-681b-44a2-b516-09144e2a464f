require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/lanes', :jsonapi, :elasticsearch do
  path '/carriers/lanes/{origin}/{destination}' do
    parameter name: :origin, in: :path, description: 'Origin postal code', schema: { type: :string },
              required: true
    parameter name: :destination, in: :path, description: 'Destination postal code', schema: { type: :string },
              required: true

    shared_context 'with lane data' do
      let(:carrier_profile) { create :carrier_profile, company: }
      let(:company) { create(:company, :carrier, :with_authority, :with_city) }
      let(:carrier) { carrier_profile.carrier }
      let(:pickup_city) { create :city, name: 'Chicago', state_code: 'IL' }
      let(:dropoff_city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
      let(:profile) { carrier.profile }

      before do
        create(:preferred_lane, carrier_profile: profile, pickup_city:, dropoff_city:)
        profile.operation_states.create!(state_id: 'united-states:utah')

        create(:postal_code, code: '60654')
          .then { |pc| create(:cities_postal_code, city: pickup_city, postal_code: pc) }

        create(:postal_code, code: '84108')
          .then { |pc| create(:cities_postal_code, city: dropoff_city, postal_code: pc) }
      end
    end

    get 'List Carriers by Lane' do
      tags 'Carriers'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'filter[carrier_operation]', in: :query, description: 'Filter by carrier operation',
                schema: { type: :string, enum: Company::CARRIER_OPERATIONS }, required: false

      parameter name: 'filter[safety_rating]', in: :query, description: 'Filter by safety rating',
                schema: { type: :string, enum: Company::SAFETY_RATINGS }, required: false

      parameter name: 'filter[freight]', in: :query, description: 'Filter by freight',
                schema: { type: :string, enum: Records[:freights].all.map(&:slug) }, required: false

      parameter name: 'filter[truck_type]', in: :query, description: 'Filter by truck type',
                schema: { type: :string, enum: Records[:truck_types].all.map(&:slug) }, required: false

      parameter name: 'filter[shipment_type]', in: :query, description: 'Filter by shipment type',
                schema: { type: :string, enum: Records[:shipment_types].all.map(&:slug) }, required: false

      parameter name: 'page[cursor]', in: :query, description: 'Cursor for pagination',
                schema: { type: :string }, required: false

      parameter name: 'page[size]', in: :query, description: 'Number of records per page',
                schema: { type: :integer, minimum: 1, maximum: 100, default: 10 }, required: false

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(operating_authorities freights truck_types shipment_types) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/carrier' } }
               }

        include_context 'with authorized request'
        include_context 'with lane data'

        let(:origin) { '60654' }
        let(:destination) { '84108' }
        let(:on_time_pickup) { create :sentiment, :positive, label: 'On Time Pickup' }
        let(:on_time_delivery) { create :sentiment, :positive, label: 'On Time Delivery' }

        before do
          Carrier.es.import force: true, refresh: true
          create(:carrier_profile_contact, carrier_profile:, default: true)
          ReviewsAggregate.create(company:, cleanliness: 9.4, communication: 9.7, timeliness: 8.5,
                                  sentiments: { on_time_pickup.id => 3, on_time_delivery.id => 2 })
        end

        run_test!
      end

      response 400, 'Bad Request' do
        schema type: :object,
               properties: {
                 errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
               }

        include_context 'with authorized request'
        include_context 'with lane data'

        let(:origin) { '606' }
        let(:destination) { '841' }

        before do
          Carrier.es.import force: true, refresh: true
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:origin) { '60654' }
        let(:destination) { '84108' }
      end
    end
  end
end
