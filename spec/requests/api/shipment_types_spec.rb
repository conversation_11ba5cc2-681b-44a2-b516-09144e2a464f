require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/shipment_types', :jsonapi do
  path '/shipment_types' do
    get 'List Shipment Types' do
      tags 'Shipment Types'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/shipment_type' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/shipment_types/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the shipment type'

    get 'Get Shipment Type' do
      tags 'Shipment Types'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/shipment_type' }
               }

        include_context 'with authorized request'

        let(:id) { shipment_types(:ltl).id }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
