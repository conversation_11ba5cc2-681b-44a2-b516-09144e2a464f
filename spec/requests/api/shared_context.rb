RSpec.shared_context 'with authorized request' do
  let(:developer_account_user) { create :developer_account_user }
  let(:developer_account) { developer_account_user.developer_account }
  let(:Authorization) { "Bearer #{developer_account_user.api_token}" }
end

RSpec.shared_context 'with unauthorized request' do
  let(:Authorization) { "Bearer #{SecureRandom.hex}" }
end

RSpec.shared_examples 'an unauthorized response' do
  response 401, 'Unauthorized' do
    schema type: :object,
           properties: {
             errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
           }

    include_context 'with unauthorized request'

    run_test!
  end
end

RSpec.shared_examples 'a not found response' do
  response 404, 'Not Found' do
    schema type: :object,
           properties: {
             errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
           }

    include_context 'with authorized request'

    run_test!
  end
end
