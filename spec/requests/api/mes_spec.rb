require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/cities', :jsonapi, :elasticsearch do
  path '/me' do
    get 'Show Current User' do
      tags 'Users'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK', document: false do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/user' }
               }

        include_context 'with authorized request'

        run_test!
      end
    end
  end
end
