require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/regions', :jsonapi, :elasticsearch do
  path '/regions' do
    get 'List Regions' do
      tags 'Regions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/region' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/regions/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the region'

    get 'Get Region' do
      tags 'Regions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/region' }
               }

        include_context 'with authorized request'

        let(:id) { 'united-states:midwest' }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { 'united-states:midwest' }
      end

      it_behaves_like 'a not found response' do
        let(:id) { 'canada:midwest' }
      end
    end
  end
end
