require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/freights', :jsonapi do
  path '/freights' do
    get 'List Freights' do
      tags 'Freights'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/freight' } }
               }

        include_context 'with authorized request'

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end
  end

  path '/freights/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the freight'

    get 'Get Freight' do
      tags 'Freights'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/freight' }
               }

        include_context 'with authorized request'

        let(:id) { freights(:household).uuid }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
