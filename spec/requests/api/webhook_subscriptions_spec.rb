require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/webhook_subscriptions', :jsonapi do
  path '/webhook_subscriptions' do
    get 'List Webhook Subscriptions' do
      tags 'Webhook Subscriptions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/webhook_subscription' } }
               }

        include_context 'with authorized request'

        let!(:subscription) { create :webhooks_subscription, developer_account: }

        run_test!
      end

      it_behaves_like 'an unauthorized response'
    end

    post 'Create Webhook Subscription' do
      tags 'Webhook Subscriptions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'
      consumes 'application/vnd.api+json'

      parameter name: :subscription, in: :body, schema: {
        type: :object,
        properties: {
          data: {
            type: :object,
            properties: {
              type: { type: :string, enum: %w(webhook_subscriptions) },
              attributes: {
                type: :object,
                properties: {
                  url: { type: :string },
                  types: { type: :array, items: { type: :string, enum: Webhooks::SampleEvent.keys } }
                },
                required: %w(url types)
              }
            },
            required: %w(type attributes)
          }
        }
      }

      response 201, 'Created' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/webhook_subscription' }
               }

        include_context 'with authorized request'

        let(:subscription) do
          { data: { type: 'webhook_subscriptions', attributes: { url: Faker::Internet.url,
                                                                 types: ['carrier.updated'] } } }
        end

        run_test!
      end

      response 422, 'Unprocessable Entity' do
        schema type: :object,
               properties: {
                 errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
               }

        include_context 'with authorized request'

        let(:subscription) do
          { data: { type: 'webhook_subscriptions', attributes: { url: '' } } }
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:subscription) do
          { data: { type: 'webhook_subscriptions', attributes: { url: Faker::Internet.url } } }
        end
      end
    end
  end

  path '/webhook_subscriptions/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the webhook subscription'

    get 'Show Webhook Subscription' do
      tags 'Webhook Subscriptions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/webhook_subscription' }
               }

        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end

    delete 'Delete Webhook Subscription' do
      tags 'Webhook Subscriptions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 204, 'No Content' do
        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end

  path '/webhook_subscriptions/{id}/send_test_notification' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the webhook subscription'

    post 'Send Test Notification' do
      tags 'Webhook Subscriptions'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'
      consumes 'application/json'

      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          type: { type: :string, enum: Webhooks::SampleEvent.keys },
          meta: { type: :object }
        },
        required: %w(type)
      }

      response 204, 'No Content' do
        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }
        let(:payload) { { type: 'carrier.updated', meta: { 'dot_number' => company.dot_number } } }

        before do
          stub_request(:post, subscription.url).and_return(status: 200)
        end

        run_test!
      end

      response 204, 'No Content', document: false do
        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }
        let(:brokerage_profile) { create :brokerage_profile, company: }
        let(:company) { create :company, :broker }
        let(:payload) { { type: 'brokerage.updated', meta: { 'dot_number' => company.dot_number } } }

        before do
          stub_request(:post, subscription.url).and_return(status: 200)
        end

        run_test!
      end

      response 408, 'Request Timeout' do
        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }
        let(:payload) { { type: 'carrier.updated', meta: { 'dot_number' => company.dot_number } } }

        before do
          stub_request(:post, subscription.url).to_timeout
        end

        run_test!
      end

      response 422, 'Unprocessable Entity' do
        include_context 'with authorized request'

        let(:subscription) { create :webhooks_subscription }
        let(:id) { subscription.uuid }
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }
        let(:payload) { { type: 'carrier.updated', meta: { 'dot_number' => company.dot_number } } }

        before do
          stub_request(:post, subscription.url).to_return(status: 500)
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:id) { SecureRandom.uuid }
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }
        let(:payload) { { type: 'carrier.updated', meta: { 'dot_number' => company.dot_number } } }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }
        let(:payload) { { type: 'carrier.updated', meta: { 'dot_number' => company.dot_number } } }
      end
    end
  end
end
