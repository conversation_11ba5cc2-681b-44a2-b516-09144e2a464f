require 'swagger_helper'
require_relative 'shared_context'

RSpec.describe 'api/capacities', :jsonapi, :elasticsearch do
  path '/carriers/{carrier_id}/capacities' do
    parameter name: :carrier_id, in: :path, type: :string,
              description: 'The unique identifier of the carrier (id, dot_number, slug)'

    get 'List Capacities' do
      tags 'Capacities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'filter[date]', in: :query, description: 'Filter by date',
                schema: { type: :string, format: :date }, required: false

      parameter name: 'filter[truck_type]', in: :query, description: 'Filter by truck type',
                schema: { type: :string, enum: Records[:truck_types].all.map(&:slug) }, required: false

      parameter name: 'filter[shipment_type]', in: :query, description: 'Filter by shipment type',
                schema: { type: :string, enum: Records[:shipment_types].all.map(&:slug) }, required: false

      parameter name: 'page[cursor]', in: :query, description: 'Cursor for pagination',
                schema: { type: :string }, required: false

      parameter name: 'page[size]', in: :query, description: 'Number of records per page',
                schema: { type: :integer, minimum: 1, maximum: 100, default: 10 }, required: false

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(truck_type shipment_type) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { type: :array, items: { '$ref' => '#/components/schemas/capacity' } }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :carrier, :with_authority, :with_city }
        let!(:capacity) { create :carrier_availability, company: }
        let(:carrier_id) { company.uuid }

        before do
          CarrierAvailability.es.import force: true, refresh: true
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :carrier, :with_authority, :with_city }
        let(:carrier_id) { company.uuid }
      end
    end

    post 'Create Capacity' do
      tags 'Capacities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'
      consumes 'application/vnd.api+json'

      parameter name: :capacity, in: :body, schema: {
        type: :object,
        properties: {
          data: {
            type: :object,
            properties: {
              type: { type: :string, enum: %w(capacities) },
              attributes: {
                type: :object,
                properties: {
                  date: { type: :string, format: :date },
                  truck_type_id: { type: :string, enum: Records[:truck_types].all.map(&:slug) },
                  shipment_type_id: { type: :string, enum: Records[:shipment_types].all.map(&:slug) },
                  length: { type: :number, description: 'Length in feet' },
                  weight: { type: :number, description: 'Weight in thousands of pounds' },
                  origin_type: { type: :string, enum: %w(city state region) },
                  destination_type: { type: :string, enum: %w(city state region anywhere) },
                  origin_city_ids: { type: :array, items: { type: :string } },
                  destination_city_ids: { type: :array, items: { type: :string } },
                  origin_state_ids: { type: :array, items: { type: :string } },
                  destination_state_ids: { type: :array, items: { type: :string } },
                  origin_region_ids: { type: :array, items: { type: :string } },
                  destination_region_ids: { type: :array, items: { type: :string } }
                },
                required: %w(date truck_type_id origin_type destination_type)
              }
            },
            required: %w(type attributes)
          }
        }
      }

      response 201, 'Created' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/capacity' }
               }

        include_context 'with authorized request'

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              attributes: {
                date: Time.zone.today.to_s,
                truck_type_id: truck_types(:flatbed).uuid,
                shipment_type_id: shipment_types(:ftl).uuid,
                length: 70,
                weight: 10,
                origin_type: 'state',
                destination_type: 'state',
                origin_state_ids: ['united-states:california'],
                destination_state_ids: ['united-states:oregon']
              }
            }
          }
        end

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :carrier, :with_authority, :with_city }
        let(:carrier_id) { company.uuid }

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user: developer_account_user.user
        end

        run_test!
      end

      response 401, 'Unauthorized' do
        schema type: :object,
               properties: {
                 errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
               }

        include_context 'with authorized request'

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              attributes: { date: Time.zone.today.to_s }
            }
          }
        end

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :carrier, :with_authority, :with_city }
        let(:carrier_id) { company.uuid }

        run_test!
      end

      response 422, 'Unprocessable Entity' do
        schema type: :object,
               properties: {
                 errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
               }

        include_context 'with authorized request'

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              attributes: { date: Time.zone.today.to_s }
            }
          }
        end

        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:company) { create :company, :carrier, :with_authority, :with_city }
        let(:carrier_id) { company.uuid }

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user: developer_account_user.user
        end

        run_test!
      end
    end
  end

  path '/capacities/{id}' do
    parameter name: :id, in: :path, type: :string, description: 'The unique identifier of the capacity'

    get 'Get Capacity' do
      tags 'Capacities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      parameter name: 'include', in: :query, description: 'Include related resources', getter: :query_include,
                schema: { type: :string, enum: %w(truck_type shipment_type) },
                required: false

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/capacity' }
               }

        include_context 'with authorized request'

        let!(:capacity) { create :carrier_availability }
        let(:id) { capacity.uuid }

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let!(:capacity) { create :carrier_availability }
        let(:id) { capacity.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end

    patch 'Update Capacity' do
      tags 'Capacities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'
      consumes 'application/vnd.api+json'

      parameter name: :capacity, in: :body, schema: {
        type: :object,
        properties: {
          data: {
            type: :object,
            properties: {
              type: { type: :string, enum: %w(capacities) },
              id: { type: :string },
              attributes: {
                type: :object,
                properties: {
                  date: { type: :string, format: :date },
                  truck_type_id: { type: :string },
                  shipment_type_id: { type: :string },
                  length: { type: :number },
                  weight: { type: :number },
                  origin_type: { type: :string },
                  destination_type: { type: :string },
                  origin_city_ids: { type: :array, items: { type: :string } },
                  destination_city_ids: { type: :array, items: { type: :string } },
                  origin_state_ids: { type: :array, items: { type: :string } },
                  destination_state_ids: { type: :array, items: { type: :string } },
                  origin_region_ids: { type: :array, items: { type: :string } },
                  destination_region_ids: { type: :array, items: { type: :string } }
                }
              }
            },
            required: %w(type id attributes)
          }
        }
      }

      response 200, 'OK' do
        schema type: :object,
               properties: {
                 data: { '$ref' => '#/components/schemas/capacity' }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier, :with_authority, :with_city }
        let!(:existing_capacity) { create :carrier_availability, company: }
        let(:id) { existing_capacity.uuid }

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              id:,
              attributes: {
                date: Time.zone.today.advance(days: 7)
              }
            }
          }
        end

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user: developer_account_user.user
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let!(:existing_capacity) { create :carrier_availability }
        let(:id) { existing_capacity.uuid }

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              id:,
              attributes: { date: Time.zone.today.advance(days: 7) }
            }
          }
        end
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              id:,
              attributes: { date: Time.zone.today.advance(days: 7) }
            }
          }
        end
      end

      response 422, 'Unprocessable Entity' do
        schema type: :object,
               properties: {
                 errors: { type: :array, items: { '$ref' => '#/components/schemas/error' } }
               }

        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier, :with_authority, :with_city }
        let!(:existing_capacity) { create :carrier_availability, company: }
        let(:id) { existing_capacity.uuid }

        let(:capacity) do
          {
            data: {
              type: 'capacities',
              id:,
              attributes: { date: nil }
            }
          }
        end

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user: developer_account_user.user
        end

        run_test!
      end
    end

    delete 'Delete Capacity' do
      tags 'Capacities'
      security [bearerAuth: []]
      produces 'application/vnd.api+json'

      response 204, 'No Content' do
        include_context 'with authorized request'

        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier, :with_authority, :with_city }
        let!(:capacity) { create :carrier_availability, company: }
        let(:id) { capacity.uuid }

        before do
          create :carrier_profile_user, :verified, carrier_profile:, user: developer_account_user.user
        end

        run_test!
      end

      it_behaves_like 'an unauthorized response' do
        let!(:capacity) { create :carrier_availability }
        let(:id) { capacity.uuid }
      end

      it_behaves_like 'a not found response' do
        let(:id) { SecureRandom.uuid }
      end
    end
  end
end
