require 'rails_helper'

RSpec.describe Personas::VerificationsController do
  let(:persona) { create :persona, :broker }
  let(:user) { persona.user }

  describe 'GET show' do
    before do
      create :persona_verification, persona:
    end

    it 'responds with ok status' do
      get persona_verification_url(persona, as: user.to_param)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET new' do
    context 'when verification request exists' do
      before do
        create :persona_verification, persona:
      end

      it 'redirects to verification page' do
        get new_persona_verification_url(persona, as: user.to_param)
        expect(response).to redirect_to persona_verification_url(persona)
      end
    end

    context 'when verification request does not exist' do
      it 'responds with ok status' do
        get new_persona_verification_url(persona, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    context 'with valid params' do
      let(:create_params) { attributes_for(:persona_verification) }

      it 'creates a new verification request' do
        expect do
          post persona_verification_url(persona, as: user.to_param), params: { persona_verification: create_params }
        end.to change(PersonaVerification, :count).by(1)
      end
    end

    context 'with invalid params' do
      let(:create_params) { attributes_for(:persona_verification, company: nil) }

      it 'does not create a new verification request' do
        expect do
          post persona_verification_url(persona, as: user.to_param), params: { persona_verification: create_params }
        end.not_to change(PersonaVerification, :count)
      end
    end
  end
end
