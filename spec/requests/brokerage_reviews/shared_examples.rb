RSpec.shared_examples 'a protected brokerage review controller action' do |method|
  context 'when user not logged in' do
    it 'redirects to login' do
      public_send method, url
      expect(response).to redirect_to sign_in_url
    end
  end

  context 'when user is logged in' do
    context 'when user is not a broker' do
      let(:user) { create :user, :carrier }

      it 'redirects back to root' do
        get root_url(as: user.to_param)
        public_send method, url
        expect(response).to redirect_to root_url
      end
    end

    context 'when brokerage user is different than review broker' do
      let(:brokerage_profile_user) { create :brokerage_profile_user, :verified }

      it 'redirects back to root' do
        get root_url(as: user.to_param)
        public_send method, url
        expect(response).to redirect_to root_url
      end
    end
  end
end
