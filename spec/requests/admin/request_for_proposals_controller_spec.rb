require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::RequestForProposalsController do
  describe 'GET index' do
    let(:url) { admin_request_for_proposals_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :request_for_proposal, :carrier
      end

      it 'responds with ok status' do
        get admin_request_for_proposals_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_request_for_proposal_url(request_for_proposal) }
    let(:request_for_proposal) { create :request_for_proposal, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_request_for_proposal_url(request_for_proposal, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_request_for_proposal_url(request_for_proposal) }
    let(:request_for_proposal) { create :request_for_proposal, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_request_for_proposal_url(request_for_proposal, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST notify' do
    let(:url) { notify_admin_request_for_proposal_url(request_for_proposal) }
    let(:request_for_proposal) { create :request_for_proposal, :carrier }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'redirects to show' do
        post notify_admin_request_for_proposal_url(request_for_proposal, as: user.to_param)
        expect(response).to redirect_to admin_request_for_proposal_url(request_for_proposal)
      end
    end
  end

  describe 'GET preview' do
    let(:url) { preview_admin_request_for_proposal_url(request_for_proposal) }
    let(:request_for_proposal) { create :request_for_proposal, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get preview_admin_request_for_proposal_url(request_for_proposal, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'with body param' do
        it 'responds with ok status' do
          get preview_admin_request_for_proposal_url(request_for_proposal, as: user.to_param, body: 'true')
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
