require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DriverCarrierReviewsController do
  describe 'GET index' do
    let(:url) { admin_driver_carrier_reviews_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :driver_carrier_review, :submitted
      end

      it 'responds with ok status' do
        get admin_driver_carrier_reviews_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_driver_carrier_review_url(review) }
    let(:review) { create :driver_carrier_review }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_driver_carrier_review_url(review, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST moderate' do
    let(:url) { moderate_admin_driver_carrier_review_url(review) }
    let(:review) { create :driver_carrier_review, :submitted }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      context 'when review is approved' do
        let(:params) { { driver_carrier_review: { status: 'approved' } } }

        it 'changes review status to approved' do
          expect do
            post moderate_admin_driver_carrier_review_url(review, as: user.to_param), params:
          end.to change { review.reload.status }.from('submitted').to('approved')
        end
      end

      context 'when review is rejected' do
        let(:params) { { driver_carrier_review: { status: 'rejected', rejection_reason: 'LACK_DETAIL' } } }

        it 'changes review status to rejected' do
          expect do
            post moderate_admin_driver_carrier_review_url(review, as: user.to_param), params:
          end.to change { review.reload.status }.from('submitted').to('rejected')
        end

        context 'when rejection reason is not provided' do
          let(:params) { { driver_carrier_review: { status: 'rejected' } } }

          it 'does not change review status' do
            expect do
              post moderate_admin_driver_carrier_review_url(review, as: user.to_param), params:
            end.not_to change { review.reload.status }.from('submitted')
          end
        end
      end
    end
  end
end
