require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::CarrierProfileUsersController do
  describe 'GET index' do
    let(:url) { admin_carrier_profile_users_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :carrier_profile_user
      end

      it 'responds with ok status' do
        get admin_carrier_profile_users_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_carrier_profile_user_url(carrier_profile_user) }
    let(:carrier_profile_user) { create :carrier_profile_user }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      context 'with pending brokerage profile user' do
        it 'responds with ok status' do
          get admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context 'with verified brokerage profile user' do
        let(:carrier_profile_user) { create :carrier_profile_user, :verified }

        it 'responds with ok status' do
          get admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'POST verify' do
    let(:url) { verify_admin_carrier_profile_user_url(carrier_profile_user) }
    let(:carrier_profile_user) { create :carrier_profile_user }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        post verify_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
        expect(response).to redirect_to admin_carrier_profile_user_url(carrier_profile_user)
      end

      context 'when profile is pending' do
        it 'verifies brokerage profile user' do
          post verify_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_verified
        end
      end

      context 'when profile is verified' do
        let(:carrier_profile_user) { create :carrier_profile_user, :verified }

        it 'does not verify brokerage profile user' do
          post verify_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_verified
        end
      end
    end
  end

  describe 'POST reject' do
    let(:url) { reject_admin_carrier_profile_user_url(carrier_profile_user) }
    let(:carrier_profile_user) { create :carrier_profile_user }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        post reject_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
        expect(response).to redirect_to admin_carrier_profile_user_url(carrier_profile_user)
      end

      context 'when profile is pending' do
        it 'rejects brokerage profile user' do
          post reject_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_rejected
        end
      end

      context 'when profile is rejected' do
        let(:carrier_profile_user) { create :carrier_profile_user, :rejected }

        it 'does not reject brokerage profile user' do
          post reject_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_rejected
        end
      end
    end
  end

  describe 'POST reset' do
    let(:url) { reset_admin_carrier_profile_user_url(carrier_profile_user) }
    let(:carrier_profile_user) { create :carrier_profile_user, :verified }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        post reset_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
        expect(response).to redirect_to admin_carrier_profile_user_url(carrier_profile_user)
      end

      context 'when profile is verified' do
        it 'resets brokerage profile user' do
          post reset_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_pending
        end
      end

      context 'when profile is pending' do
        let(:carrier_profile_user) { create :carrier_profile_user }

        it 'does not reset brokerage profile user' do
          post reset_admin_carrier_profile_user_url(carrier_profile_user, as: user.to_param)
          expect(carrier_profile_user.reload).to be_pending
        end
      end
    end
  end
end
