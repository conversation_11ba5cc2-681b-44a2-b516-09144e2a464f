require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::BrokerageReviewsController do
  describe 'GET index' do
    let(:url) { admin_brokerage_reviews_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :brokerage_review, :submitted, :carrier
      end

      it 'responds with ok status' do
        get admin_brokerage_reviews_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_brokerage_review_url(review) }
    let(:review) { create :brokerage_review, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_brokerage_review_url(review, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'with screenshot' do
        let(:review) { create :brokerage_review, :carrier, screenshot: }

        context 'with attached image' do
          let(:screenshot) { fixture_file_upload('logo.png', 'image/png') }

          it 'responds with ok status' do
            get admin_brokerage_review_url(review, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end

        context 'with attached pdf' do
          let(:screenshot) { fixture_file_upload('blank.pdf', 'application/pdf') }

          it 'responds with ok status' do
            get admin_brokerage_review_url(review, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end

        context 'with attached csv' do
          let(:screenshot) { fixture_file_upload('blank.csv', 'text/csv') }

          it 'responds with ok status' do
            get admin_brokerage_review_url(review, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_brokerage_review_url(review) }
    let(:review) { create :brokerage_review, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_brokerage_review_url(review, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { admin_brokerage_review_url(review) }
    let(:review) { create :brokerage_review, :carrier }

    it_behaves_like 'an activeadmin controller action', :patch

    context 'when logged in user has access' do
      include_context 'with super admin user'

      context 'when company_id does not change' do
        it 'redirects to show' do
          patch admin_brokerage_review_url(review, as: user.to_param),
                params: { brokerage_review: { company_id: review.company_id } }
          expect(response).to redirect_to admin_brokerage_review_url(review)
        end
      end

      context 'when company_id changes' do
        let!(:brokerage) { create :company, :broker }

        it 'redirects to index' do
          patch admin_brokerage_review_url(review, as: user.to_param),
                params: { brokerage_review: { company_id: brokerage.id } }
          expect(response).to redirect_to admin_brokerage_review_url(review)
        end
      end
    end
  end

  describe 'POST moderate' do
    let(:url) { moderate_admin_brokerage_review_url(review) }
    let(:review) { create :brokerage_review, :carrier }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      let(:brokerage_profile) { create :brokerage_profile, brokerage: review.brokerage }

      before do
        create :brokerage_profile_user, :verified, brokerage_profile:
      end

      context 'when review is approved' do
        it 'redirects to show' do
          post moderate_admin_brokerage_review_url(review, as: user.to_param),
               params: { brokerage_review: { status: 'approved' } }
          expect(response).to redirect_to admin_brokerage_review_url(review)
        end
      end

      context 'when review is rejected' do
        it 'redirects to show' do
          post moderate_admin_brokerage_review_url(review, as: user.to_param),
               params: { brokerage_review: { status: 'rejected', rejection_reason: 'LACK_DETAIL' } }
          expect(response).to redirect_to admin_brokerage_review_url(review)
        end

        context 'when rejection reason is not selected' do
          it 'does not change review status' do
            expect do
              post moderate_admin_brokerage_review_url(review, as: user.to_param),
                   params: { brokerage_review: { status: 'rejected' } }
            end.not_to(change { review.reload.status })
          end
        end
      end

      context 'when review is submitted' do
        it 'redirects to show' do
          post moderate_admin_brokerage_review_url(review, as: user.to_param),
               params: { brokerage_review: { status: 'submitted' } }
          expect(response).to redirect_to admin_brokerage_review_url(review)
        end
      end
    end
  end
end
