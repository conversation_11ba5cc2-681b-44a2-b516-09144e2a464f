require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DriverJobApplicationsController do
  describe 'GET index' do
    let(:url) { admin_driver_job_applications_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :driver_job_application
      end

      it 'responds with ok status' do
        get admin_driver_job_applications_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_driver_job_application_url(application) }
    let(:application) { create :driver_job_application }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_driver_job_application_url(application, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
