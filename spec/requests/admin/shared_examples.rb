RSpec.shared_examples 'an activeadmin controller action' do |method|
  context 'when user not logged in' do
    it 'redirects to login' do
      public_send method, url
      expect(response).to redirect_to sign_in_url
    end
  end

  context 'when logged in user is not admin' do
    let(:user) { create :user, :broker, admin: false }

    it 'redirects to root' do
      public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
      expect(response).to redirect_to root_url
    end
  end

  context 'when admin user does not have access' do
    let(:user) { create :user, :broker, admin: true }

    it 'redirects to root' do
      public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
      expect(response).to redirect_to root_url
    end
  end
end
