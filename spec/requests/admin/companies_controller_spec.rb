require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::CompaniesController do
  describe 'GET index' do
    let(:url) { admin_companies_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_companies_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_company_url(company) }
    let(:company) { create :company, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_company_url(company, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_company_url(company) }
    let(:company) { create :company }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_company_url(company, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'with requested_name' do
        let(:company) { create :company, name_field: 'requested_name', requested_name: 'Elite Trucking' }

        it 'responds with ok status' do
          get edit_admin_company_url(company, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { admin_company_url(company) }
    let(:company) { create :company, :carrier }

    it_behaves_like 'an activeadmin controller action', :patch

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'queues up job to sync in elasticsearch' do
        patch admin_company_url(company, as: user.to_param), params: { active_in_census: true }
        expect(Elastic::SyncRecordJob.jobs.pick('args')).to eq [company.as_entity(:carrier).to_gid.to_s]
      end
    end
  end
end
