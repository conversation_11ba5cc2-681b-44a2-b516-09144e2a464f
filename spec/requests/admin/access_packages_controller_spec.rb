require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::AccessPackagesController do
  describe 'GET index' do
    let(:url) { admin_access_packages_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :access_package, :carrier
      end

      it 'responds with ok status' do
        get admin_access_packages_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_admin_access_package_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get new_admin_access_package_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_access_package_url(access_package) }
    let(:access_package) { create :access_package, :carrier }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_access_package_url(access_package, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
