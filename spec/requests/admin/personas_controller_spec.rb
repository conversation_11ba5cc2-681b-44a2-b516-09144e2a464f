require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::PersonasController do
  describe 'GET index' do
    let(:url) { admin_personas_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :persona, :broker
      end

      it 'responds with ok status' do
        get admin_personas_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_persona_url(persona) }
    let(:persona) { create :persona, :broker }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_persona_url(persona, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'when verified' do
        let(:persona) { create :persona, :broker, :verified }

        it 'responds with ok status' do
          get admin_persona_url(persona, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'POST verify' do
    let(:url) { verify_admin_persona_url(persona) }
    let(:persona) { create :persona, :broker }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with redirect status' do
        post verify_admin_persona_url(persona, as: user.to_param)
        expect(response).to redirect_to admin_persona_url(persona)
      end
    end
  end

  describe 'POST reset' do
    let(:url) { reset_admin_persona_url(persona) }
    let(:persona) { create :persona, :broker, :verified }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with redirect status' do
        post reset_admin_persona_url(persona, as: user.to_param)
        expect(response).to redirect_to admin_persona_url(persona)
      end
    end
  end

  describe 'POST convert_to_dispatcher' do
    let(:url) { convert_to_dispatcher_admin_persona_url(persona) }
    let(:persona) { create :persona, :broker }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with redirect status' do
        post convert_to_dispatcher_admin_persona_url(persona, as: user.to_param)
        expect(response).to redirect_to admin_persona_url(persona)
      end

      context 'when already a dispatcher' do
        let(:persona) { create :persona, :dispatcher }

        it 'responds with no content' do
          post convert_to_dispatcher_admin_persona_url(persona, as: user.to_param)
          expect(response).to have_http_status :no_content
        end
      end
    end
  end

  describe 'POST convert_to_seller' do
    let(:url) { convert_to_seller_admin_persona_url(persona) }
    let(:persona) { create :persona, :broker }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with redirect status' do
        post convert_to_seller_admin_persona_url(persona, as: user.to_param)
        expect(response).to redirect_to admin_persona_url(persona)
      end

      context 'when already a seller' do
        let(:persona) { create :persona, :seller }

        it 'responds with no content' do
          post convert_to_seller_admin_persona_url(persona, as: user.to_param)
          expect(response).to have_http_status :no_content
        end
      end
    end
  end
end
