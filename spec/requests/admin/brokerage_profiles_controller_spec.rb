require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::BrokerageProfilesController do
  describe 'GET index' do
    let(:url) { admin_brokerage_profiles_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :brokerage_profile
      end

      it 'responds with ok status' do
        get admin_brokerage_profiles_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_brokerage_profile_url(brokerage_profile) }
    let(:brokerage_profile) { create :brokerage_profile }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :access_package, resource: brokerage_profile
      end

      it 'responds with ok status' do
        get admin_brokerage_profile_url(brokerage_profile, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'with logo and banner attachments' do
        let(:brokerage_profile) { create :brokerage_profile, :with_assets }

        it 'responds with ok status' do
          get admin_brokerage_profile_url(brokerage_profile, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_brokerage_profile_url(brokerage_profile) }
    let(:brokerage_profile) { create :brokerage_profile }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_brokerage_profile_url(brokerage_profile, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PUT update' do
    let(:url) { admin_brokerage_profile_url(brokerage_profile) }
    let(:brokerage_profile) { create :brokerage_profile }

    it_behaves_like 'an activeadmin controller action', :put

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        put admin_brokerage_profile_url(brokerage_profile, as: user.to_param, brokerage_profile: { hide_street: true })
        expect(brokerage_profile.reload.hide_street).to be true
      end
    end
  end
end
