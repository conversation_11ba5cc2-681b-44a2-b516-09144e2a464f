require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::PersonaVerificationsController do
  describe 'GET index' do
    let(:url) { admin_persona_verifications_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :persona_verification, :broker
      end

      it 'responds with ok status' do
        get admin_persona_verifications_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_persona_verification_url(persona_verification) }
    let(:persona_verification) { create :persona_verification, :broker }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      context 'with pending persona verification' do
        it 'responds with ok status' do
          get admin_persona_verification_url(persona_verification, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context 'with approved persona verification' do
        let(:persona_verification) { create :persona_verification, :broker, :approved }

        it 'responds with ok status' do
          get admin_persona_verification_url(persona_verification, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'POST approve' do
    let(:url) { approve_admin_persona_verification_url(persona_verification) }
    let(:persona) { create :persona, :broker }
    let!(:persona_verification) { create :persona_verification, :broker, :pending, persona: }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'approves the persona verification' do
        expect do
          post approve_admin_persona_verification_url(persona_verification, as: user.to_param)
        end.to(change { persona_verification.reload.approved? }.from(false).to(true))
      end

      context 'when persona is alraedy verified' do
        let(:persona) { create :persona, :broker, :verified }

        it 'does not re-verify the persona' do
          post approve_admin_persona_verification_url(persona_verification, as: user.to_param)
          expect(persona.reload).to be_verified
        end
      end

      it 'redirects to the persona verification page' do
        post approve_admin_persona_verification_url(persona_verification, as: user.to_param)
        expect(response).to redirect_to(admin_persona_verification_path(persona_verification))
      end
    end
  end

  describe 'POST reject' do
    let(:url) { reject_admin_persona_verification_url(persona_verification) }
    let(:persona) { create :persona, :broker }
    let!(:persona_verification) { create :persona_verification, :broker, :pending, persona: }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'rejects the persona verification' do
        expect do
          post reject_admin_persona_verification_url(persona_verification, as: user.to_param)
        end.to(change { persona_verification.reload.rejected? }.from(false).to(true))
      end

      it 'redirects to the persona verification page' do
        post reject_admin_persona_verification_url(persona_verification, as: user.to_param)
        expect(response).to redirect_to(admin_persona_verification_path(persona_verification))
      end
    end
  end

  describe 'POST reset' do
    let(:url) { reset_admin_persona_verification_url(persona_verification) }
    let(:persona) { create :persona, :verified, :broker }
    let!(:persona_verification) { create :persona_verification, :broker, :approved, persona: }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'resets the persona verification' do
        expect do
          post reset_admin_persona_verification_url(persona_verification, as: user.to_param)
        end.to(change { persona_verification.reload.pending? }.from(false).to(true))
      end

      context 'when the persona is pending' do
        let(:persona) { create :persona, :broker }

        it 'does not reset the persona' do
          post reset_admin_persona_verification_url(persona_verification, as: user.to_param)
          expect(persona.reload).to be_pending
        end
      end

      it 'redirects to the persona verification page' do
        post reset_admin_persona_verification_url(persona_verification, as: user.to_param)
        expect(response).to redirect_to(admin_persona_verification_path(persona_verification))
      end
    end
  end
end
