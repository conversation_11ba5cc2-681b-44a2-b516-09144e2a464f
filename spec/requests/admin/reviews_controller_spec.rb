require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::ReviewsController do
  describe 'GET index' do
    let(:url) { admin_reviews_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :review, :submitted
      end

      it 'responds with ok status' do
        get admin_reviews_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_review_url(review) }
    let(:review) { create :review }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_review_url(review, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_review_url(review) }
    let(:review) { create :review }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_review_url(review, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { admin_review_url(review) }
    let(:review) { create :review }

    it_behaves_like 'an activeadmin controller action', :patch

    context 'when logged in user has access' do
      include_context 'with super admin user'

      context 'when company_id does not change' do
        it 'redirects to show' do
          patch admin_review_url(review, as: user.to_param),
                params: { review: { company_id: review.company_id } }
          expect(response).to redirect_to admin_review_url(review)
        end
      end

      context 'when company_id changes' do
        let!(:carrier) { create :company, :carrier }

        it 'redirects to index' do
          patch admin_review_url(review, as: user.to_param), params: { review: { company_id: carrier.id } }
          expect(response).to redirect_to admin_review_url(review)
        end
      end
    end
  end

  describe 'POST moderate' do
    let(:url) { moderate_admin_review_url(review) }
    let(:review) { create :review, :submitted }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      include_context 'with super admin user'

      let(:carrier_profile) { create :carrier_profile, company: review.company }

      before do
        create :carrier_profile_user, :verified, carrier_profile:
      end

      context 'when review is approved' do
        it 'redirects to show' do
          post moderate_admin_review_url(review, as: user.to_param), params: { review: { status: 'approved' } }
          expect(response).to redirect_to admin_review_url(review)
        end
      end

      context 'when review is rejected' do
        it 'redirects to show' do
          post moderate_admin_review_url(review, as: user.to_param),
               params: { review: { status: 'rejected', rejection_reason: 'LACK_DETAIL' } }
          expect(response).to redirect_to admin_review_url(review)
        end

        context 'when rejection reason is not selected' do
          it 'does not change review status' do
            expect do
              post moderate_admin_review_url(review, as: user.to_param),
                   params: { review: { status: 'rejected' } }
            end.not_to(change { review.reload.status })
          end
        end
      end

      context 'when review is submitted' do
        it 'redirects to show' do
          post moderate_admin_review_url(review, as: user.to_param),
               params: { review: { status: 'submitted' } }
          expect(response).to redirect_to admin_review_url(review)
        end
      end
    end
  end
end
