require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::UsersController do
  describe 'GET index' do
    let(:url) { admin_users_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :user, :broker
        create :user
      end

      it 'responds with ok status' do
        get admin_users_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_user_url(user) }
    let(:user) { create :user, :broker }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :access_package, resource: user
        create :subscription, resource: user
      end

      it 'responds with ok status' do
        get admin_user_url(user, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_user_url(user) }
    let(:user) { create :user, :broker }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_user_url(user, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
