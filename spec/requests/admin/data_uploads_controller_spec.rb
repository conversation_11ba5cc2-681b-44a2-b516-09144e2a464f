require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DataUploadsController do
  describe 'GET index' do
    let(:url) { admin_data_uploads_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :data_upload, :pending
        create :data_upload, :success
      end

      it 'responds with ok status' do
        get admin_data_uploads_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_data_upload_url(data_upload) }
    let(:data_upload) { create :data_upload }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_data_upload_url(data_upload, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { admin_data_upload_url(data_upload) }
    let!(:data_upload) { create :data_upload }

    it_behaves_like 'an activeadmin controller action', :delete

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'deletes the data upload' do
        expect do
          delete admin_data_upload_url(data_upload, as: user.to_param)
        end.to change(DataUpload, :count).by(-1)
      end

      it 'redirects to index' do
        delete admin_data_upload_url(data_upload, as: user.to_param)
        expect(response).to redirect_to(admin_data_uploads_url)
      end
    end
  end
end
