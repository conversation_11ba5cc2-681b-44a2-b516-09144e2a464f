require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::ClaimPartnersController do
  describe 'GET index' do
    let(:url) { admin_claim_partners_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :claim_partner
      end

      it 'responds with ok status' do
        get admin_claim_partners_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_claim_partner_url(partner) }
    let(:partner) { create :claim_partner }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_claim_partner_url(partner, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_claim_partner_url(partner) }
    let(:partner) { create :claim_partner }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_claim_partner_url(partner, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
