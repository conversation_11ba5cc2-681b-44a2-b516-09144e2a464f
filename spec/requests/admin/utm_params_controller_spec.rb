require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::UtmParamsController do
  describe 'GET index' do
    let(:url) { admin_utm_params_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        UtmParam.create source: 'test'
      end

      it 'responds with ok status' do
        get admin_utm_params_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
