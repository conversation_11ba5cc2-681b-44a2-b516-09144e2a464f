require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::CarrierProfilesController do
  describe 'GET index' do
    let(:url) { admin_carrier_profiles_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :carrier_profile
      end

      it 'responds with ok status' do
        get admin_carrier_profiles_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_carrier_profile_url(carrier_profile) }
    let(:carrier_profile) { create :carrier_profile }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :access_package, resource: carrier_profile
        create :subscription, resource: carrier_profile
      end

      it 'responds with ok status' do
        get admin_carrier_profile_url(carrier_profile, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      context 'with logo and banner attachments' do
        let(:carrier_profile) { create :carrier_profile, :with_assets }

        it 'responds with ok status' do
          get admin_carrier_profile_url(carrier_profile, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_admin_carrier_profile_url(carrier_profile) }
    let(:carrier_profile) { create :carrier_profile }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get edit_admin_carrier_profile_url(carrier_profile, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
