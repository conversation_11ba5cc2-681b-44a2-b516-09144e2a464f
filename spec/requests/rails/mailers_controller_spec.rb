require 'rails_helper'

RSpec.describe Rails::MailersController do
  describe 'GET #index' do
    context 'when user is logged in' do
      context 'when user is not admin' do
        let(:user) { create :user, :broker, admin: false }

        it 'returns http forbidden' do
          get rails_mailers_url(as: user.to_param)
          expect(response).to have_http_status(:forbidden)
        end
      end

      context 'when user is admin' do
        let(:user) { create :user, :broker, admin: true }

        it 'returns http success' do
          get rails_mailers_url(as: user.to_param)
          expect(response).to be_successful
        end
      end
    end

    context 'when user is not logged in' do
      it 'returns http forbidden' do
        get rails_mailers_url
        expect(response).to have_http_status(:forbidden)
      end
    end
  end
end
