require 'rails_helper'

RSpec.describe Rails::HealthController do
  describe 'GET show' do
    it 'responds with ok status' do
      get '/healthz'
      expect(response).to have_http_status :ok
    end

    context 'when blocking unproxied requests' do
      around do |example|
        ClimateControl.modify BLOCK_UNPROXIED_REQUEST: 'true' do
          example.run
        end
      end

      context 'when request is proxied' do
        it 'responds with ok status' do
          get '/healthz', headers: { 'HTTP_X_FORWARDED_FOR' => '127.0.0.1, 172.71.210.38' }
          expect(response).to have_http_status :ok
        end
      end

      context 'when request is not proxied' do
        it 'responds with ok status' do
          get '/healthz'
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
