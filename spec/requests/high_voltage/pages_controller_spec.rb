require 'rails_helper'

RSpec.describe HighVoltage::PagesController do
  shared_examples 'a static page' do |id, params = {}|
    describe "GET /#{id}" do
      it 'returns success status' do
        get page_url(id, params)
        expect(response).to have_http_status :ok
      end
    end
  end

  it_behaves_like 'a static page', 'terms-of-service'
  it_behaves_like 'a static page', 'privacy-policy'
  it_behaves_like 'a static page', 'cookie-policy'
  it_behaves_like 'a static page', 'content-and-data-usage'
  it_behaves_like 'a static page', 'learn-more'
end
