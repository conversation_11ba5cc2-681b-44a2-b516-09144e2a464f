require 'rails_helper'

RSpec.describe Brokerages::WriteReviewController do
  describe 'GET search', :elasticsearch do
    let!(:company) { create :company, :broker, :with_city, legal_name: 'Cooler Logistics' }
    let!(:brokerage_profile) { create :brokerage_profile, company: }

    before do
      Brokerage.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    context 'with no query' do
      it 'responds with ok status' do
        get write_review_brokerages_url
        expect(response).to have_http_status :ok
      end

      it 'does not return any brokerages' do
        get write_review_brokerages_url
        expect(response.body).not_to include 'Cooler Logistics'
      end
    end

    context 'with query' do
      it 'responds with ok status' do
        get write_review_brokerages_url(query: 'cooler l')
        expect(response).to have_http_status :ok
      end

      it 'renders matching brokerage' do
        get write_review_brokerages_url(query: 'cooler l')
        expect(response.body).to include 'Cooler Logistics'
      end
    end
  end
end
