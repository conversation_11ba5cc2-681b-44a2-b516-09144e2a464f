require 'rails_helper'

RSpec.describe Brokerages::BookmarksController do
  let(:company) { create :company, :broker }
  let(:brokerage) { company.as_entity(:broker) }

  shared_examples 'an authenticated action' do |method|
    context 'when user is not logged in' do
      it 'redirects to login' do
        public_send method, url
        expect(response).to redirect_to sign_in_url
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_brokerage_bookmark_url(brokerage) }

    it_behaves_like 'an authenticated action', :get

    context 'when user is logged in' do
      let(:user) { create :user, :broker }
      let(:url) { new_brokerage_bookmark_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end

      context 'when user has lists' do
        let(:list) { create :list, user: }

        before do
          create :bookmark, :broker, list:, company:
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_bookmarks_url(brokerage) }

    it_behaves_like 'an authenticated action', :post

    context 'when user is logged in' do
      let(:user) { create :user, :broker }
      let(:url) { brokerage_bookmarks_url(brokerage, as: user.to_param) }
      let(:list) { create :list, user: }

      it 'creates bookmark' do
        post url, params: { bookmark: { list_ids: [list.id] } }
        expect(Bookmark.last).to have_attributes list:, company:, entity_type: 'broker'
      end
    end
  end

  describe 'POST create_list' do
    let(:url) { create_list_brokerage_bookmarks_url(brokerage) }

    it_behaves_like 'an authenticated action', :post

    context 'when user is logged in' do
      let(:user) { create :user, :broker }
      let(:url) { create_list_brokerage_bookmarks_url(brokerage, as: user.to_param) }

      it 'creates list' do
        post url, params: { list: { name: 'Chicago' } }
        expect(List.last).to have_attributes user:, name: 'Chicago'
      end
    end
  end
end
