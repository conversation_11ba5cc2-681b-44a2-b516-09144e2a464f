require 'rails_helper'

RSpec.describe Brokerages::<PERSON>laimsController do
  let(:company) { create :company, :broker }
  let!(:brokerage_profile) { create :brokerage_profile, company: }
  let(:user) { create :user, :broker }

  describe 'GET show' do
    let(:profile_user) { create :brokerage_profile_user, brokerage_profile: }

    context 'when profile user is verified' do
      before do
        profile_user.verify!
      end

      it 'redirects to brokerage dashboard root url' do
        get brokerage_claim_url(company, profile_user, as: user.to_param)
        expect(response).to redirect_to brokerage_dashboard_root_url(company)
      end
    end

    context 'when profile user is not verified' do
      it 'responds with ok status' do
        get brokerage_claim_url(company, profile_user, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    context 'when user is not logged in' do
      it 'redirects to sign in' do
        get new_brokerage_claim_url(company)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      it 'responds with ok status' do
        get new_brokerage_claim_url(company, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      it 'queues up job to upsert ActiveCampaign account', :wisper do
        expect do
          get new_brokerage_claim_url(company, as: user.to_param)
        end.to change(ActiveCampaign::InitiateProfileClaimJob.jobs, :size).by(1)
      end

      context 'when user has already claimed the brokerage profile' do
        let!(:brokerage_profile_user) { create :brokerage_profile_user, user:, brokerage_profile: }

        it 'redirects to show' do
          get new_brokerage_claim_url(company, as: user.to_param)
          expect(response).to redirect_to brokerage_claim_url(company, brokerage_profile_user)
        end
      end
    end
  end

  describe 'POST create' do
    it 'redirects to show' do
      post brokerage_claims_url(company, as: user.to_param)
      expect(response).to redirect_to brokerage_claim_url(company, BrokerageProfileUser.last)
    end
  end
end
