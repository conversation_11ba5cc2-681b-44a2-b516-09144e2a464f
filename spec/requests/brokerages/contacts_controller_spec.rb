require 'rails_helper'

RSpec.describe Brokerages::ContactsController do
  describe 'GET show' do
    let(:brokerage_profile) { create :brokerage_profile }
    let(:brokerage) { brokerage_profile.brokerage }
    let(:url) { brokerage_contact_url(brokerage) }

    context 'when user is not logged in' do
      it 'redirects to login page' do
        get url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:user) { create :user, :shipper }
      let(:url) { brokerage_contact_url(brokerage, as: user.to_param) }

      it 'returns ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
