require 'rails_helper'

RSpec.describe Brokerages::AssetsController do
  describe 'GET #show' do
    let(:brokerage_profile) { create :brokerage_profile }
    let(:brokerage) { brokerage_profile.brokerage }

    context 'when the asset is a logo' do
      let(:asset) { 'logo' }

      context 'when upgraded' do
        before do
          create :access_package, :brokerage, resource: brokerage_profile
        end

        it 'returns a not found response' do
          get brokerage_asset_url(brokerage, asset)
          expect(response).to have_http_status :not_found
        end
      end

      context 'when not upgraded' do
        it 'returns a not authorized response' do
          get brokerage_asset_url(brokerage, asset)
          expect(response).to have_http_status :unauthorized
        end
      end
    end

    context 'when the asset is an email signature' do
      let(:asset) { 'email_signature' }

      context 'when upgraded' do
        before do
          create :access_package, :brokerage, resource: brokerage_profile
        end

        context 'when attached' do
          before do
            brokerage_profile.email_signature.attach(io: fixture_file_upload('logo.png', 'image/png'),
                                                     filename: 'logo.png')
          end

          it 'redirects to blob' do
            get brokerage_asset_url(brokerage, asset)
            expect(response).to redirect_to rails_blob_url(brokerage_profile.email_signature)
          end
        end

        context 'when not attached' do
          it 'returns a not found response' do
            get brokerage_asset_url(brokerage, asset)
            expect(response).to have_http_status :not_found
          end
        end
      end

      context 'when not upgraded' do
        it 'returns a not found response' do
          get brokerage_asset_url(brokerage, asset)
          expect(response).to have_http_status :unauthorized
        end
      end
    end
  end
end
