require 'rails_helper'

RSpec.describe Brokerages::RequestForProposalsController do
  let(:company) { create :company }
  let(:user) { create :user, :shipper }

  describe 'GET #new' do
    let(:url) { new_brokerage_request_for_proposal_url(company) }

    context 'when user is not logged in' do
      it 'responds with success status' do
        get url
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when user is logged in' do
      let(:url) { new_brokerage_request_for_proposal_url(company, as: user.to_param) }

      it 'responds with success status' do
        get url
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'POST #create' do
    let(:url) { brokerage_request_for_proposals_url(company) }
    let(:pickup_city) { create :city }
    let(:dropoff_city) { create :city }

    let(:valid_params) do
      attributes_for(:request_for_proposal)
        .merge(pickup_city_id: pickup_city.id, dropoff_city_id: dropoff_city.id)
    end

    context 'when user is not logged in' do
      context 'with valid params' do
        it 'creates a new request for proposal' do
          expect do
            post url, params: { request_for_proposal: valid_params }
          end.to change(RequestForProposal, :count).by(1)
        end
      end

      context 'with invalid params' do
        let(:invalid_params) { valid_params.merge(company_name: nil) }

        it 'does not create a new request for proposal' do
          expect do
            post url, params: { request_for_proposal: invalid_params }
          end.not_to change(RequestForProposal, :count)
        end
      end
    end

    context 'when user is logged in' do
      let(:url) { brokerage_request_for_proposals_url(company, as: user.to_param) }

      context 'with valid params' do
        it 'creates a new request for proposal' do
          expect do
            post url, params: { request_for_proposal: valid_params }
          end.to change(RequestForProposal, :count).by(1)
        end
      end

      context 'with invalid params' do
        let(:invalid_params) { valid_params.merge(company_name: nil) }

        it 'does not create a new request for proposal' do
          expect do
            post url, params: { request_for_proposal: invalid_params }
          end.not_to change(RequestForProposal, :count)
        end
      end
    end
  end
end
