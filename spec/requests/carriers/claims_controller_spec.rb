require 'rails_helper'

RSpec.describe Carriers::ClaimsController do
  let(:carrier_profile) { create :carrier_profile }
  let(:carrier) { carrier_profile.carrier }
  let(:user) { create :user, :carrier }

  describe 'GET show' do
    let(:profile_user) { create :carrier_profile_user, carrier_profile: }

    context 'when profile user is verified' do
      before do
        profile_user.verify!
      end

      it 'redirects to carrier dashboard root url' do
        get carrier_claim_url(carrier, profile_user, as: user.to_param)
        expect(response).to redirect_to carrier_dashboard_root_url(carrier)
      end
    end

    context 'when profile user is not verified' do
      it 'responds with ok status' do
        get carrier_claim_url(carrier, profile_user, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    context 'when user is not logged in' do
      it 'redirects to sign in' do
        get new_carrier_claim_url(carrier)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      it 'responds with ok status' do
        get new_carrier_claim_url(carrier, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      it 'queues up job to upsert ActiveCampaign account', :wisper do
        expect do
          get new_carrier_claim_url(carrier, as: user.to_param)
        end.to change(ActiveCampaign::InitiateProfileClaimJob.jobs, :size).by(1)
      end

      context 'when user has already claimed the carrier profile' do
        let!(:carrier_profile_user) { create :carrier_profile_user, user:, carrier_profile: }

        it 'redirects to show' do
          get new_carrier_claim_url(carrier, as: user.to_param)
          expect(response).to redirect_to carrier_claim_url(carrier, carrier_profile_user)
        end
      end
    end
  end

  describe 'POST create' do
    let(:attributes) do
      { truck_types: [truck_types(:flatbed).id], shipment_types: [shipment_types(:ftl).id] }
    end

    it 'redirects to show' do
      post carrier_claims_url(carrier, as: user.to_param), params: { carriers_claim: attributes }
      expect(response).to redirect_to carrier_claim_url(carrier, CarrierProfileUser.last)
    end
  end
end
