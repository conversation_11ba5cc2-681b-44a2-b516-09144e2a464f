require 'rails_helper'

RSpec.describe Carriers::ContactsController do
  describe 'GET show' do
    let(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city, :with_authority }
    let(:carrier) { carrier_profile.carrier }
    let(:url) { carrier_contact_url(carrier) }

    context 'when user is not logged in' do
      it 'redirects to login page' do
        get url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:user) { create :user, :broker }
      let(:url) { carrier_contact_url(carrier, as: user.to_param) }

      context 'when user is not authorized' do
        it 'redirects to root' do
          get url
          expect(response).to redirect_to root_url
        end
      end

      context 'when user is authorized' do
        before { user.broker.verify! }

        it 'renders' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
