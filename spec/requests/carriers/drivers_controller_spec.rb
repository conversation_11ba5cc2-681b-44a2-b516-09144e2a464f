require 'rails_helper'

RSpec.describe Carriers::DriversController do
  let(:carrier_profile) { create :carrier_profile }
  let(:company) { carrier_profile.company }

  describe 'GET show' do
    context 'when user is not signed in' do
      it 'responds with ok status' do
        get carrier_driver_url(company)
        expect(response).to have_http_status :ok
      end

      it 'creates driver persona' do
        get carrier_driver_url(company)
        expect { post users_url(user: attributes_for(:user)) }.to change { Persona.driver.count }.by(1)
      end

      context 'when user has driver persona' do
        let(:password) { Faker::Internet.password(special_characters: true) }
        let!(:user) { create :user, :driver, password: }

        it 'does not create driver persona' do
          get carrier_driver_url(company)

          expect do
            post session_url(session: { email: user.email, password: })
          end.not_to(change { Persona.driver.count })
        end
      end
    end

    context 'when user is signed in' do
      let(:user) { create :user }

      context 'when user has driver persona' do
        let(:user) { create :user, :driver, verified: true }

        it 'redirects to new driver review' do
          get carrier_driver_url(company, as: user.to_param)
          expect(response).to redirect_to new_carrier_driver_review_url(company)
        end
      end

      context 'when user does not have driver persona' do
        it 'responds with ok status' do
          get carrier_driver_url(company, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
