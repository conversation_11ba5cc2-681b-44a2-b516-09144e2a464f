require 'rails_helper'

RSpec.describe Carriers::AssetsController do
  describe 'GET #show' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }

    context 'when the asset is a logo' do
      let(:asset) { 'logo' }

      context 'when upgraded' do
        before do
          create :access_package, :carrier, resource: carrier_profile
        end

        it 'returns a not found response' do
          get carrier_asset_url(carrier, asset)
          expect(response).to have_http_status :not_found
        end
      end

      context 'when not upgraded' do
        it 'returns a not authorized response' do
          get carrier_asset_url(carrier, asset)
          expect(response).to have_http_status :unauthorized
        end
      end
    end

    context 'when the asset is an email signature' do
      let(:asset) { 'email_signature' }

      context 'when upgraded' do
        before do
          create :access_package, :carrier, resource: carrier_profile
        end

        context 'when attached' do
          before do
            carrier.profile.email_signature.attach(io: fixture_file_upload('logo.png', 'image/png'),
                                                   filename: 'logo.png')
          end

          it 'redirects to blob' do
            get carrier_asset_url(carrier, asset)
            expect(response).to redirect_to rails_blob_url(carrier.profile.email_signature)
          end
        end

        context 'when not attached' do
          it 'returns a not found response' do
            get carrier_asset_url(carrier, asset)
            expect(response).to have_http_status :not_found
          end
        end
      end

      context 'when not upgraded' do
        it 'returns a not found response' do
          get carrier_asset_url(carrier, asset)
          expect(response).to have_http_status :unauthorized
        end
      end
    end
  end
end
