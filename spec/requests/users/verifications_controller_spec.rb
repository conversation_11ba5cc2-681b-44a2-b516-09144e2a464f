require 'rails_helper'

RSpec.describe Users::VerificationsController do
  shared_examples 'redirect verified user' do |method|
    let(:user) { create :user }

    it 'redirects to root' do
      public_send method, url
      expect(response).to redirect_to root_url
    end
  end

  describe 'GET show' do
    let(:url) { user_verification_url(user) }

    it_behaves_like 'redirect verified user', :get

    context 'when user is not verified' do
      let(:user) { create :user, verified: false }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { user_verification_url(user) }

    it_behaves_like 'redirect verified user', :post

    context 'when user is not verified' do
      let(:user) { create :user, verified: false }

      it 'queues up mailer' do
        post url
        expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with('UserMailer', 'verify', any_args)
      end

      it 'redirects to show' do
        post url
        expect(response).to redirect_to url
      end
    end
  end

  describe 'GET verify' do
    let(:url) { verify_user_verification_url(user) }

    it_behaves_like 'redirect verified user', :get

    context 'when user is not verified' do
      let(:user) { create :user, verified: false }

      it 'returns ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { user_verification_url(user) }

    it_behaves_like 'redirect verified user', :patch

    context 'when user is not verified' do
      let(:user) { create :user, verified: false }

      context 'with no token' do
        it 'redirects to show' do
          patch url
          expect(response).to redirect_to user_verification_url(user)
        end
      end

      context 'with incorrect token' do
        it 'redirects to show' do
          patch user_verification_url(user, token: 'abc')
          expect(response).to redirect_to user_verification_url(user)
        end
      end

      context 'with correct token' do
        it 'redirects to sign up completion' do
          patch user_verification_url(user, token: user.verification_token)
          expect(response).to redirect_to new_user_persona_url(user)
        end

        it 'marks user as verified' do
          patch user_verification_url(user, token: user.verification_token)
          expect(user.reload).to have_attributes verified: true, verification_token: nil
        end
      end
    end
  end
end
