require 'rails_helper'

RSpec.describe Users::PersonasController do
  shared_examples 'redirect signed in user' do |method|
    let(:user) { create :user, :broker }

    before do
      get root_url(as: user.to_param)
    end

    it 'redirects to root' do
      public_send method, url
      expect(response).to redirect_to root_url
    end
  end

  describe 'GET new' do
    let(:url) { new_user_persona_url(user) }

    it_behaves_like 'redirect signed in user', :get

    context 'when logged out' do
      let(:user) { create :user }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { user_personas_url(user) }

    it_behaves_like 'redirect signed in user', :post

    context 'when logged out' do
      let(:user) { create :user }

      context 'when signing up as a broker' do
        let(:company) { create :company, :broker }

        it 'redirects to root url' do
          post url, params: { persona: { persona: 'broker', dot_number: company.dot_number } }
          expect(response).to redirect_to root_url
        end

        it 'responds with unprocessable for missing data' do
          post url, params: { persona: { persona: 'broker' } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end

      context 'when signing up as a shipper' do
        let(:city) { create :city }

        it 'redirects to root url' do
          post url, params: { persona: { persona: 'shipper', company: Faker::Company.name, city_id: city.id } }
          expect(response).to redirect_to root_url
        end

        it 'responds with unprocessable for missing data' do
          post url, params: { persona: { persona: 'shipper' } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end

      context 'when signing up as a carrier' do
        let(:carrier_profile) { create :carrier_profile, company: }
        let(:company) { create :company, :carrier }

        it 'redirects to root url' do
          post url, params: { persona: { persona: 'carrier', dot_number: company.dot_number } }
          expect(response).to redirect_to root_url
        end

        it 'responds with unprocessable for missing data' do
          post url, params: { persona: { persona: 'carrier' } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end
end
