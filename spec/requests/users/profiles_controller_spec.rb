require 'rails_helper'

RSpec.describe Users::ProfilesController do
  describe 'GET #index' do
    context 'when user is not logged in' do
      it 'redirects to login page' do
        get user_profiles_url('~')
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:user) { create :user, :broker }

      before do
        create(:carrier_profile_user, :verified, user:)
        create(:brokerage_profile_user, :verified, user:)
      end

      it 'responds with ok status' do
        get user_profiles_url('~', as: user.to_param)
        expect(response).to have_http_status(:success)
      end
    end
  end
end
