require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Overview::HomeController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_overview_root_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_overview_root_url(company, as: user.to_param) }

      context 'with a large carrier' do
        let(:company_attributes) { { power_units: 100 } }

        before do
          create :analytics_shipper_event, type: 'carrier.widget.viewed', company: company, time: 2.days.ago
          Rollups.generate
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end

      context 'with a small carrier' do
        let(:company_attributes) { { power_units: 5 } }

        before do
          create(:user, :broker).then do |user|
            create :analytics_event, name: 'Page Viewed', user:, properties: { id: company.id, route: 'carriers#show' }
            create :analytics_event, name: 'Carrier Contact Viewed', user:, properties: { id: company.id }
          end

          Rollups.generate
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
