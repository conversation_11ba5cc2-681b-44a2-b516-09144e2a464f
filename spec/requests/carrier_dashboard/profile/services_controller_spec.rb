require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Profile::ServicesController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_profile_services_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_services_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_dashboard_profile_services_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_services_url(company, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { carrier_profile: { truck_types: [truck_types(:van).id] } }
        expect(carrier_profile.reload).to have_attributes truck_types: [truck_types(:van).id]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_profile: { truck_types: [truck_types(:van).id] } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end
end
