require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Profile::ContactsController do
  include_context 'with verified carrier profile user'

  describe 'GET index' do
    let(:url) { carrier_dashboard_profile_contacts_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_contacts_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_carrier_dashboard_profile_contact_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { new_carrier_dashboard_profile_contact_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:contact) { create :carrier_profile_contact, carrier_profile: }
    let(:url) { edit_carrier_dashboard_profile_contact_url(company, contact) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { edit_carrier_dashboard_profile_contact_url(company, contact, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_dashboard_profile_contacts_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :post

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_contacts_url(company, as: user.to_param) }

      it 'redirects to index page' do
        post url, params: { carrier_profile_contact: attributes_for(:carrier_profile_contact) }
        expect(response).to redirect_to carrier_dashboard_profile_contacts_url(company)
      end

      it 'creates a new contact' do
        expect do
          post url, params: { carrier_profile_contact: attributes_for(:carrier_profile_contact) }
        end.to change(CarrierProfileContact, :count).by(1)
      end

      context 'when contact is invalid' do
        it 'does not create a new contact' do
          expect do
            post url, params: { carrier_profile_contact: attributes_for(:carrier_profile_contact, type: nil) }
          end.not_to change(CarrierProfileContact, :count)
        end

        it 'returns unprocessable status' do
          post url, params: { carrier_profile_contact: attributes_for(:carrier_profile_contact, type: nil) }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:contact) { create :carrier_profile_contact, carrier_profile: }
    let(:url) { carrier_dashboard_profile_contact_url(company, contact) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_contact_url(company, contact, as: user.to_param) }

      it 'redirects to index page' do
        patch url, params: { carrier_profile_contact: { name: 'Fred Rogers' } }
        expect(response).to redirect_to carrier_dashboard_profile_contacts_url(company)
      end

      it 'updates the contact' do
        patch url, params: { carrier_profile_contact: { name: 'Fred Rogers' } }
        expect(contact.reload.name).to eq 'Fred Rogers'
      end

      context 'when contact is invalid' do
        it 'does not update the contact' do
          patch url, params: { carrier_profile_contact: { type: nil } }
          expect(contact.reload.name).not_to be_nil
        end

        it 'returns unprocessable status' do
          patch url, params: { carrier_profile_contact: { type: nil } }
          expect(response).to have_http_status :unprocessable_entity
        end
      end
    end

    describe 'DELETE destroy' do
      let!(:contact) { create :carrier_profile_contact, carrier_profile: }
      let(:url) { carrier_dashboard_profile_contact_url(company, contact) }

      it_behaves_like 'a carrier dashboard controller action', :delete

      context 'when carrier user is verified' do
        let(:url) { carrier_dashboard_profile_contact_url(company, contact, as: user.to_param) }

        it 'redirects to index page' do
          delete url
          expect(response).to redirect_to carrier_dashboard_profile_contacts_url(company)
        end

        it 'deletes the contact' do
          expect do
            delete url
          end.to change(CarrierProfileContact, :count).by(-1)
        end

        context 'when destroy fails' do
          before do
            allow_any_instance_of(CarrierProfileContact).to receive(:destroy).and_return(false)
          end

          it 'does not delete the contact' do
            expect do
              delete url
            end.not_to change(CarrierProfileContact, :count)
          end

          it 'returns unprocessable status' do
            delete url
            expect(response).to have_http_status :unprocessable_entity
          end
        end
      end
    end

    describe 'PATCH make_default' do
      let!(:contact) { create :carrier_profile_contact, carrier_profile: }
      let(:url) { make_default_carrier_dashboard_profile_contact_url(company, contact) }

      it_behaves_like 'a carrier dashboard controller action', :patch

      context 'when carrier user is verified' do
        let(:url) { make_default_carrier_dashboard_profile_contact_url(company, contact, as: user.to_param) }

        it 'redirects to index page' do
          patch url
          expect(response).to redirect_to carrier_dashboard_profile_contacts_url(company)
        end

        it 'makes the contact the default' do
          patch url
          expect(contact.reload.default).to be true
        end
      end
    end
  end
end
