require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Lanes::OperationStatesController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_lanes_operation_states_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_operation_states_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_dashboard_lanes_operation_states_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_operation_states_url(company, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { carrier_lane: { state_ids: ['united-states:utah'] } }
        expect(carrier_profile.operation_states.pluck(:state_id)).to eq ['united-states:utah']
      end

      it 'queues up map job' do
        patch url, params: { carrier_lane: { state_ids: ['united-states:utah'] } }
        expect(CarrierProfiles::StoreLanesMapJob.jobs.pick('args')).to eq [carrier_profile.id]
      end

      it 'queues up company event feed update job' do
        patch url, params: { carrier_lane: { state_ids: ['united-states:utah'] } }
        expect(Analytics::CompanyEventFeeds::UpdateCarrierDefaultJob.jobs.pick('args')).to eq [carrier.to_gid.to_s]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_lane: { state_ids: ['united-states:utah'] } }
          expect(response).to have_http_status :unprocessable_entity
        end

        it 'does not queue up map job' do
          patch url, params: { carrier_lane: { state_ids: ['united-states:utah'] } }
          expect(CarrierProfiles::StoreLanesMapJob.jobs.size).to eq 0
        end
      end
    end
  end
end
