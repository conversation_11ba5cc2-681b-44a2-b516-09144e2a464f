require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Lanes::TerminalsController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_lanes_terminals_url(carrier) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_terminals_url(carrier, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_dashboard_lanes_terminals_url(carrier) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_terminals_url(carrier, as: user.to_param) }
      let(:city) { create :city }

      it 'updates attributes' do
        patch url, params: { carrier_profile: { terminals_attributes: { '0' => { city_id: city.id } } } }
        expect(carrier_profile.terminals.pluck(:city_id)).to eq [city.id]
      end

      it 'queues up jobs' do
        patch url, params: { carrier_profile: { terminals_attributes: { '0' => { city_id: city.id } } } }
        expect(CarrierProfiles::StoreLanesMapJob.jobs.pick('args')).to eq [carrier_profile.id]
        expect(Elastic::SyncRecordJob.jobs.pick('args')).to eq [carrier.to_gid.to_s]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_profile: { terminals_attributes: { '0' => { city_id: city.id } } } }
          expect(response).to have_http_status :unprocessable_entity
        end

        it 'does not queue up map job' do
          patch url, params: { carrier_profile: { terminals_attributes: { '0' => { city_id: city.id } } } }
          expect(CarrierProfiles::StoreLanesMapJob.jobs.size).to eq 0
        end
      end
    end
  end
end
