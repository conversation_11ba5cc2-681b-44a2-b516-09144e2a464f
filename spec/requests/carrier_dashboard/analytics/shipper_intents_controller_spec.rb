require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Analytics::ShipperIntentsController, :elasticsearch do
  include_context 'with verified carrier profile user'

  let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
  let(:company_attributes) { { city: } }
  let(:analytics_company) { create :analytics_company }
  let(:feed) { create :analytics_company_event_feed, :carrier, company: }

  let!(:competitor_profile) { create :carrier_profile, company: competitor }
  let(:competitor) { create :company, :with_city, city: }

  before do
    CarriersTruckType.create!(company:, truck_type: truck_types(:flatbed))
    CarriersTruckType.create!(company: competitor, truck_type: truck_types(:flatbed))

    visit = create :analytics_visit, :completed, company: analytics_company

    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { id: company.id, route: 'carriers#show', url: carrier_url(company) }

    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { id: competitor.id, route: 'carriers#show', url: carrier_url(competitor) }

    2.times do
      create :analytics_event,
             name: 'Page Viewed', visit:,
             properties: { route: 'trucking_companies/locations#city',
                           url: city_trucking_companies_url(*city.path,
                                                            carrier: { truck_type: [truck_types(:flatbed).id] }),
                           path_parameters: { city: 'salt-lake-city', state: 'utah', country: 'united-states' } }
    end

    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { route: 'trucking_companies/locations#city',
                         url: city_trucking_companies_url(*city.path),
                         path_parameters: { city: 'salt-lake-city', state: 'utah', country: 'united-states' } }

    2.times do
      create :analytics_event,
             name: 'Page Viewed', visit:,
             properties: { route: 'trucking_companies/locations#state',
                           url: state_trucking_companies_url('united-states', 'utah'),
                           path_parameters: { state: 'utah', country: 'united-states' } }
    end

    Analytics::Events::CreateShipperEventJob.drain
    Elastic::Analytics::Events::BulkImport.call
  end

  describe 'GET index' do
    let(:url) { carrier_dashboard_analytics_feed_shipper_intents_url(company, feed) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user does not have access' do
      let(:url) { carrier_dashboard_analytics_feed_shipper_intents_url(company, feed, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_analytics_feed_shipper_intents_url(company, feed, as: user.to_param) }

      before do
        create :access_package, :carrier, resource: carrier_profile
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end

      context 'with direct signals' do
        before do
          model = Forms::AnalyticsEvent.new(direct_signals: 'carrier.profile.viewed', target_company_id: company.id)
          feed.update(filters: model.to_h)
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end

      context 'with filter parameters' do
        before do
          model = Forms::AnalyticsEvent.new(search: true, location_type: 'city', city_ids: [city.id])
          feed.update(filters: model.to_h)
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { carrier_dashboard_analytics_feed_shipper_intent_url(company, feed, analytics_company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) do
        carrier_dashboard_analytics_feed_shipper_intent_url(company, feed, analytics_company, as: user.to_param)
      end

      it 'responds with ok' do
        get url, headers: { 'Turbo-Frame' => 'company-result' }
        expect(response).to have_http_status :ok
      end

      context 'when filter parameters are missing' do
        before do
          model = Forms::AnalyticsEvent.new(direct_signals: 'carrier.profile.viewed',
                                            indirect_signals: 'carrier.profile.viewed',
                                            target_company_id: company.id)
          feed.update(filters: model.to_h)
        end

        it 'responds with ok' do
          get url, headers: { 'Turbo-Frame' => 'company-result' }
          expect(response).to have_http_status :ok
        end
      end

      context 'when filter parameters are present' do
        context 'with city search' do
          before do
            model = Forms::AnalyticsEvent.new(search: 'true', location_type: 'city', city_ids: [city.id],
                                              truck_type: truck_types(:flatbed).id)
            feed.update(filters: model.to_h)
          end

          it 'responds with ok' do
            get url, headers: { 'Turbo-Frame' => 'company-result' }
            expect(response).to have_http_status :ok
          end

          context 'when loads are missing' do
            before do
              model = Forms::AnalyticsEvent.new(search: 'true', location_type: 'city', city_ids: [city.id])
              feed.update(filters: model.to_h)
            end

            it 'responds with ok' do
              get url, headers: { 'Turbo-Frame' => 'company-result' }
              expect(response).to have_http_status :ok
            end
          end
        end

        context 'with state search' do
          before do
            model = Forms::AnalyticsEvent.new(search: 'true', location_type: 'state', state_ids: ['united-states:utah'])
            feed.update(filters: model.to_h)
          end

          it 'responds with ok' do
            get url, headers: { 'Turbo-Frame' => 'company-result' }
            expect(response).to have_http_status :ok
          end
        end
      end
    end
  end

  describe 'GET search' do
    let(:url) { search_carrier_dashboard_analytics_feed_shipper_intents_url(company, feed) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) do
        search_carrier_dashboard_analytics_feed_shipper_intents_url(
          company, feed, analytics_event: { analytics_company: { name: analytics_company.name } }, as: user.to_param
        )
      end

      it 'responds with ok' do
        get url, headers: { 'Accept' => 'text/vnd.turbo-stream.html, text/html, application/xhtml+xml' }
        expect(response).to have_http_status :ok
      end

      context 'with default feed' do
        let(:url) { search_carrier_dashboard_analytics_feed_shipper_intents_url(company, '~', as: user.to_param) }

        it 'responds with ok' do
          get url, headers: { 'Accept' => 'text/vnd.turbo-stream.html, text/html, application/xhtml+xml' }
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
