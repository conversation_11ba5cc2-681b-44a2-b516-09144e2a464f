require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Website::SocialsController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_website_socials_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_website_socials_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
