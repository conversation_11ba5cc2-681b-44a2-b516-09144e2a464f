require 'rails_helper'

RSpec.describe DriverCarrierReview do
  describe '#star_rating' do
    subject(:review) { build_stubbed :driver_carrier_review, nps: 9 }

    it 'returns half the nps value' do
      expect(review.star_rating).to eq 4.5
    end
  end

  describe '#started_on=' do
    context 'when value is a hash' do
      subject(:review) { build_stubbed :driver_carrier_review, started_on: { month: 1, year: 2020 } }

      it 'sets the date' do
        expect(review.started_on).to eq Date.new(2020, 1, 1)
      end
    end

    context 'when value is not a hash' do
      subject(:review) { build_stubbed :driver_carrier_review, started_on: '2020-01-01' }

      it 'sets the date' do
        expect(review.started_on).to eq Date.new(2020, 1, 1)
      end
    end
  end

  describe '#ended_on=' do
    context 'when value is a hash' do
      subject(:review) { build_stubbed :driver_carrier_review, ended_on: { month: 1, year: 2020 } }

      it 'sets the date' do
        expect(review.ended_on).to eq Date.new(2020, 1, 1)
      end
    end

    context 'when value is not a hash' do
      subject(:review) { build_stubbed :driver_carrier_review, ended_on: '2020-01-01' }

      it 'sets the date' do
        expect(review.ended_on).to eq Date.new(2020, 1, 1)
      end
    end
  end
end
