require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLane, type: :model do
  describe 'associations' do
    it { should belong_to(:carrier_network_builder) }
  end

  describe 'enums' do
    it { should define_enum_for(:origin_type).with_values(%w[city region state].index_with(&:itself)) }
    it { should define_enum_for(:destination_type).with_values(%w[city region state].index_with(&:itself)) }
  end

  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:carrier_network_builder_lane)).to be_valid
    end
  end

  describe '#origin' do
    let(:lane) { build(:carrier_network_builder_lane, origin_type: 'city', origin_id: '123') }

    context 'when origin_id is blank' do
      before { lane.origin_id = nil }

      it 'returns a nullable city object' do
        expect(lane.origin).to be_a(Nullable::City)
      end
    end

    context 'when origin_id is present' do
      it 'attempts to find the origin by type and id' do
        expect(City).to receive(:find).with('123')
        lane.origin
      end
    end
  end

  describe '#destination' do
    let(:lane) { build(:carrier_network_builder_lane, destination_type: 'city', destination_id: '456') }

    context 'when destination_id is blank' do
      before { lane.destination_id = nil }

      it 'returns a nullable city object' do
        expect(lane.destination).to be_a(Nullable::City)
      end
    end

    context 'when destination_id is present' do
      it 'attempts to find the destination by type and id' do
        expect(City).to receive(:find).with('456')
        lane.destination
      end
    end
  end
end
