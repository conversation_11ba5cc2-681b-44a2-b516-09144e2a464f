require 'rails_helper'

RSpec.describe User do
  subject(:user) { build_stubbed :user, :broker, first_name: '<PERSON>', last_name: '<PERSON>' }

  describe '#name' do
    it 'returns user name' do
      expect(user.name).to eq '<PERSON>'
    end
  end

  describe '#persona' do
    context 'when persona does not exist' do
      subject(:user) { create :user }

      context 'without verified flag' do
        it 'returns nil' do
          expect(user.persona).to be_nil
        end
      end

      context 'with verified flag' do
        it 'returns nil' do
          expect(user.persona(scope: :broker, verified: true)).to be_nil
        end
      end
    end

    context 'when persona exists' do
      subject(:user) { create :user, :broker }

      context 'without verified flag' do
        it 'returns persona' do
          expect(user.persona).to be_a Persona
        end
      end

      context 'with verified flag' do
        it 'returns persona' do
          expect(user.persona(scope: :broker, verified: false)).to be_a Persona
        end

        it 'returns nil' do
          expect(user.persona(scope: :broker, verified: true)).to be_nil
        end
      end
    end
  end
end
