require 'rails_helper'

RSpec.describe CitySpecializedService do
  describe '::refresh' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city }

    before do
      CarriersSpecializedService.create(company:, specialized_service: specialized_services(:drayage))
    end

    it 'refreshes the city_specialized_services table' do
      expect { described_class.refresh }.to change(described_class, :count)
    end
  end
end
