require 'rails_helper'

RSpec.describe City do
  let(:city) { build :city, name: 'Chicago', state_code: 'IL', latitude: 41.8375, longitude: -87.6866 }

  describe 'validations' do
    it 'is valid' do
      expect(city).to be_valid
    end

    it 'is invalid without name' do
      city.name = nil
      expect(city).not_to be_valid
    end

    it 'is invalid without state_code' do
      city.state_code = nil
      expect(city).not_to be_valid
    end

    it 'is invalid with wrong state' do
      city.state_code = 'XX'
      expect(city).not_to be_valid
    end

    it 'is invalid without latitude' do
      city.latitude = nil
      expect(city).not_to be_valid
    end

    it 'is invalid without longitude' do
      city.longitude = nil
      expect(city).not_to be_valid
    end
  end

  describe 'searchable' do
    describe '::es_import_query' do
      let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
      let(:postal_code) { create :postal_code, code: '84108' }

      before do
        create :cities_postal_code, city:, postal_code:
      end

      it 'is included' do
        expect(described_class.es_import_query).to contain_exactly(city)
      end
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'cities_test'
      end
    end

    describe '#as_indexed_json' do
      let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
      let(:postal_code) { create :postal_code, code: '84108' }

      before do
        create :cities_postal_code, city:, postal_code:
      end

      it 'returns document hash' do
        expect(city.as_indexed_json({})).to(
          match(hash_including('name' => 'Salt Lake City', 'state_code' => 'UT', 'zipcodes' => ['84108'],
                               'zipcode_count' => 1, 'city_and_state' => 'Salt Lake City UT',
                               'id' => city.id, 'trucking_companies_path' => 'united-states/utah/salt-lake-city'))
        )
      end
    end
  end

  describe '#label' do
    it 'returns city and state' do
      expect(city.label).to eq 'Chicago, IL'
    end
  end

  describe '#location' do
    it 'returns coordinates' do
      expect(city.location).to eq lat: 41.8375, lon: -87.6866
    end
  end
end
