require 'rails_helper'

RSpec.describe CarrierNetworkBuilder, type: :model do
  describe 'associations' do
    it { should belong_to(:user).optional }
    it { should have_many(:lanes).class_name('CarrierNetworkBuilderLane').dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
  end

  describe 'nested attributes' do
    it { should accept_nested_attributes_for(:lanes).allow_destroy(true) }
  end

  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:carrier_network_builder)).to be_valid
    end

    it 'has a valid factory with lanes' do
      expect(build(:carrier_network_builder, :with_lanes)).to be_valid
    end
  end

  describe 'dependent destroy' do
    it 'destroys associated lanes when destroyed' do
      builder = create(:carrier_network_builder, :with_lanes)
      expect { builder.destroy }.to change(CarrierNetworkBuilderLane, :count).by(-3)
    end
  end
end
