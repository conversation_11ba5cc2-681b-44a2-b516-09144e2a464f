require 'rails_helper'

RSpec.describe ShipmentType do
  describe 'seo_name' do
    context 'when seo_name is not present' do
      it 'returns the name' do
        expect(shipment_types(:ftl).seo_name).to eq('Full Truckload')
      end
    end

    context 'when seo_name is present' do
      it 'returns the seo_name' do
        expect(shipment_types(:ltl).seo_name).to eq('LTL')
      end
    end
  end
end
