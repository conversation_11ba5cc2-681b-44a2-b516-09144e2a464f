require 'rails_helper'

RSpec.describe UserEmail do
  describe 'validations' do
    context 'when email is already taken by another user' do
      let(:user) { create :user }
      let(:another_user) { create :user }
      let(:user_email) { build :user_email, user:, email: another_user.email }

      it 'is invalid' do
        expect(user_email).to be_invalid
        expect(user_email.errors[:email]).to include('has already been taken')
      end
    end

    context 'when email is already taken by the same user' do
      let(:user) { create :user }
      let(:user_email) { build :user_email, user:, email: user.email }

      it 'is valid' do
        expect(user_email).to be_valid
      end
    end
  end
end
