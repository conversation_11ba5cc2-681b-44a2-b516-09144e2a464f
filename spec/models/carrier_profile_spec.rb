require 'rails_helper'

RSpec.describe CarrierProfile do
  describe 'callbacks' do
    subject!(:carrier_profile) { create :carrier_profile, company: }

    let(:census) { create :census, crgo_coldfood: 'X', crgo_chem: 'X' }
    let(:company) { create :company, census: }
    let(:carrier) { carrier_profile.carrier }

    context 'with update' do
      it 'propagates truck_types to carrier' do
        Carriers::SyncTruckTypesJob.drain
        carrier_profile.update(truck_types: [truck_types(:van).id])
        Carriers::SyncTruckTypesJob.drain
        expect(carrier.reload.truck_types).to eq [truck_types(:van)]
      end

      it 'propagates shipment_types to carrier' do
        Carriers::SyncShipmentTypesJob.drain
        carrier_profile.update(shipment_types: [shipment_types(:ltl).id])
        Carriers::SyncShipmentTypesJob.drain
        expect(carrier.reload.shipment_types).to eq [shipment_types(:ltl)]
      end

      it 'propagates specialized_services to carrier' do
        Carriers::SyncSpecializedServicesJob.drain
        carrier_profile.update(specialized_services: [specialized_services(:drayage).id])
        Carriers::SyncSpecializedServicesJob.drain
        expect(carrier.reload.specialized_services).to eq [specialized_services(:drayage)]
      end
    end
  end

  describe '#claimed?' do
    subject(:profile) { create :carrier_profile }

    context 'when claimed' do
      before do
        create :carrier_profile_user, :verified, carrier_profile: profile
      end

      it 'returns true' do
        expect(profile).to be_claimed
      end
    end

    context 'when not claimed' do
      it 'returns false' do
        expect(profile).not_to be_claimed
      end
    end
  end
end
