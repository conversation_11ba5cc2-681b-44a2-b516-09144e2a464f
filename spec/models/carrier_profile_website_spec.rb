require 'rails_helper'

RSpec.describe CarrierProfileWebsite do
  describe 'callbacks' do
    describe 'domain' do
      let(:website) { create :carrier_profile_website }

      it 'issues a certificate when the domain is set' do
        website.update!(domain: 'example.com')
        expect(Websites::IssueCertificatesJob.jobs.pick('args')).to eq ['example.com']
      end

      it 'revokes a certificate when the domain is changed' do
        website.update!(domain: 'example.com')
        website.update!(domain: 'example.org')
        expect(Websites::RevokeCertificatesJob.jobs.pick('args')).to eq ['example.com']
      end

      it 'revokes a certificate when the domain is removed' do
        website.update!(domain: 'example.com')
        website.update!(domain: nil)
        expect(Websites::RevokeCertificatesJob.jobs.pick('args')).to eq ['example.com']
      end
    end
  end
end
