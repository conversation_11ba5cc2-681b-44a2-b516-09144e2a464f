require 'rails_helper'

RSpec.describe CarrierProfileContact do
  describe 'validations' do
    subject { build_stubbed :carrier_profile_contact }

    it { is_expected.to validate_presence_of(:type) }

    context 'when phone is blank' do
      subject { build_stubbed :carrier_profile_contact, phone: nil }

      it { is_expected.to be_valid }
      it { is_expected.to validate_presence_of(:email) }
    end

    context 'when email is blank' do
      subject { build_stubbed :carrier_profile_contact, email: nil }

      it { is_expected.to be_valid }
      it { is_expected.to validate_presence_of(:phone) }
    end

    context 'when email is present' do
      it { is_expected.to be_valid }
      it { is_expected.to allow_value('<EMAIL>').for(:email) }
      it { is_expected.not_to allow_value('safety@transit').for(:email) }
    end

    context 'when email and phone are blank' do
      subject { build_stubbed :carrier_profile_contact, email: nil, phone: nil }

      it { is_expected.not_to be_valid }
    end
  end
end
