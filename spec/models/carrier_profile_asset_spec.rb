require 'rails_helper'

RSpec.describe CarrierProfileAsset do
  describe 'validations' do
    describe 'content_type' do
      subject(:asset) { described_class.new(carrier_profile:, file: fixture_file_upload(*file)) }

      let(:carrier_profile) { build :carrier_profile }

      context 'with image' do
        let(:file) { %w(logo.png image/png) }

        it 'is valid' do
          expect(asset).to be_valid
        end
      end

      context 'with pdf' do
        let(:file) { %w(blank.pdf application/pdf) }

        it 'is valid' do
          expect(asset).to be_valid
        end
      end

      context 'with csv' do
        let(:file) { %w(blank.csv text/csv) }

        it 'is invalid' do
          expect(asset).not_to be_valid
        end
      end
    end
  end
end
