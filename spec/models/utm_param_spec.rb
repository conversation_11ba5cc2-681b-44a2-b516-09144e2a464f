require 'rails_helper'

RSpec.describe UtmParam do
  describe '#display_name' do
    subject(:utm_param) { described_class.new(source:, medium:, campaign:) }

    let(:source) { 'source' }
    let(:medium) { 'medium' }
    let(:campaign) { 'campaign' }

    context 'when all params are present' do
      it 'returns a string with all params' do
        expect(utm_param.display_name).to eq 'source / medium / campaign'
      end
    end

    context 'when some params are missing' do
      let(:medium) { nil }

      it 'returns a string with all params' do
        expect(utm_param.display_name).to eq 'source / campaign'
      end
    end
  end
end
