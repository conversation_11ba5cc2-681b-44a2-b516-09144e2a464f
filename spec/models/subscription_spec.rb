require 'rails_helper'

RSpec.describe Subscription do
  describe 'mailers' do
    context 'with User resource' do
      context 'when status is canceled' do
        it 'sends canceled mailer' do
          expect do
            create :subscription, :broker, status: 'canceled'
          end.to have_enqueued_mail SubscriptionMailer, :canceled
        end
      end
    end

    context 'with CarrierProfile resource' do
      context 'when status is canceled' do
        it 'sends canceled mailer' do
          expect do
            create :subscription, :carrier, status: :canceled
          end.to have_enqueued_mail CarrierSubscriptionMailer, :canceled
        end
      end
    end

    context 'with BrokerageProfile resource' do
      context 'when status is canceled' do
        it 'does not send canceled mailer' do
          expect do
            create :subscription, :brokerage, status: :canceled
          end.not_to have_enqueued_mail SubscriptionMailer, :canceled
        end
      end
    end
  end
end
