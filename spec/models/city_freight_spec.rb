require 'rails_helper'

RSpec.describe CityFreight do
  describe '::refresh' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city }

    before do
      CompaniesFreight.create(company:, freight: freights(:produce))
    end

    it 'refreshes the city_freights table' do
      expect { described_class.refresh }.to change(described_class, :count)
    end
  end
end
