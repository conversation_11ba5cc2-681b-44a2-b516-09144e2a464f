require 'rails_helper'

RSpec.describe Authentication do
  describe '#update_user_data' do
    context 'with linkedin authentication' do
      subject(:authentication) { create :authentication, :linkedin }

      it 'queues up job to attach avatar' do
        authentication.update_user_data
        expect(Users::AttachAvatarJob.jobs.pick('args')).to eq [authentication.user.id,
                                                                authentication.payload.dig('info', 'picture_url')]
      end
    end

    context 'with facebook authentication' do
      subject(:authentication) { create :authentication, :facebook }

      it 'queues up job to attach avatar' do
        authentication.update_user_data
        expect(Users::AttachAvatarJob.jobs.pick('args')).to eq [authentication.user.id,
                                                                authentication.payload.dig('info', 'image')]
      end
    end

    context 'with google authentication' do
      subject(:authentication) { create :authentication, :google }

      it 'queues up job to attach avatar' do
        authentication.update_user_data
        expect(Users::AttachAvatarJob.jobs.pick('args')).to eq [authentication.user.id,
                                                                authentication.payload.dig('info', 'image')]
      end
    end

    context 'with twitter authentication' do
      subject(:authentication) { create :authentication, :twitter }

      it 'does not queue job' do
        authentication.update_user_data
        expect(Users::AttachAvatarJob.jobs.size).to eq 0
      end
    end
  end
end
