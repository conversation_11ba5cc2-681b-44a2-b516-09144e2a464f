require 'rails_helper'

RSpec.describe Carrier do
  describe 'searchable' do
    describe '::es_import_query' do
      shared_examples 'eager-loaded association' do |name|
        before do
          create :company, :carrier, :with_authority, :with_city
        end

        it 'eager-loads association' do
          expect(described_class.es_import_query.first).to(satisfy { |c| c.association(name).loaded? })
        end
      end

      it_behaves_like 'eager-loaded association', :operating_authorities
      it_behaves_like 'eager-loaded association', :city
      it_behaves_like 'eager-loaded association', :freights
      it_behaves_like 'eager-loaded association', :insurances
      it_behaves_like 'eager-loaded association', :authhists
      it_behaves_like 'eager-loaded association', :reviews_aggregate
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'carriers_test'
      end
    end

    describe '#as_indexed_json' do
      subject(:company) { create :company, **attributes }

      let(:carrier) { company.as_entity(:carrier) }

      let(:attributes) { {} }

      it 'returns document hash' do
        expect(carrier.as_indexed_json({})).to match hash_including('dot_number' => carrier.dot_number)
      end

      context 'when authority exists' do
        let(:common) { Date.new(2018, 3, 27) }
        let(:contract) { Date.new(2016, 4, 19) }
        let(:operating_authority) { create :operating_authority, company: }

        before do
          Authhist.create(docket_number: operating_authority.docket_number, common:, contract:)
        end

        it 'adds authority_date' do
          expect(carrier.as_indexed_json({})).to match hash_including(authority_date: contract)
        end
      end

      context 'with authorities' do
        before do
          create :operating_authority, company:, common_authority: 'active', contract_authority: 'active'
        end

        it 'returns hash with active authority types' do
          expect(carrier.as_indexed_json({})).to match hash_including(authorities: %w(common contract))
        end
      end

      context 'when insurances exist' do
        let(:operating_authority) { create :operating_authority, company: }
        let!(:insurance_bipd) { create :insurance, :bipd, operating_authority:, bi_pd_max_limit: 500 }
        let!(:insurance_cargo) { create :insurance, :cargo, operating_authority:, bi_pd_max_limit: 750 }

        it 'adds insurance' do
          expect(carrier.as_indexed_json({})).to match hash_including(insurance: 750_000)
        end
      end

      context 'when carrier_operation is present' do
        let(:attributes) { { carrier_operation: 'interstate' } }

        it 'adds carrier operation' do
          expect(carrier.as_indexed_json({})).to match hash_including('carrier_operation' => 'interstate')
        end
      end

      context 'when carrier_operation is missing' do
        let(:attributes) { { carrier_operation: nil } }

        it 'adds carrier operation' do
          expect(carrier.as_indexed_json({})).to match hash_including('carrier_operation' => nil)
        end
      end

      context 'when safety rating is present' do
        let(:attributes) { { safety_rating: 'satisfactory' } }

        it 'adds carrier safety rating' do
          expect(carrier.as_indexed_json({})).to match hash_including('safety_rating' => 'satisfactory')
        end
      end

      context 'when star rating is present' do
        before do
          ReviewsAggregate.create(company:, star_rating: 4)
        end

        it 'adds star rating' do
          expect(carrier.as_indexed_json({})).to match hash_including(star_rating: 4)
        end
      end

      context 'when preferred lanes are present' do
        let(:carrier_profile) { create :carrier_profile, company: }
        let!(:preferred_lane) { create :preferred_lane, carrier_profile: }

        it 'adds preferred lanes' do
          expect(carrier.as_indexed_json({})).to(
            match(
              hash_including(
                preferred_lanes: [
                  {
                    pickup_city_id: preferred_lane.pickup_city_id,
                    pickup_location: preferred_lane.pickup_city.location,
                    dropoff_city_id: preferred_lane.dropoff_city_id,
                    dropoff_location: preferred_lane.dropoff_city.location
                  }
                ]
              )
            )
          )
        end
      end

      context 'when review lanes are present' do
        let!(:review) { create :review, :approved, company: }
        let(:review_lane) { review.review_lanes.first }

        before do
          Companies::UpsertReviewLanes.call(carrier)
        end

        it 'adds review lanes' do
          expect(carrier.as_indexed_json({})).to(
            match(
              hash_including(
                review_lanes: [
                  {
                    pickup_city_id: review_lane.pickup_city_id,
                    pickup_location: review_lane.pickup_city.location,
                    dropoff_city_id: review_lane.dropoff_city_id,
                    dropoff_location: review_lane.dropoff_city.location
                  }
                ]
              )
            )
          )
        end
      end
    end
  end
end
