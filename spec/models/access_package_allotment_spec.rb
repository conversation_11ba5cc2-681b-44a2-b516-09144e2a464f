require 'rails_helper'

RSpec.describe AccessPackageAllotment do
  describe '#full?' do
    subject(:package_allotment) { build :access_package_allotment, used:, allotment: 10 }

    context 'when used is more than allotment' do
      let(:used) { 11 }

      it 'is true' do
        expect(package_allotment).to be_full
      end
    end

    context 'when used is less than allotment' do
      let(:used) { 9 }

      it 'is false' do
        expect(package_allotment).not_to be_full
      end
    end

    context 'when used is equal to allotment' do
      let(:used) { 10 }

      it 'is true' do
        expect(package_allotment).to be_full
      end
    end
  end

  describe '#capacity?' do
    subject(:package_allotment) { build :access_package_allotment, used:, allotment: 10 }

    context 'when used is more than allotment' do
      let(:used) { 11 }

      it 'is false' do
        expect(package_allotment).not_to be_capacity
      end
    end

    context 'when used is less than allotment' do
      let(:used) { 9 }

      it 'is true' do
        expect(package_allotment).to be_capacity
      end
    end

    context 'when used is equal to allotment' do
      let(:used) { 10 }

      it 'is false' do
        expect(package_allotment).not_to be_capacity
      end
    end
  end
end
