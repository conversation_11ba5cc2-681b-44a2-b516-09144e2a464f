require 'rails_helper'

RSpec.describe BrokerageProfileUser do
  describe 'event: verify' do
    subject(:profile_user) { create :brokerage_profile_user, :pending, brokerage_profile: }

    let(:brokerage_profile) { create :brokerage_profile }

    context 'when profile attributes are stored' do
      before do
        BrokerageProfiles::ClaimResponse.store(
          profile_user, { truck_types: [truck_types(:flatbed).id], shipment_types: [shipment_types(:ftl).id] }
        )
      end

      it 'updates the brokerage profile' do
        profile_user.verify!
        brokerage_profile.reload
        expect(brokerage_profile.truck_types).to eq [truck_types(:flatbed).id]
        expect(brokerage_profile.shipment_types).to eq [shipment_types(:ftl).id]
      end
    end

    context 'when profile attributes are not stored' do
      it 'does not update the brokerage profile' do
        expect { profile_user.verify! }.not_to raise_error
      end
    end
  end
end
