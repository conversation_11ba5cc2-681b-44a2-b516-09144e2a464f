require 'rails_helper'

RSpec.describe CityTruckType do
  describe '::refresh' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city }

    before do
      CarriersTruckType.create(company:, truck_type: truck_types(:flatbed))
    end

    it 'refreshes the city_truck_types table' do
      expect { described_class.refresh }.to change(described_class, :count)
    end
  end
end
