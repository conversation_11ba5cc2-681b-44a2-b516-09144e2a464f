require 'rails_helper'

RSpec.describe CarrierProfileTerminal do
  subject(:terminal) { described_class.new }

  describe '#city_options' do
    context 'with new instance' do
      it 'returns empty array' do
        expect(terminal.city_options).to be_empty
      end
    end

    context 'with persisted instance' do
      subject(:terminal) { described_class.create(carrier_profile:, city:) }

      let(:carrier_profile) { create :carrier_profile }
      let(:city) { create :city }

      it 'returns city options' do
        expect(terminal.city_options).to eq [[city.label, city.id]]
      end
    end
  end
end
