require 'rails_helper'

RSpec.describe Widget do
  describe 'callbacks' do
    let(:widget) { create :widget, :carrier, widget_type: 'collect_review', style: 'gradient' }

    context 'when enabled' do
      it 'enqueues a job after update' do
        widget.update(enabled: true)
        expect(Widgets::StoreImageJob.jobs.size).to eq 1
      end

      context 'when image_style is the same as style' do
        it 'does not enqueue a job' do
          widget.update(enabled: true, image_style: 'gradient')
          expect(Widgets::StoreImageJob.jobs.size).to eq 0
        end
      end
    end

    it 'does not enqueue a job if not enabled' do
      widget.update(enabled: false)
      expect(Widgets::StoreImageJob.jobs.size).to eq 0
    end
  end
end
