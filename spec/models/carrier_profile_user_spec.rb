require 'rails_helper'

RSpec.describe CarrierProfileUser do
  describe 'event: verify' do
    subject(:profile_user) { create :carrier_profile_user, :pending, carrier_profile: }

    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }

    context 'when profile attributes are stored' do
      before do
        CarrierProfiles::ClaimResponse.store(
          profile_user, { truck_types: [truck_types(:flatbed).id], shipment_types: [shipment_types(:ftl).id] }
        )
      end

      it 'updates the carrier profile' do
        profile_user.verify!
        carrier_profile.reload
        expect(carrier_profile.truck_types).to eq [truck_types(:flatbed).id]
        expect(carrier_profile.shipment_types).to eq [shipment_types(:ftl).id]
      end
    end

    context 'when profile attributes are not stored' do
      it 'does not update the carrier profile' do
        expect { profile_user.verify! }.not_to raise_error
      end
    end
  end
end
