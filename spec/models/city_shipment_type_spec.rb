require 'rails_helper'

RSpec.describe CityShipmentType do
  describe '::refresh' do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city }

    before do
      CarriersShipmentType.create(company:, shipment_type: shipment_types(:ltl))
    end

    it 'refreshes the city_shipment_types table' do
      expect { described_class.refresh }.to change(described_class, :count)
    end
  end
end
