require 'rails_helper'

RSpec.describe CarrierAvailability do
  describe 'callbacks' do
    describe 'after_commit' do
      describe 'on: %i(create update)' do
        it 'enqueues an Elastic::SyncRecord<PERSON>ob' do
          expect do
            create(:carrier_availability)
          end.to change(Elastic::SyncRecordJob.jobs, :size).by(1)
        end
      end

      describe 'on: :destroy' do
        it 'enqueues an Elastic::DeleteRecordJob' do
          carrier_availability = create(:carrier_availability)

          expect do
            carrier_availability.destroy
          end.to change(Elastic::DeleteRecordJob.jobs, :size).by(1)
        end
      end
    end
  end
end
