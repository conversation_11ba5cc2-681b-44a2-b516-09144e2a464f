require 'rails_helper'

RSpec.describe Review do
  describe 'searchable' do
    describe '::es_import_query' do
      shared_examples 'eager-loaded association' do |name|
        before do
          create :review, :approved
        end

        it 'eager-loads association' do
          expect(described_class.es_import_query.first).to(satisfy { |c| c.association(name).loaded? })
        end
      end

      it_behaves_like 'eager-loaded association', :review_lanes
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'reviews_test'
      end
    end

    describe '#as_indexed_json' do
      subject(:review) { create :review, :approved, **attributes }

      let(:attributes) { {} }

      it 'returns document hash' do
        expect(review.as_indexed_json({})).to match hash_including('company_id' => review.company_id)
      end

      context 'when review lanes are present' do
        let(:review_lane) { review.review_lanes.first }

        it 'adds review lanes' do
          expect(review.as_indexed_json({})).to(
            match(
              hash_including(
                review_lanes: [
                  {
                    pickup_city_id: review_lane.pickup_city_id,
                    pickup_location: review_lane.pickup_city.location,
                    dropoff_city_id: review_lane.dropoff_city_id,
                    dropoff_location: review_lane.dropoff_city.location
                  }
                ]
              )
            )
          )
        end
      end
    end
  end

  describe 'sync equipment callbacks' do
    context 'when review is pending' do
      subject!(:review) { create :review, :pending }

      it 'syncs truck types when approved' do
        expect { review.update(status: 'approved') }.to change { Carriers::SyncTruckTypesJob.jobs.size }.by(1)
      end

      it 'syncs shipment types when approved' do
        expect { review.update(status: 'approved') }.to change { Carriers::SyncShipmentTypesJob.jobs.size }.by(1)
      end

      it 'syncs specialized services when approved' do
        expect { review.update(status: 'approved') }.to change { Carriers::SyncSpecializedServicesJob.jobs.size }.by(1)
      end
    end
  end

  describe 'sync lanes callbacks' do
    context 'when review is pending' do
      subject!(:review) { create :review, :pending }

      it 'syncs lanes when approved' do
        expect { review.update(status: 'approved') }.to change { Companies::UpsertReviewLanesJob.jobs.size }.by(1)
      end
    end
  end

  describe '#approved?' do
    context 'when approved' do
      subject(:review) { build_stubbed :review, :approved }

      it 'returns true' do
        expect(review).to be_approved
      end
    end

    context 'when not approved' do
      subject(:review) { build_stubbed :review, :pending }

      it 'returns false' do
        expect(review).not_to be_approved
      end
    end
  end

  describe '#pending?' do
    context 'when pending' do
      subject(:review) { build_stubbed :review, :pending }

      it 'returns true' do
        expect(review).to be_pending
      end
    end

    context 'when not pending' do
      subject(:review) { build_stubbed :review, :approved }

      it 'returns false' do
        expect(review).not_to be_pending
      end
    end
  end

  describe '#rejected?' do
    context 'when rejected' do
      subject(:review) { build_stubbed :review, :rejected }

      it 'returns true' do
        expect(review).to be_rejected
      end
    end

    context 'when not rejected' do
      subject(:review) { build_stubbed :review, :approved }

      it 'returns false' do
        expect(review).not_to be_rejected
      end
    end
  end

  describe '#last_worked_with=' do
    context 'when value is a hash' do
      subject(:review) { build_stubbed :review, last_worked_with: { month: 1, year: 2020 } }

      it 'sets the date' do
        expect(review.last_worked_with).to eq Date.new(2020, 1, 1)
      end
    end

    context 'when value is not a hash' do
      subject(:review) { build_stubbed :review, last_worked_with: '2020-01-01' }

      it 'sets the date' do
        expect(review.last_worked_with).to eq Date.new(2020, 1, 1)
      end
    end
  end
end
