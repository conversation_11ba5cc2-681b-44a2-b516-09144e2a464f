require 'rails_helper'

RSpec.describe BrokerageProfile do
  describe 'callbacks' do
    subject!(:brokerage_profile) { create :brokerage_profile }

    let(:brokerage) { brokerage_profile.brokerage }

    context 'with update' do
      it 'propagates truck_types to brokerage' do
        brokerage_profile.update(truck_types: [truck_types(:van).id])
        Brokerages::SyncTruckTypesJob.drain
        expect(brokerage.reload.truck_types).to eq [truck_types(:van)]
      end

      it 'propagates shipment_types to brokerage' do
        brokerage_profile.update(shipment_types: [shipment_types(:ltl).id])
        Brokerages::SyncShipmentTypesJob.drain
        expect(brokerage.reload.shipment_types).to eq [shipment_types(:ltl)]
      end

      it 'propagates specialized_services to brokerage' do
        brokerage_profile.update(specialized_services: [specialized_services(:drayage).id])
        Brokerages::SyncSpecializedServicesJob.drain
        expect(brokerage.reload.specialized_services).to eq [specialized_services(:drayage)]
      end
    end
  end
end
