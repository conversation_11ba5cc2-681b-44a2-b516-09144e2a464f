require 'rails_helper'

RSpec.describe AccessPackage do
  describe 'callbacks' do
    context 'when updating packages' do
      subject!(:access_package) { create :access_package, :carrier, packages: %w(branding) }

      it 'adds features' do
        expect { access_package.update!(packages: %w(branding content_and_media)) }
          .to change(access_package, :features)
                .from(%w(featured_review assets sponsored_content banner capacity))
                .to(%w(featured_review assets sponsored_content banner capacity email_signature review_widget))
      end
    end
  end

  describe 'validations' do
    context 'when resource is a BrokerageProfile' do
      subject(:access_package) { build :access_package, :brokerage }

      it 'is valid by default' do
        expect(access_package).to be_valid
      end
    end

    context 'when resource is a CarrierProfile' do
      subject(:access_package) { build :access_package, :carrier }

      it 'is valid by default' do
        expect(access_package).to be_valid
      end
    end

    context 'when resource is a User' do
      subject(:access_package) { build :access_package, :broker }

      it 'is valid by default' do
        expect(access_package).to be_valid
      end
    end
  end
end
