require 'rails_helper'

RSpec.describe Company do
  describe 'callbacks' do
    describe 'name_field', :elasticsearch do
      subject(:company) do
        create :company, :carrier, census:, legal_name: '<PERSON>uer Pfeffer', dba_name: '<PERSON> Tremblay'
      end

      let(:census) { create :census, carship: 'C' }

      before do
        allow(Elastic::SyncRecordJob).to receive(:perform_inline)
      end

      it 'syncs entities in elasticsearch' do
        company.update(name_field: 'legal_name')
        expect(Elastic::SyncRecordJob).to have_received(:perform_inline).with(company.as_entity(:carrier).to_gid.to_s)
      end
    end
  end

  describe '#name' do
    subject(:company) { build :company, legal_name:, dba_name:, name_field: }

    let(:legal_name) { '<PERSON>uer Pfeffer' }
    let(:dba_name) { 'Klein Tremblay' }

    context 'when name_field is legal_name' do
      let(:name_field) { 'legal_name' }

      it 'returns legal_name' do
        expect(company.name).to eq legal_name
      end
    end

    context 'when name_field is dba_name' do
      let(:name_field) { 'dba_name' }

      it 'returns dba_name' do
        expect(company.name).to eq dba_name
      end
    end

    context 'when dba_name is blank' do
      let(:name_field) { 'dba_name' }
      let(:dba_name) { nil }

      it 'returns legal_name' do
        expect(company.name).to eq legal_name
      end
    end
  end
end
