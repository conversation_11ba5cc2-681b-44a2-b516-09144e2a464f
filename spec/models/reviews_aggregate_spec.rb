require 'rails_helper'

RSpec.describe ReviewsAggregate do
  describe '#ratings' do
    subject(:reviews_aggregate) { described_class.new(ratings) }

    let(:ratings) { { cleanliness: 4.5, communication: 3.5, timeliness: 4.5 } }

    it 'returns the ratings' do
      expect(reviews_aggregate.ratings).to eq(ratings)
    end

    context 'when ratings are empty' do
      let(:ratings) { {} }

      it 'returns empty hash' do
        expect(reviews_aggregate.ratings).to eq({})
      end
    end
  end

  describe '#labeled_sentiments' do
    subject(:reviews_aggregate) { described_class.new(sentiments:) }

    let(:sentiments) { { positive_sentiment.id.to_s => 3, negative_sentiment.id.to_s => 1 } }

    let(:positive_sentiment) { create :sentiment, :positive, label: 'On Time Pickup' }
    let(:negative_sentiment) { create :sentiment, :negative, label: 'Double Brokering' }

    context 'when positive is default value' do
      it 'returns all the sentiments' do
        expect(reviews_aggregate.labeled_sentiments).to eq({ 'On Time Pickup' => 3, 'Double Brokering' => 1 })
      end
    end

    context 'when positive is true' do
      it 'returns only the positive sentiments' do
        expect(reviews_aggregate.labeled_sentiments(positive: true)).to eq({ 'On Time Pickup' => 3 })
      end
    end

    context 'when positive is false' do
      it 'returns only the negative sentiments' do
        expect(reviews_aggregate.labeled_sentiments(positive: false)).to eq({ 'Double Brokering' => 1 })
      end
    end

    context 'when sentiments are empty' do
      let(:sentiments) { {} }

      it 'returns empty hash' do
        expect(reviews_aggregate.labeled_sentiments).to eq({})
      end
    end
  end
end
