require 'rails_helper'

RSpec.describe BrokerageReview do
  describe 'searchable' do
    describe '::es_import_query' do
      it 'returns approved reviews' do
        create :brokerage_review, :carrier, :approved
        create :brokerage_review, :carrier, :rejected

        expect(described_class.es_import_query.count).to eq 1
      end
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'brokerage_reviews_test'
      end
    end

    describe '#as_indexed_json' do
      subject(:review) { create :brokerage_review, :carrier, :approved, **attributes }

      let(:attributes) { {} }

      it 'returns document hash' do
        expect(review.as_indexed_json({})).to match hash_including('company_id' => review.company_id)
      end
    end
  end
end
