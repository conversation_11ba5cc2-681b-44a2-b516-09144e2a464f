require 'rails_helper'

RSpec.describe Analytics::CompanyEventFeedNotification do
  describe 'callbacks' do
    subject(:notification) { create :analytics_company_event_feed_notification, :slack }

    context 'when slack properties is unchanged' do
      it 'does not queue job to join slack channel' do
        notification.update(interval: 'daily')
        expect(Slack::AutoJoinChannelJob.jobs).to be_empty
      end
    end

    context 'when updating slack properties' do
      it 'queues job to join slack channel' do
        notification.properties.slack.channel = 'general'
        notification.save
        expect(Slack::AutoJoinChannelJob.jobs.pick('args')).to eq [notification.id]
      end
    end
  end

  describe '#properties' do
    context 'with teams provider' do
      let(:notification) { create :analytics_company_event_feed_notification, :teams }
      let(:team_id) { notification.properties.teams.team_id }
      let(:channel_id) { notification.properties.teams.channel_id }

      describe '#channel_team_id' do
        it 'returns the channel id' do
          expect(notification.properties.teams.channel_team_id).to eq "#{channel_id}|#{team_id}"
        end
      end

      describe '#channel_team_id=' do
        it 'sets the channel id' do
          notification.properties.teams.channel_team_id = 'abc|def'
          expect(notification.properties.teams.channel_id).to eq 'abc'
          expect(notification.properties.teams.team_id).to eq 'def'
        end
      end
    end
  end
end
