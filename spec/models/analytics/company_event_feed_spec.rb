require 'rails_helper'

RSpec.describe Analytics::CompanyEventFeed do
  describe '#entity' do
    context 'when entity_type is broker' do
      subject(:feed) { build_stubbed :analytics_company_event_feed, :broker }

      it 'returns a Brokerage' do
        expect(feed.entity).to be_a Brokerage
      end
    end

    context 'when entity_type is carrier' do
      subject(:feed) { build_stubbed :analytics_company_event_feed, :carrier }

      it 'returns a Company' do
        expect(feed.entity).to be_a Carrier
      end
    end
  end
end
