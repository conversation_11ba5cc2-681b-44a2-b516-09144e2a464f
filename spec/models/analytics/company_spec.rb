require 'rails_helper'

RSpec.describe Analytics::Company do
  describe 'searchable' do
    describe '::es_import_query' do
      shared_examples 'eager-loaded association' do |name|
        let!(:company) { create :analytics_company, :with_city }

        it 'eager-loads association' do
          expect(described_class.es_import_query.first).to(satisfy { |c| c.association(name).loaded? })
        end
      end

      it_behaves_like 'eager-loaded association', :industry
      it_behaves_like 'eager-loaded association', :city
    end

    describe '::index_name' do
      it 'returns elasticsearch index name' do
        expect(described_class.index_name).to eq 'analytics_companies_test'
      end
    end

    describe '#as_indexed_json' do
      let!(:company) { create :analytics_company, :with_city }

      it 'returns document hash' do
        expect(company.as_indexed_json({})).to match hash_including(id: company.id)
      end
    end
  end

  describe '#website' do
    it 'returns the subdomain if present' do
      company = described_class.new(domain: 'www.example.com')
      expect(company.website).to eq('www.example.com')
    end

    it 'prepends www if root domain' do
      company = described_class.new(domain: 'example.com')
      expect(company.website).to eq('www.example.com')
    end

    it 'returns nil if domain is nil' do
      company = described_class.new(domain: nil)
      expect(company.website).to be_nil
    end

    it 'returns nil if domain is invalid' do
      company = described_class.new(domain: 'wixsite.com')
      expect(company.website).to be_nil
    end
  end

  describe '#logo_url' do
    it 'returns the logo_dev_url if domain is present' do
      company = described_class.new(domain: 'example.com')
      expect(company.logo_url).to eq('https://img.logo.dev/example.com?retina=true&token=pk_test_1234')
    end

    it 'returns nil if domain is missing' do
      company = described_class.new(domain: nil)
      expect(company.logo_url).to be_nil
    end
  end
end
