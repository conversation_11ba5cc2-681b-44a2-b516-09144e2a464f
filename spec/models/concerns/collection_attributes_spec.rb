require 'rails_helper'

RSpec.describe CollectionAttributes do
  subject(:instance) { BlogPost.new(tags: ['', 'seo', 'search']) }

  with_model :BlogPost do
    table do |t|
      t.string :tags, array: true
    end

    model do
      # rubocop:disable RSpec/DescribedClass
      include CollectionAttributes
      # rubocop:enable RSpec/DescribedClass

      collection_attributes :tags
    end
  end

  it 'removes empty values from collection' do
    instance.valid?
    expect(instance).to have_attributes tags: %w(seo search)
  end
end
