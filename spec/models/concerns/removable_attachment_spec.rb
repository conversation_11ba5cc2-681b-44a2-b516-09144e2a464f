require 'rails_helper'

RSpec.describe RemovableAttachment do
  subject(:instance) do
    Person.create!(avatar: fixture_file_upload('logo.png', 'image/png'))
  end

  with_model :Person do
    model do
      # rubocop:disable RSpec/DescribedClass
      include RemovableAttachment
      # rubocop:enable RSpec/DescribedClass

      has_one_attached :avatar
      removable_attachment :avatar
    end
  end

  describe '::removable_attachment' do
    it 'defines a method to purge the attachment' do
      expect(instance).to respond_to :purge_avatar=
    end

    it 'purges the attachment' do
      instance.update(purge_avatar: '1')
      expect(instance.reload.avatar).not_to be_attached
    end

    it 'does not purge the attachment' do
      instance.update(purge_avatar: '0')
      expect(instance.reload.avatar).to be_attached
    end
  end
end
