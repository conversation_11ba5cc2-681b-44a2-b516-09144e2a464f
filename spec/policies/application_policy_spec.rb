require 'rails_helper'

RSpec.describe ApplicationPolicy do
  subject(:policy) { described_class.new(double, double) }

  shared_examples 'a disallowed action' do |action|
    it 'returns false' do
      expect(policy.public_send(action)).to be false
    end
  end

  it_behaves_like 'a disallowed action', :index?
  it_behaves_like 'a disallowed action', :new?
  it_behaves_like 'a disallowed action', :create?
  it_behaves_like 'a disallowed action', :show?
  it_behaves_like 'a disallowed action', :edit?
  it_behaves_like 'a disallowed action', :update?
  it_behaves_like 'a disallowed action', :destroy?
end
