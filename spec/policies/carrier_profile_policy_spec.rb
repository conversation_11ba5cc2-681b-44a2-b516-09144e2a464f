require 'rails_helper'

RSpec.describe CarrierProfilePolicy do
  subject(:policy) { Pundit.policy!(user, record) }

  let(:user) { build_stubbed :user, :carrier }
  let(:record) { build_stubbed :carrier_profile }

  shared_examples 'permitted' do |action|
    it "is permitted for #{action}" do
      expect(policy.public_send("#{action}?")).to be true
    end
  end

  shared_examples 'not permitted' do |action|
    it "is not permitted for #{action}" do
      expect(policy.public_send("#{action}?")).to be false
    end
  end

  context 'when user is not present' do
    let(:user) { nil }

    it_behaves_like 'not permitted', :new
    it_behaves_like 'not permitted', :create
    it_behaves_like 'not permitted', :show
    it_behaves_like 'not permitted', :edit
    it_behaves_like 'not permitted', :update
    it_behaves_like 'not permitted', :destroy
  end

  context 'when user is present' do
    context 'when record is not present' do
      let(:record) { nil }

      it_behaves_like 'not permitted', :new
      it_behaves_like 'not permitted', :create
      it_behaves_like 'not permitted', :show
      it_behaves_like 'not permitted', :edit
      it_behaves_like 'not permitted', :update
      it_behaves_like 'not permitted', :destroy
    end

    context 'when record is present' do
      context 'when user is admin' do
        let(:user) { create :user, admin: true }

        it_behaves_like 'permitted', :new
        it_behaves_like 'permitted', :create
        it_behaves_like 'permitted', :show
        it_behaves_like 'permitted', :edit
        it_behaves_like 'permitted', :update
        it_behaves_like 'permitted', :destroy
      end

      context 'when record is the same as the user' do
        before do
          allow(record).to receive(:users).and_return([user])
        end

        it_behaves_like 'permitted', :new
        it_behaves_like 'permitted', :create
        it_behaves_like 'permitted', :show
        it_behaves_like 'permitted', :edit
        it_behaves_like 'permitted', :update
        it_behaves_like 'permitted', :destroy
      end

      context 'when record is different than the user' do
        it_behaves_like 'not permitted', :new
        it_behaves_like 'not permitted', :create
        it_behaves_like 'not permitted', :show
        it_behaves_like 'not permitted', :edit
        it_behaves_like 'not permitted', :update
        it_behaves_like 'not permitted', :destroy
      end
    end
  end
end
