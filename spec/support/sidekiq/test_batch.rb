module Sidekiq
  module Test<PERSON>atch
    cattr_accessor :batches, default: Set.new
    attr_accessor :description, :linger

    class << self
      def clear
        self.batches = Set.new
      end

      def execute_callbacks(event)
        current = batches.to_a
        while (batch = current.pop)
          batch.execute_callback(event)
          batches.delete batch
        end
      end
    end

    def initialize(*args, &)
      super.tap { batches << self }
    end

    def execute_callback(event)
      Array.wrap(callbacks[event.to_s]).each do |args|
        args.each do |callback, options|
          klass, method = callback.to_s.split('#')
          method ||= "on_#{event}"
          klass.constantize.new.public_send(method, status, options)
        end
      end
    end

    def eql?(other)
      bid == other.bid
    end

    delegate :hash, to: :bid
  end
end

Sidekiq::Batch.prepend Sidekiq::TestBatch
