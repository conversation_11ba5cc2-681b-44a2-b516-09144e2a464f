require 'rails_helper'

RSpec.describe ApplicationHelper do
  describe '#dom_id' do
    let(:company) { build_stubbed :company, id: 853 }

    it 'generates unique id' do
      expect(helper.dom_id(company, 'bookmark')).to eq 'bookmark_company_853'
    end

    it 'matches turbo_frame_tag id' do
      expect(helper.turbo_frame_tag(company, 'bookmark')).to have_css "turbo-frame[id='bookmark_company_853']"
    end
  end
end
