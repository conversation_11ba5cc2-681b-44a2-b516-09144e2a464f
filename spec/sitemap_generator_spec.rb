require 'rails_helper'
require 'sitemap_generator'

RSpec.describe SitemapGenerator do
  let(:sitemap_files) { %w(sitemap.xml.gz sitemap1.xml.gz) }
  let(:company) { create :company, :carrier, :with_city, :with_authority }
  let!(:carrier_profile) { create :carrier_profile, company: }

  before do
    described_class.verbose = false

    sitemap_files.each do |file|
      stub_request(:put, "https://carriersource-test-file-upload.s3.amazonaws.com/sitemaps/#{file}")
        .and_return(status: 200, body: '')
    end

    ReviewsAggregate.create(company:, review_count: 1)

    CarrierCity.refresh
  end

  it 'runs successfully' do
    SitemapGenerator::Interpreter.run

    sitemap_files.each do |file|
      expect(WebMock).to have_requested :put, "https://carriersource-test-file-upload.s3.amazonaws.com/sitemaps/#{file}"
    end

    sitemap = Zlib::GzipReader.new(File.open('tmp/sitemaps/sitemap1.xml.gz')).read

    expect(Nokogiri::XML(sitemap).xpath('//xmlns:urlset/xmlns:url/xmlns:loc').map(&:text)).to(
      include(
        Routes.carrier_url(company),
        Routes.load_trucking_companies_url(truck_types(:flatbed)),
        Routes.page_url('terms-of-service'),
        Routes.city_trucking_companies_url(*company.city.path)
      )
    )
  end
end
