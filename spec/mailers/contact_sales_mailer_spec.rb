require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe ContactSalesMailer do
  describe '#contact' do
    let(:mail) { described_class.with(params).contact }
    let(:params) do
      {
        first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email,
        phone: Faker::PhoneNumber.phone_number, plan_type: 'Premium', comments: 'Please reach out to me'
      }
    end

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq 'A carrier is inquiring about an upgrade'
      expect(mail.to).to eq ['<EMAIL>']
      expect(mail.from).to eq ['<EMAIL>']
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match 'Please reach out to me'
    end
  end
end
