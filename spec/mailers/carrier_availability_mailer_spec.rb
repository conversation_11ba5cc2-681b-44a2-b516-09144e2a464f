require 'rails_helper'

RSpec.describe CarrierAvailabilityMailer do
  describe '#notify' do
    let(:mail) do
      described_class.with(email:, availabilities: [[carrier.id, availabilities.pluck(:id), 3]], company:).notify
    end

    let(:email) { Faker::Internet.email }
    let(:carrier) { create :company }
    let(:availabilities) { create_list(:carrier_availability, 3, company: carrier) }
    let(:company) { 'circle-logistics' }

    it 'renders the headers' do
      expect(mail.subject).to eq("This week's carrier capacity update")
      expect(mail.to).to eq([email])
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail['X-SES-LIST-MANAGEMENT-OPTIONS'].value).to eq('TransactionalContactList; topic=CarrierAvailability')
      expect(mail['X-SES-CONFIGURATION-SET'].value).to eq('CarrierAvailability')
      expect(mail['X-SES-MESSAGE-TAGS'].value).to eq('company=circle-logistics')
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('Below is the list of carriers')
    end
  end
end
