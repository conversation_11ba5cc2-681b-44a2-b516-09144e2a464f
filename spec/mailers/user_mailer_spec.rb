require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe UserMailer do
  describe '#verify' do
    let(:mail) { described_class.with(user:).verify }
    let(:user) { build_stubbed :user }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq 'Verify Your Email on CarrierSource'
      expect(mail.to).to eq [user.email]
      expect(mail.from).to eq ['<EMAIL>']
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match 'Thanks for signing up for CarrierSource'
    end
  end

  describe '#welcome' do
    shared_examples 'a welcome email' do |type|
      let(:mail) { described_class.with(user:, type: type.to_s).welcome }
      let(:user) { create :user, type }

      it_behaves_like 'a multipart email'

      it 'renders the headers' do
        expect(mail.subject).to eq 'Welcome to CarrierSource'
        expect(mail.to).to eq [user.email]
        expect(mail.from).to eq ['<EMAIL>']
      end
    end

    it_behaves_like 'a welcome email', :carrier
    it_behaves_like 'a welcome email', :broker
    it_behaves_like 'a welcome email', :shipper
    it_behaves_like 'a welcome email', :dispatcher
    it_behaves_like 'a welcome email', :seller
    it_behaves_like 'a welcome email', :driver
  end

  describe '#remind_unverified_user' do
    let(:mail) { described_class.with(user:).remind_unverified_user }
    let(:user) { build_stubbed :user }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq 'Verify Your Email on CarrierSource'
      expect(mail.to).to eq [user.email]
      expect(mail.from).to eq ['<EMAIL>']
    end

    it 'renders the body' do
      expect(mail.body.encoded.squish).to match 'It looks like your email is still unverified'
    end
  end
end
