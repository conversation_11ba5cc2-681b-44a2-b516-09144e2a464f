require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe BrokerageReviewMailer do
  describe '#approved' do
    let(:mail) { described_class.with(review:, user:).approved }
    let(:user) { build_stubbed :user }
    let(:review) { create :brokerage_review, :carrier }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq 'You have a new review on CarrierSource'
      expect(mail.to).to eq [user.email]
      expect(mail.from).to eq ['<EMAIL>']
      expect(mail['X-SES-LIST-MANAGEMENT-OPTIONS'].value).to eq('TransactionalContactList; topic=ReviewApproved')
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match 'just received a new review on CarrierSource'
    end
  end

  describe '#rejected' do
    let(:mail) { described_class.with(review:).rejected }
    let(:review) { create :brokerage_review, :carrier, :rejected }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq 'Your CarrierSource review was rejected'
      expect(mail.to).to eq [review.user.email]
      expect(mail.from).to eq ['<EMAIL>']
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match 'Our moderation team was unable to approve your review'
    end
  end
end
