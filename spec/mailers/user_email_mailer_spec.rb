require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe UserEmailMailer do
  describe '#verification_email' do
    let(:user_email) { build_stubbed :user_email }
    let(:mail) { described_class.with(user_email:).verification_email }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Verify your email')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([user_email.email])
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('Please confirm your ownership of this email address')
    end
  end
end
