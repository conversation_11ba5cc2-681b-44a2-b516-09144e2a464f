require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe PersonaVerificationMail<PERSON> do
  describe '#approved' do
    let(:persona_verification) { build_stubbed :persona_verification, :broker }
    let(:mail) { described_class.with(persona_verification:).approved }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq("You've been approved!")
      expect(mail.to).to eq([persona_verification.user.email])
      expect(mail.from).to eq(['<EMAIL>'])
    end
  end

  describe '#rejected' do
    let(:persona_verification) { build_stubbed :persona_verification, :broker }
    let(:mail) { described_class.with(persona_verification:).rejected }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Your CarrierSource account')
      expect(mail.to).to eq([persona_verification.user.email])
      expect(mail.from).to eq(['<EMAIL>'])
    end
  end
end
