require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe CarrierSubscriptionMailer do
  describe 'welcome_premium' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }
    let(:subscription) { create :subscription, resource: carrier_profile }
    let(:mail) { described_class.with(subscription:).welcome_premium }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Your CarrierSource Profile')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([carrier.contact_email])
      expect(mail.bcc).to eq(%w(<EMAIL>))
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('upgraded to CarrierSource premium')
      expect(mail.body.encoded).to match('Digital Advertising')
    end

    context 'when mailer is not blocked' do
      it 'sends mailer' do
        ClimateControl.modify BLOCKED_MAILERS: 'carrier_subscription_mailer#welcome_startup' do
          expect { mail.deliver_now }.to change(ActionMailer::Base.deliveries, :count).by(1)
        end
      end
    end

    context 'when mailer is blocked' do
      it 'does not send mailer' do
        ClimateControl.modify BLOCKED_MAILERS: 'carrier_subscription_mailer#welcome_premium' do
          expect { mail.deliver_now }.not_to change(ActionMailer::Base.deliveries, :count)
        end
      end
    end
  end

  describe 'welcome_startup' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }
    let(:subscription) { create :subscription, resource: carrier_profile }
    let(:mail) { described_class.with(subscription:).welcome_startup }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Your CarrierSource Profile')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([carrier.contact_email])
      expect(mail.bcc).to eq(%w(<EMAIL>))
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('upgraded to CarrierSource branding')
      expect(mail.body.encoded).to match('Reviews')
    end
  end

  describe 'canceled' do
    let(:carrier_profile) { create :carrier_profile }
    let(:carrier) { carrier_profile.carrier }

    let(:subscription) do
      create :subscription, resource: carrier_profile, ended_at: Time.zone.now
    end

    let(:mail) { described_class.with(subscription:).canceled }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Successful CarrierSource cancellation')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([carrier.contact_email])
      expect(mail.bcc).to eq(%w(<EMAIL>))
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('Your CarrierSource subscription has been successfully canceled')
    end
  end
end
