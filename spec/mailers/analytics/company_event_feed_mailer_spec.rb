require 'rails_helper'

RSpec.describe Analytics::CompanyEventFeedMailer do
  describe 'export_ready' do
    let(:feed_export) { create :analytics_company_event_feed_export, :with_file }
    let(:mail) { described_class.with(feed_export:).export_ready }

    it 'renders the headers' do
      expect(mail.subject).to eq('Your feed export is ready')
      expect(mail.to).to eq([feed_export.email])
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('The shipper leads export you requested')
    end
  end
end
