require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe CarrierProfileUserMailer do
  describe '#rejected' do
    let(:mail) { described_class.with(profile_user:).rejected }
    let(:profile_user) { create :carrier_profile_user }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Your CarrierSource profile claim was rejected')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([profile_user.user.email])
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match 'Thank you for requesting ownership'
    end
  end
end
