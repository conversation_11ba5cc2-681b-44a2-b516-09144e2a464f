require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe SubscriptionMailer do
  describe '#welcome' do
    let(:user) { build_stubbed :user }
    let(:subscription) { build_stubbed :subscription, resource: user }
    let(:mail) { described_class.with(subscription:).welcome }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Your CarrierSource Account')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([user.email])
      expect(mail.bcc).to eq(%w(<EMAIL>))
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match("You've successfully upgraded your CarrierSource profile")
    end
  end

  describe '#canceled' do
    let(:user) { build_stubbed :user }
    let(:subscription) { build_stubbed :subscription, resource: user, ended_at: Time.zone.now }
    let(:mail) { described_class.with(subscription:).canceled }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('Successful CarrierSource cancellation')
      expect(mail.from).to eq(['<EMAIL>'])
      expect(mail.to).to eq([user.email])
      expect(mail.bcc).to eq(%w(<EMAIL>))
    end

    it 'renders the body' do
      expect(mail.body.encoded).to match('Your CarrierSource subscription has been successfully canceled')
    end
  end
end
