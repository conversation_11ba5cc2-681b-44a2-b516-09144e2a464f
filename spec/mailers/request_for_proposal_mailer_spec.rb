require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe RequestForProposalMailer do
  describe 'submitted' do
    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }
    let(:request) { create :request_for_proposal, :carrier, company: }
    let(:mail) { described_class.with(request:).submitted }

    it_behaves_like 'a multipart email'

    it 'renders the headers' do
      expect(mail.subject).to eq('New message request on CarrierSource')
      expect(mail.to).to eq([request.entity.contact_email])
      expect(mail.from).to eq(['<EMAIL>'])
    end

    context 'with carrier entity' do
      context 'when profile has not been claimed' do
        it 'renders the body' do
          expect(mail.body.encoded).to match 'In order to send you your one free lead'
        end
      end

      context 'when profile has been claimed' do
        before do
          create :carrier_profile_user, :verified, carrier_profile:
        end

        context 'when previously notified' do
          before do
            create :request_for_proposal, :carrier, company:, status: 'notified'
          end

          it 'renders the body' do
            expect(mail.body.encoded).to match 'We got another RFQ on your CarrierSource profile'
          end
        end

        context 'when not previously notified' do
          it 'renders the body' do
            expect(mail.body.encoded).to match 'this is the only free lead you are allotted'
          end
        end
      end
    end

    context 'with broker entity' do
      let(:brokerage_profile) { create :brokerage_profile }
      let(:company) { brokerage_profile.company }
      let(:request) { create :request_for_proposal, :broker, company: }

      context 'when profile has not been claimed' do
        it 'renders the body' do
          expect(mail.body.encoded).to match 'In order to send you your one free lead'
        end
      end

      context 'when profile has been claimed' do
        before do
          create :brokerage_profile_user, :verified, brokerage_profile:
        end

        context 'when previously notified' do
          before do
            create :request_for_proposal, :broker, company:, status: 'notified'
          end

          it 'renders the body' do
            expect(mail.body.encoded).to match 'We got another RFQ on your CarrierSource profile'
          end
        end

        context 'when not previously notified' do
          it 'renders the body' do
            expect(mail.body.encoded).to match 'this is the only free lead you are allotted'
          end
        end
      end
    end
  end
end
