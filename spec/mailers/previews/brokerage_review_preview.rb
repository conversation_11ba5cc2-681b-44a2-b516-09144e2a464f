# Preview all emails at http://localhost:3000/rails/mailers/brokerage_review
class BrokerageReviewPreview < ActionMailer::Preview
  def approved
    review = BrokerageReview.approved.joins(brokerage: { profile: :users }).first
    BrokerageReviewMailer.with(review:, user: review.brokerage.profile.users.first).approved
  end

  def rejected
    BrokerageReviewMailer.with(review: BrokerageReview.rejected.first).rejected
  end
end
