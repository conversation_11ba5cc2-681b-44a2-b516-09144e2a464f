# Preview all emails at http://localhost:3000/rails/mailers/carrier_subscription
class CarrierSubscriptionPreview < ActionMailer::Preview
  def welcome_premium
    CarrierSubscriptionMailer
      .with(subscription: Subscription.where(resource_type: 'CarrierProfile').last)
      .welcome_premium
  end

  def welcome_startup
    CarrierSubscriptionMailer
      .with(subscription: Subscription.where(resource_type: 'CarrierProfile').last)
      .welcome_startup
  end

  def canceled
    CarrierSubscriptionMailer
      .with(subscription: Subscription.where(resource_type: 'CarrierProfile').last)
      .canceled
  end
end
