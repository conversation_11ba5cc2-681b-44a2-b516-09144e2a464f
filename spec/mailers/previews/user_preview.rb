# Preview all emails at http://localhost:3000/rails/mailers/user
class UserPreview < ActionMailer::Preview
  def verify
    UserMailer.with(user: User.first).verify
  end

  def welcome
    type = params.fetch(:type, 'broker')
    UserMailer.with(user: User.joins(type.to_sym).first, type:).welcome
  end

  def remind_unverified_user
    UserMailer.with(user: User.first).remind_unverified_user
  end
end
