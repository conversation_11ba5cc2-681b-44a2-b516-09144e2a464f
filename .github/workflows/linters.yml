name: Lin<PERSON>

on: [pull_request]

permissions: write-all

jobs:
  ruby:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1

      - name: Rubo<PERSON><PERSON> <PERSON>ter
        uses: reviewdog/action-rubocop@v2
        with:
          rubocop_version: gemfile
          rubocop_extensions: rubocop-capybara:gemfile rubocop-factory_bot:gemfile rubocop-rails:gemfile rubocop-rspec:gemfile
          rubocop_flags: '--parallel --force-exclusion'
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: <PERSON><PERSON><PERSON> Linter
        uses: reviewdog/action-brakeman@v2
        with:
          fail_on_error: true
          brakeman_flags: '--no-exit-on-warn --no-exit-on-error'
          github_token: ${{ secrets.GITHUB_TOKEN }}
  node:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'
          cache: yarn

      - run: yarn install --immutable

      - name: ESLint
        uses: reviewdog/action-eslint@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-pr-check

      - name: Prettier
        uses: wearerequired/lint-action@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          prettier: true

  commitlint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.tool-versions'
          cache: yarn

      - run: yarn install --immutable

      - run: yarn commitlint --from HEAD~${{ github.event.pull_request.commits }} --to HEAD
