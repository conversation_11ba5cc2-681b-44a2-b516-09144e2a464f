import { Controller } from '@hotwired/stimulus'
import { Turbo } from '@hotwired/turbo-rails'
import Combobox from '@github/combobox-nav'

export default class extends Controller {
  static targets = ['input', 'list']

  listTargetConnected(target) {
    this.start()
    target.addEventListener('combobox-commit', this.itemSelected)
  }

  listTargetDisconnected(target) {
    this.combobox?.stop()
    target.removeEventListener('combobox-commit', this.itemSelected)
  }

  start() {
    this.combobox?.destroy()
    this.combobox = new Combobox(this.inputTarget, this.listTarget, {
      scrollIntoViewOptions: false
    })
    this.combobox.start()
  }

  itemSelected(event) {
    const { url } = event.target.dataset

    if (url) {
      Turbo.visit(url)
    }
  }

  hideValidationMessage(event) {
    event.stopPropagation()
    event.preventDefault()
  }
}
