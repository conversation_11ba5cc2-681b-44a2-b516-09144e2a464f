import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['item']
  static classes = ['hidden']

  connect() {
    this.class = this.hasHiddenClass ? this.hiddenClass : 'hidden'
  }

  toggle() {
    this.itemTargets.forEach((s) => {
      const cssClass = s.dataset.revealClass || this.class
      s.classList.toggle(cssClass)
    })
  }

  show() {
    this.itemTargets.forEach((s) => {
      const cssClass = s.dataset.revealClass || this.class
      s.classList.remove(cssClass)
    })
  }

  hide() {
    this.itemTargets.forEach((s) => {
      const cssClass = s.dataset.revealClass || this.class
      s.classList.add(cssClass)
    })
  }
}
