import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = { target: String }

  initialize() {
    this.checkboxes = document.querySelectorAll(`input.${this.targetValue}`)
  }

  connect() {
    this.element.addEventListener('input', this.toggle)
    this.checkboxes.forEach((checkbox) =>
      checkbox.addEventListener('input', this.refresh)
    )
    this.refresh()
  }

  disconnect() {
    this.element.removeEventListener('input', this.toggle)
    this.checkboxes.forEach((checkbox) =>
      checkbox.removeEventListener('input', this.refresh)
    )
  }

  toggle = (e) => {
    e.preventDefault()

    this.checkboxes.forEach((checkbox) => {
      checkbox.checked = e.target.checked
      this.triggerChangeEvent(checkbox)
    })
  }

  refresh = () => {
    const checkboxesCount = this.checkboxes.length
    const checkboxesCheckedCount = this.checked.length

    this.element.checked = checkboxesCheckedCount > 0
    this.element.indeterminate =
      checkboxesCheckedCount > 0 && checkboxesCheckedCount < checkboxesCount
  }

  triggerChangeEvent(checkbox) {
    const event = new Event('change', { bubbles: true, cancelable: true })
    checkbox.dispatchEvent(event)
  }

  get checked() {
    return Array.from(this.checkboxes).filter((checkbox) => checkbox.checked)
  }

  get unchecked() {
    return Array.from(this.checkboxes).filter((checkbox) => !checkbox.checked)
  }
}
