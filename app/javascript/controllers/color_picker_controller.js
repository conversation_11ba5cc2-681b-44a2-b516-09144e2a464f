import ColorPicker from 'stimulus-color-picker'

export default class extends ColorPicker {
  connect() {
    if (this.hasButtonTarget) {
      super.connect()
    }
    this.element.addEventListener('click', this.onClick)
  }

  disconnect() {
    this.picker && this.picker.destroy()
    this.element.removeEventListener('click', this.onClick)
  }

  onClick = (event) => {
    event.preventDefault()
    this.picker.show()
  }

  onSave(color) {
    super.onSave(color)
    this.inputTarget.dispatchEvent(new Event('change', { bubbles: true }))
  }

  get componentOptions() {
    return {
      preview: true,
      hue: true,

      interaction: {
        input: true,
        clear: false,
        save: true
      }
    }
  }
}
