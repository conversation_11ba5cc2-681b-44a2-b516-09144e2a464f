import { Controller } from '@hotwired/stimulus'
import TomSele<PERSON> from 'tom-select'

export default class extends Controller {
  static values = { options: { type: Object, default: {} } }

  connect() {
    this.control = new TomSelect(this.element, {
      plugins: [
        'dropdown_input',
        this.element.multiple ? 'remove_button' : null,
        'clear_button'
      ].filter(Boolean),
      valueField: 'id',
      labelField: 'city_and_state',
      searchField: ['name', 'state_code'],
      selectOnTab: true,
      closeAfterSelect: true,
      shouldLoad: function (query) {
        return query.length > 1
      },
      load: function (query, callback) {
        return fetch(`/cities/search.json?max=50&query=${query}`)
          .then((response) => response.json())
          .then((json) => callback(json))
          .catch(() => callback())
      },
      ...this.optionsValue
    })
  }

  disconnect() {
    this.control && this.control.destroy()
  }
}
