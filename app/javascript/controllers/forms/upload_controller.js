import { Controller } from '@hotwired/stimulus'
import { DirectUpload } from '@rails/activestorage'

export default class extends Controller {
  static targets = ['input']
  static classes = ['dragging']

  connect() {
    this.element.addEventListener('drop', this.onDrop)
    this.element.addEventListener('dragover', this.onDragover)
    this.element.addEventListener('dragleave', this.onDragleave)
    this.inputTarget.addEventListener('change', this.onSelect)
  }

  disconnect() {
    this.element.removeEventListener('drop', this.onDrop)
    this.element.removeEventListener('dragover', this.onDragover)
    this.element.removeEventListener('dragleave', this.onDragleave)
    this.inputTarget.removeEventListener('change', this.onSelect)
  }

  onDrop = (event) => {
    event.preventDefault()
    const files = event.dataTransfer.files
    Array.from(files).forEach((file) => this.uploadFile(file))
    this.removeDraggingClass()
  }

  onDragover = (event) => {
    event.preventDefault()
    this.addDraggingClass()
  }

  onDragleave = (event) => {
    event.preventDefault()
    this.removeDraggingClass()
  }

  onSelect = (event) => {
    event.preventDefault()
    event.stopPropagation()
    Array.from(this.inputTarget.files).forEach((file) => this.uploadFile(file))
    this.inputTarget.value = null
  }

  uploadFile = (file) => {
    const url = this.inputTarget.dataset.directUploadUrl
    const upload = new DirectUpload(file, url)

    upload.create((error, { signed_id: signedId }) => {
      if (error) {
        // Handle the error
      } else {
        const formElement = this.inputTarget.closest('form')
        let hiddenField = formElement.querySelector('.file-input-signed-id')

        if (!hiddenField) {
          hiddenField = document.createElement('input')
          hiddenField.setAttribute('class', 'file-input-signed-id')
          hiddenField.setAttribute('type', 'hidden')
          hiddenField.name = this.inputTarget.name
          formElement.appendChild(hiddenField)
        }

        hiddenField.setAttribute('value', signedId)
        hiddenField.dispatchEvent(new Event('change', { bubbles: true }))
      }
    })
  }

  addDraggingClass() {
    this.draggingClasses.forEach((className) =>
      this.element.classList.add(className)
    )
  }

  removeDraggingClass() {
    this.draggingClasses.forEach((className) =>
      this.element.classList.remove(className)
    )
  }
}
