import { Controller } from '@hotwired/stimulus'
import serialize from 'util/forms/serialize'

export default class extends Controller {
  static targets = ['input']
  static values = { key: String }

  connect() {
    this.load()

    this.element.addEventListener('change', () => {
      this.save()
    })
  }

  load() {
    const inputValues = JSON.parse(localStorage.getItem(this.keyValue))

    if (!inputValues) {
      return
    }

    this.inputTargets.forEach((input) => {
      if (input.name in inputValues) {
        input.value = inputValues[input.name]
      }
    })
  }

  save() {
    const data = serialize(this.element)

    const inputValues = Object.fromEntries(
      Object.entries(data).filter(([key]) =>
        this.inputTargetNames.includes(key)
      )
    )

    localStorage.setItem(this.keyValue, JSON.stringify(inputValues))
  }

  get inputTargetNames() {
    return this.inputTargets.map((input) => input.name)
  }
}
