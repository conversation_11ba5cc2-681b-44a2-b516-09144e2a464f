import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['checkboxFieldset']
  static values = {
    errorContainer: { type: String, default: '.error-container' },
    invalidClass: { type: String, default: 'invalid' },
    displayErrorMessage: { type: Boolean, default: true }
  }

  connect() {
    this.element.setAttribute('novalidate', true)
    this.element.addEventListener('change', this.onChange, true)
    this.element.addEventListener('submit', this.onSubmit)
  }

  disconnect() {
    this.element.removeEventListener('change', this.onChange)
    this.element.removeEventListener('submit', this.onSubmit)
  }

  onChange = (event) => {
    const fieldset = event.target.closest('fieldset')

    if (fieldset) {
      this.validateFieldset(fieldset)
    } else {
      this.validateField(event.target)
    }
  }

  onSubmit = (event) => {
    if (!this.validateForm()) {
      event.preventDefault()
      this.firstInvalidField.focus()
    }
  }

  validateForm() {
    let isValid = true

    this.setCheckboxFieldsetsCustomValidity()

    this.formFields.forEach((field) => {
      if (!this.validateField(field)) {
        isValid = false
      }
    })

    return isValid
  }

  setCheckboxFieldsetsCustomValidity() {
    this.checkboxFieldsetTargets.forEach((fieldset) => {
      this.setCheckboxFieldsetCustomValidity(fieldset)
    })
  }

  setCheckboxFieldsetCustomValidity(fieldset) {
    const checkboxes = Array.from(fieldset.elements)

    if (checkboxes.some((e) => e.checked)) {
      checkboxes.forEach((e) => e.setCustomValidity(''))
    } else {
      checkboxes.forEach((e) => {
        e.setCustomValidity('Please select at least one of these options.')
      })
    }
  }

  validateFieldset(fieldset) {
    if (fieldset.dataset['forms-ValidationTarget'] === 'checkboxFieldset') {
      this.setCheckboxFieldsetCustomValidity(fieldset)
    }

    Array.from(fieldset.elements).forEach((e) => {
      this.validateField(e)
    })
  }

  validateField(field) {
    if (!this.shouldValidateField(field)) {
      return true
    }
    const isValid = field.checkValidity()
    field.classList.toggle(this.invalidClassValue, !isValid)
    if (this.displayErrorMessageValue) {
      this.refreshErrorForInvalidField(field, isValid)
    }
    return isValid
  }

  shouldValidateField(field) {
    return !(
      field.disabled ||
      ['reset', 'submit', 'button'].includes(field.type) ||
      // Ignore tom-select text input fields
      !!field.closest('.ts-wrapper')
    )
  }

  refreshErrorForInvalidField(field, isValid) {
    this.removeExistingErrorMessage(field)
    if (!isValid) {
      this.showErrorForInvalidField(field)
    }
  }

  removeExistingErrorMessage(field) {
    const fieldContainer = field.closest(this.errorContainerValue)
    if (!fieldContainer) {
      return
    }
    const existingErrorMessageElement =
      fieldContainer.querySelector('.validation-error')
    if (existingErrorMessageElement) {
      existingErrorMessageElement.parentNode.removeChild(
        existingErrorMessageElement
      )
    }
  }

  showErrorForInvalidField(field) {
    const fieldContainer = field.closest(this.errorContainerValue)
    if (!fieldContainer) {
      return
    }
    fieldContainer.insertAdjacentHTML(
      'beforeend',
      this.buildFieldErrorHtml(field)
    )
  }

  buildFieldErrorHtml(field) {
    return `<div class="validation-error">${field.validationMessage}</div>`
  }

  get formFields() {
    return Array.from(this.element.elements)
  }

  get firstInvalidField() {
    return this.formFields.find((field) => !field.checkValidity())
  }
}
