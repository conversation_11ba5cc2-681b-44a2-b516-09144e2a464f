import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect() {
    this.element.addEventListener('change', this.scroll)
  }

  disconnect() {
    this.element.removeEventListener('change', this.scroll)
  }

  scroll = (event) => {
    event.preventDefault()

    const anchor = document.querySelector(`a[href='${this.element.value}']`)

    anchor.click()
  }
}
