import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['modal']

  hide() {
    this.element.parentElement.removeAttribute('src')
    this.element.remove()
  }

  closeWithKeyboard(e) {
    if (e.code === 'Escape') {
      this.hide()
    }
  }

  closeBackground(e) {
    if (e && this.modalTarget.contains(e.target)) {
      return
    }
    this.hide()
  }
}
