import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect() {
    this.tooltipElement = this.element.querySelector('.city-tooltip')
    this.textElement = this.tooltipElement.querySelector('text')
    this.element.addEventListener('mouseover', this.show)
    this.element.addEventListener('mouseout', this.hide)
  }

  disconnect() {
    this.element.removeEventListener('mouseover', this.show)
    this.element.removeEventListener('mouseout', this.hide)
  }

  show = (e) => {
    const node = e.target

    if (node.tagName === 'circle' && node.classList.contains('tooltip')) {
      const [x, y] = [node.getAttribute('cx'), node.getAttribute('cy')]
      const label = node.getAttribute('aria-label')
      let anchor = 'middle'

      if (x < this.width / 4) {
        anchor = 'start'
      } else if (x > (this.width * 3) / 4) {
        anchor = 'end'
      }

      this.tooltipElement.setAttribute('transform', `translate(${x}, ${y})`)
      this.tooltipElement.setAttribute('opacity', '1')
      this.tooltipElement.setAttribute('visibility', 'visible')

      this.textElement.setAttribute('text-anchor', anchor)
      this.textElement.innerHTML = label
    }
  }

  hide = (e) => {
    const node = e.target

    if (node.tagName === 'circle' && node.classList.contains('tooltip')) {
      this.tooltipElement.setAttribute('opacity', '0')
      this.tooltipElement.setAttribute('visibility', 'hidden')
    }
  }

  get width() {
    const regex = /\d+ \d+ (?<width>\d+) \d+/
    const width = regex.exec(this.viewBox).groups.width
    return parseInt(width, 10)
  }

  get viewBox() {
    return this.element.getAttribute('viewBox')
  }
}
