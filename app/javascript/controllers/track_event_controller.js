import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = { name: String, properties: { type: Object, default: {} } }

  track = () => {
    window.ahoy.track(this.nameValue, {
      ...this.globalProperties,
      ...this.propertiesValue,
      url: window.location.href
    })
  }

  get globalProperties() {
    const meta = document.querySelector(
      'meta[name="analytics-global-properties"]'
    )
    return meta ? JSON.parse(meta.content) : {}
  }
}
