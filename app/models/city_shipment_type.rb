# == Schema Information
#
# Table name: city_shipment_types
#
#  id               :bigint           not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  city_id          :bigint           not null
#  shipment_type_id :bigint           not null
#
# Indexes
#
#  index_city_shipment_types_on_city_and_shipment_type  (city_id,shipment_type_id) UNIQUE
#  index_city_shipment_types_on_shipment_type           (shipment_type_id)
#
# Foreign Keys
#
#  fk_rails_3c38653bf4  (city_id => cities.id) ON DELETE => cascade
#  fk_rails_b359a39908  (shipment_type_id => shipment_types.id) ON DELETE => cascade
#
class CityShipmentType < ApplicationRecord
  belongs_to :city
  belongs_to :shipment_type

  def self.refresh
    transaction do
      truncate(restart_identity: true)
      connection.exec_insert_all <<~SQL.squish, 'CityShipmentType'
        insert into city_shipment_types (city_id, shipment_type_id, created_at, updated_at)
        select distinct cmp.city_id, cst.shipment_type_id, now(), now()
        from companies cmp
                 inner join censuses cen on cmp.dot_number = cen.dot_number
                 inner join carriers_shipment_types cst on cst.company_id = cmp.id
                 inner join company_entity_types cet
                            on cmp.id = cet.company_id and cet.entity_type = 'carrier'
        where cmp.hidden = false
          and cmp.passengers_only = false
          and cmp.city_id is not null
      SQL
    end
  end
end
