class SerializableCarrierAvailability < JSONAPI::Serializable::Resource
  type 'capacities'
  id { @object.uuid }

  attributes :date, :length, :weight, :origin_type, :destination_type

  attribute(:origin_city_ids) { @object.origin_cities.map(&:full_slug) }
  attribute(:destination_city_ids) { @object.destination_cities.map(&:full_slug) }

  attributes :origin_state_ids, :destination_state_ids, :origin_region_ids, :destination_region_ids

  belongs_to :truck_type
  belongs_to :shipment_type
end
