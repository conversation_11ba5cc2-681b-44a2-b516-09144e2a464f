class DeserializableCarrierAvailability < JSONAPI::Deserializable::Resource
  attribute :date

  attribute :truck_type_id do |value, key|
    { key => Records[:truck_types].locate(value).id }
  end

  attribute :shipment_type_id do |value, key|
    { key => Records[:shipment_types].locate(value).id }
  end

  attribute :length
  attribute :weight
  attribute :origin_type
  attribute :destination_type

  attribute :origin_city_ids do |ids, key|
    { key => Array.wrap(ids).map { |id| LaneSearches::City.new(id).record.id } }
  end

  attribute :destination_city_ids do |ids, key|
    { key => Array.wrap(ids).map { |id| LaneSearches::City.new(id).record.id } }
  end

  attribute :origin_state_ids
  attribute :destination_state_ids
  attribute :origin_region_ids
  attribute :destination_region_ids
end
