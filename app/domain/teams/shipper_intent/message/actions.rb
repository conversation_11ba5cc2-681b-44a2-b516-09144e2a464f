module Teams
  module ShipperIntent
    module Message
      class Actions < Base
        # rubocop:disable Metrics/MethodLength
        def call
          [
            {
              'type' => 'TextBlock',
              'text' => '---',
              'spacing' => 'none',
              'size' => 'small',
              'isSubtle' => true
            },
            {
              'type' => 'ColumnSet',
              'spacing' => 'extraLarge',
              'columns' => [
                {
                  'type' => 'Column',
                  'width' => 'auto',
                  'items' => [
                    {
                      'type' => 'ActionSet',
                      'actions' => [
                        {
                          'type' => 'Action.OpenUrl',
                          'title' => 'View in Dashboard',
                          'url' => url
                        }
                      ]
                    }
                  ]
                },
                {
                  'type' => 'Column',
                  'width' => 'stretch',
                  'verticalContentAlignment' => 'center',
                  'items' => [
                    {
                      'type' => 'Image',
                      'url' => 'https://d3kr2esmjhl9lz.cloudfront.net/assets/carrier_source_logo-6328383d.webp',
                      'height' => '20px',
                      'horizontalAlignment' => 'right'
                    }
                  ]
                }
              ]
            }
          ]
        end
        # rubocop:enable Metrics/MethodLength

        private

        def url
          Routes.url_for(
            [entity, :dashboard, :analytics, :feed, :shipper_intent, { feed_id: feed.id, id: analytics_company.id }]
          )
        end
      end
    end
  end
end
