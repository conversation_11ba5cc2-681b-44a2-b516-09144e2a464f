module Analytics
  module Events
    class EventQueryResponse
      module SearchSummaryLoads
        def loads
          @loads ||= %i(
            freights
            truck_types
            shipment_types
            specialized_services
          ).flat_map { |type| Records[type].locate_many(bucket[type].buckets.pluck(:key)) }.compact.uniq.presence ||
                     [NullLoad.instance]
        end
      end

      ProfileView = Data.define(:company_id, :entity_type, :doc_count)
      ProfileViewSummaryRow = Data.define(:entity, :doc_count)

      SearchSummaryRow = Struct.new(:bucket, :city, :destination, :doc_count) do
        include SearchSummaryLoads
      end

      StateSearchSummaryRow = Struct.new(:bucket, :state, :doc_count) do
        include SearchSummaryLoads
      end

      attr_reader :company, :response

      delegate :results, to: :response

      def initialize(company, response)
        @company = company
        @response = response
      end

      def visits
        @visits ||= results.map { |result| VisitResult.new(company, visit_records[result._source.visit_id], result) }
      end

      def visit_records
        @visit_records ||= ::Analytics::Visit.where(id: results.map { |r| r._source.visit_id }).index_by(&:id)
      end

      def profile_views_summary
        @profile_views_summary ||= profile_views.then do |pvs|
          companies = ::Company.where(id: pvs.map(&:company_id)).index_by(&:id)
          pvs.map { |pv| ProfileViewSummaryRow.new(companies[pv.company_id].as_entity(pv.entity_type), pv.doc_count) }
        end
      end

      def profile_views
        @profile_views ||= response.response.aggregations.profile_views.companies.buckets.flat_map do |cbucket|
          cbucket.entities.buckets.map do |ebucket|
            ProfileView.new(cbucket['key'].to_i, ebucket['key'], ebucket['doc_count'])
          end
        end
      end

      # rubocop:disable Metrics/AbcSize
      def search_summary
        @search_summary ||= response.response.aggregations.city_search.cities.buckets.flat_map do |bucket|
          bucket['destinations'].buckets.map do |dbucket|
            SearchSummaryRow.new(
              bucket, search_cities[bucket['key'].to_i], search_cities[dbucket['key'].to_i], dbucket['doc_count']
            )
          end
        end
      end
      # rubocop:enable Metrics/AbcSize

      def search_cities
        @search_cities ||= response.response.aggregations.city_search.cities.buckets.then do |buckets|
          ids = buckets.flat_map { |bucket| [bucket['key'], *bucket['destinations'].buckets.pluck(:key)] }.uniq
          ::City.where(id: ids).index_by(&:id)
        end
      end

      def state_search_summary
        @state_search_summary ||= response.response.aggregations.state_search.states.buckets.map do |bucket|
          StateSearchSummaryRow.new(bucket, search_states[bucket['key']], bucket['doc_count'])
        end
      end

      def search_states
        @search_states ||= response.response.aggregations.state_search.states.buckets.pluck(:key).then do |ids|
          ::Geo::State.where(id: ids).index_by(&:id)
        end
      end
    end
  end
end
