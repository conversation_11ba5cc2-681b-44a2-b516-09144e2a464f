module Analytics
  module Integrations
    module SettingsFromResponse
      extend Dry::Container::Mixin

      register :hubspot do |response|
        expires_at = Time.zone.now.advance(seconds: response.parse['expires_in'])
        response.parse.merge('expires_at' => expires_at)
      end

      register :salesforce do |response|
        expires_at = Time.zone.now.advance(hours: 2)
        response.parse.merge('expires_at' => expires_at)
      end

      register :slack do |response|
        response.parse.slice('access_token', 'token_type', 'scope', 'bot_user_id', 'app_id')
      end

      register :teams do |response|
        expires_at = Time.zone.now.advance(seconds: response.parse['expires_in'])
        response.parse.slice('access_token', 'refresh_token', 'token_type', 'scope').merge('expires_at' => expires_at)
      end

      def self.extract(provider:, response:)
        resolve(provider).call(response)
      end
    end
  end
end
