<%= turbo_frame_tag 'modal' do %>
  <%= render ModalComponent.new(title: 'Contact Information', class: 'min-w-96') do %>
    <div class="space-y-2">
      <% @carrier.profile.contacts.each do |contact| %>
        <div class="border border-gray-300 rounded-sm">
          <div class="px-3 py-2 font-semibold text-black bg-gray-100 border-b border-gray-300">
            <%= contact.type.titleize %>
          </div>

          <dl class="p-3">
            <% if contact.name.present? %>
              <div class="flex gap-2">
                <dt class="font-semibold text-black">Name</dt>
                <dd><%= contact.name %></dd>
              </div>
            <% end %>

            <% if contact.phone.present? %>
              <div class="flex gap-2">
                <dt class="font-semibold text-black">Phone</dt>
                <dd><%= phone_to contact.phone, class: 'hover:underline' %></dd>
              </div>
            <% end %>

            <% if contact.email.present? %>
              <div class="flex gap-2">
                <dt class="font-semibold text-black">Email</dt>
                <dd><%= mail_to contact.email, class: 'hover:underline' %></dd>
              </div>
            <% end %>
          </dl>
        </div>
      <% end %>
    </div>
  <% end %>
<% end %>
