<%= render layout: 'carrier_dashboard/website/layout', locals: { carrier: @carrier, website: @website } do %>
  <% if @profile.access_to_feature?(:website) %>
    <div class="md:border md:border-gray-300 md:rounded-sm p-6 space-y-4 mt-6">
      <h2 class="text-black font-semibold text-lg">Get the most out of your website</h2>
      <div class="flex flex-col-reverse xl:flex-row gap-2 overflow-hidden">
        <div class="space-y-4">
          <p>
            We are so excited that you've chosen to utilize the website we made you. You can edit the different pieces
            of the website using the buttons below. To ensure that your website looks even better, make sure you have
            fully optimized your profile page on CarrierSource with the following:
          </p>

          <ul class="list-disc ml-6">
            <li>
              Make sure that your
              <%= link_to 'Profile', carrier_dashboard_profile_url(@carrier),
                          class: 'text-primary font-semibold hover:underline' %>,
              <%= link_to 'Lane Information', carrier_dashboard_lanes_operation_states_url(@carrier),
                          class: 'text-primary font-semibold hover:underline' %>, and
              <%= link_to 'Terminals', carrier_dashboard_lanes_terminals_url(@carrier),
                          class: 'text-primary font-semibold hover:underline' %>
              sections are filled out and accurate
            </li>
            <li>
              Upload images of your truck, fleet, or team in the
              <%= link_to 'Media & Downloads', carrier_dashboard_assets_url(@carrier),
                          class: 'text-primary font-semibold hover:underline' %>
              section
            </li>
            <li>
              Go to the
              <%= link_to 'Reviews', carrier_dashboard_reviews_url(@carrier),
                          class: 'text-primary font-semibold hover:underline' %>
              section and select which reviews you want to showcase on your website
            </li>
          </ul>

          <p>
            If you need any assistance, please contact CarrierSource at
            <%= mail_to '<EMAIL>', class: 'text-primary font-semibold hover:underline' %>
            or call
            <%= phone_to '************', class: 'text-primary font-semibold hover:underline' %>.
          </p>
          <div class="flex flex-col md:flex-row gap-1.5">
            <% CarrierDashboard::Navigation.page('website', @carrier, current_user, params)
                 .children.each do |page| %>
              <%= link_to page.url, class: 'btn default gray' do %>
                <%= svg_tag page.icon, class: 'h-4 w-4 mr-2 fill-white' %>
                <span><%= page.label %></span>
              <% end %>
            <% end %>
          </div>
        </div>
        <div class="flex flex-none justify-center items-center my-4 xl:my-0">
          <%= image_tag 'marketing/laptop_website.webp', alt: 'Website shown on Laptop and Phone',
                        class: 'max-w-full max-h-[180px] lg:max-h-[207px]' %>
        </div>
      </div>
    </div>
  <% else %>
    <div class="md:border md:border-gray-300 md:rounded-sm p-6 space-y-4 mt-6">
      <h2 class="text-black font-semibold text-lg">Your website is a couple clicks away</h2>
      <div class="flex flex-col-reverse xl:flex-row gap-2">
        <div class="space-y-4">
          <p>
            We built <%= @carrier.name %> a website that can be easily managed right here on CarrierSource. All the
            information displayed on your website is fully customizable and you can showcase your best reviews
            gathered through CarrierSource, all on a domain of your choice. Having a professional website that shows
            up when shippers are looking for their next carrier helps instill confidence and ultimately drives you
            more business.
          </p>
          <div class="flex gap-1.5">
            <%= link_to "https://#{@carrier.dot_number}.carriersource.website",
                        rel: 'noopener', target: '_blank', class: 'btn hollow gray' do %>
              <%= svg_tag 'eye-regular', class: 'h-4 w-4 mr-2 fill-gray-700' %>
              <span>Preview</span>
            <% end %>

            <%= link_to 'Upgrade Now', 'javascript:void(0)',
                        data: upgrade_message_data, class: 'btn default secondary' %>
          </div>
        </div>
        <div class="flex justify-center items-center flex-none my-4 xl:my-0">
          <%= image_tag 'marketing/laptop_website.webp', alt: 'Website shown on Laptop and Phone',
                        class: 'max-w-full max-h-[180px] lg:max-h-[207px]' %>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
