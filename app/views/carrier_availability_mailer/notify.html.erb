<% content_for :preheader do %>
  Find out where carriers are going to be this week
<% end %>

<% utm = { utm_source: 'aws', utm_medium: 'email', utm_campaign: 'capacity' } %>

<div class="bg-slate-50 sm:px-4 font-sans text-base">
  <table align="center">
    <tr>
      <td class="w-[800px] max-w-full">
        <table class="w-full">
          <tr>
            <td class="py-4 sm:py-8 sm:px-6 text-center">
              <a ses:no-track href="<%= root_url %>">
                <%= image_tag attachments['logo.png'].url, width: 150, alt: 'CarrierSource Logo' %>
              </a>
            </td>
          </tr>
          <tr>
            <td class="bg-gray-600 py-6 text-left rounded">
              <div role="separator" class="leading-16 sm:leading-8"></div>

              <table class="w-full">
                <tr>
                  <td class="w-6"></td>
                  <td>
                    <h1 class="m-0 mb-4 text-xl sm:text-lg text-white">
                      Carrier Capacity List
                    </h1>

                    <p class="m-0 text-white leading-6">
                      Below is the list of carriers who have told us their location and availability for the coming
                      week. Don't forget that lane search, a tool that allows you to source carriers for the exact
                      lane you are trying to fill, is now free for you to use as well! Try it
                      <%= link_to 'here', lane_searches_url(utm), class: 'no-underline font-semibold text-white',
                                  target: '_blank' %>.
                    </p>
                  </td>
                  <td class="w-6"></td>
                </tr>
              </table>
            </td>
          </tr>

          <tr role="separator">
            <td class="leading-8">&zwj;</td>
          </tr>

          <tr>
            <td class="text-left">
              <table class="text-gray-600 w-full text-sm border-collapse">
                <thead>
                <tr class="text-black">
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5">Name</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5">Date</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5">Origin</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5">Destination</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5 text-center">Load</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5 text-center">Equip</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5 text-center">Length</th>
                  <th class="border-0 border-b border-solid border-b-gray-400 whitespace-nowrap py-0.5 text-center">Weight</th>
                </tr>

                </thead>
                <tbody class="bg-white">
                <% @availabilities.each do |row| %>
                  <% row.availabilities.each do |availability| %>
                    <% location = CarrierAvailabilities::Location.new(availability) %>
                    <tr>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 border-l-gray-400 py-2 px-1">
                        <%= link_to row.carrier.name, carrier_url(row.carrier, utm),
                                    class: 'text-primary', 'ses:tags' => "carrier:#{row.carrier.to_param}" %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1 whitespace-nowrap">
                        <%= l(availability.date, format: '%m/%d/%y') %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1">
                        <% location.origins.each do |origin| %>
                          <p class="my-px"><%= origin.name %></p>
                        <% end %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1">
                        <% location.destinations.each do |destination| %>
                          <p class="my-px"><%= destination.name %></p>
                        <% end %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1 text-center">
                        <% if availability.shipment_type %>
                          <%= t("shipment_types.#{availability.shipment_type.key}.shorthand") %>
                        <% else %>
                          Any
                        <% end %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1 text-center">
                        <%= availability.truck_type.name %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 py-2 px-1 text-center">
                        <% if availability.length.present? %>
                          <%= availability.length %>'
                        <% end %>
                      </td>
                      <td class="border border-solid border-x-gray-200 border-y-gray-400 border-r-gray-400 py-2 px-1 text-center">
                        <% if availability.weight.present? %>
                          <%= availability.weight %>k
                        <% end %>
                      </td>
                    </tr>
                  <% end %>
                  <% if row.total > row.availabilities.size %>
                    <tr>
                      <td colspan="8" class="border border-solid border-gray-400 py-2 px-1 text-center">
                        <% remaining = row.total - row.availabilities.size %>
                        <%= link_to "View #{remaining} more listings for #{row.carrier.name}",
                                    carrier_url(row.carrier, scroll_to: 'availability', **utm),
                                    class: 'text-primary',
                                    'ses:tags' => "carrier:#{row.carrier.to_param}" %>
                      </td>
                    </tr>
                  <% end %>
                <% end %>
                </tbody>
              </table>
            </td>
          </tr>

          <tr role="separator">
            <td class="leading-8">&zwj;</td>
          </tr>
          <tr>
            <td class="px-12 sm:px-6 py-4 bg-white rounded text-left shadow-sm">
              <p class="m-0 text-xs text-slate-500 text-center">
                <a ses:no-track href="{{amazonSESUnsubscribeUrl}}" class="text-primary no-underline">Unsubscribe</a>
                from receiving these emails.
              </p>
            </td>
          </tr>
          <tr role="separator">
            <td class="leading-12 sm:leading-8">&zwj;</td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</div>
