class ContentTypeValidator < ActiveModel::EachValidator
  def check_validity!
    return if options.include?(:in) ^ options.include?(:start_with)
    raise ArgumentError, 'Either :in or :start_with must be supplied (but not both)'
  end

  def validate_each(record, attribute, value)
    return unless value.attached?

    content_type = value.content_type

    valid = if options.key?(:in)
              content_type.in?(options[:in])
            else
              content_type.start_with?(*options[:start_with])
            end

    record.errors.add(attribute, :content_type) unless valid
  end
end
