class PasswordValidator < ActiveModel::EachValidator
  REGEX = /\A
    (?=.{8,})          # at least 8 characters
    (?=.*\d)           # at least 1 digit
    (?=.*[a-z])        # at least 1 lower case letter
    (?=.*[A-Z])        # at least 1 upper case letter
    (?=.*[[:^alnum:]]) # at least 1 special character
    (?!.*\s)           # no spaces
  /x

  def validate_each(record, attribute, value)
    return if value.blank?
    return if value.match?(REGEX)
    record.errors.add(attribute, :invalid_password)
  end
end
