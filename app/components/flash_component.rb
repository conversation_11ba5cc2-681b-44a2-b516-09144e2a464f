class FlashComponent < ViewComponent::Base
  attr_reader :flash, :options

  BORDER = {
    alert: 'border-secondary bg-secondary-50',
    notice: 'border-green bg-green-50/[.5]'
  }.with_indifferent_access.freeze

  SVG_FILE = {
    alert: 'circle-exclamation-regular',
    notice: 'circle-check-regular'
  }.with_indifferent_access.freeze

  SVG_FILL = {
    alert: 'fill-secondary',
    notice: 'fill-green-700'
  }.with_indifferent_access.freeze

  TEXT_COLOR = {
    alert: 'text-secondary',
    notice: 'text-green-700'
  }.with_indifferent_access.freeze

  def self.from_errors(errors, **)
    errors
      .map { |error| [:alert, error.full_message] }
      .then { |flash| new(flash, **) }
  end

  def initialize(flash, **options)
    @flash = flash
    @options = options
  end

  def type_valid?(type)
    %w(alert notice).include?(type.to_s)
  end

  def border(type)
    BORDER.fetch(type)
  end

  def svg_file(type)
    SVG_FILE.fetch(type)
  end

  def svg_fill(type)
    SVG_FILL.fetch(type)
  end

  def text_color(type)
    TEXT_COLOR.fetch(type)
  end
end
