<div class="inline-flex flex-wrap text-sm gap-2" <%= tag.attributes(microdata) %>>
  <% if count.positive? %>
    <%= render StarsComponent.new(rating:, class: 'fill-secondary') %>
  <% end %>
  <div class="flex gap-2">
    <span>
      <% if link %>
        <%= link_to link, class: 'text-primary hover:underline' do %>
          (<span itemprop="reviewCount" class="font-semibold"><%= count %></span> <%= 'review'.pluralize(count) %>)
        <% end %>
      <% end %>
    </span>
    <% if count.positive? %>
      <span>
        <strong itemprop="ratingValue" class="ml-1"><%= rating.round(1) %></strong>
        out of <span itemprop="bestRating">5</span>
      </span>
    <% end %>
  </div>
  <meta itemprop="worstRating" content="0">
</div>
