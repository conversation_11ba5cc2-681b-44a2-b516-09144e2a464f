<% if attachment.content_type == 'image/svg+xml' %>
  <% content = attachment.download.force_encoding('utf-8') %>
  <%= raw ActiveStorage::OptimizeSvg.call(content) %>
<% elsif attachment.representable? %>
    <picture class="<%= options.fetch(:picture_class, 'w-full h-full') %>">
      <source srcset="<%= rails_storage_proxy_path(attachment.representation(convert: :webp, resize_to_limit: size(resolution: 2))) %> 2x">
      <source srcset="<%= rails_storage_proxy_path(attachment.representation(convert: :webp, resize_to_limit: size)) %>">
      <%= image_tag attachment.representation(resize_to_limit: size), loading:, **options.except(:picture_class) %>
    </picture>
<% end %>
