<div id="<%= dom_id(carrier_profile, 'carrier_dashboard_progress') %>"
     class="border border-gray-300 py-4 rounded-sm bg-white">
  <div class="text-sm text-black text-center">Your Profile Completion</div>
  <div class="my-4 relative">
    <div class="flex absolute left-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 pl-2
                      font-semibold text-xl text-black">
      <div><%= completion.percentage %></div><div class="text-2xs inline-flex mt-1">%</div>
    </div>
    <svg id="svg" width="100" height="100" viewbox="0 0 100 100" class="mx-auto -rotate-90">
      <circle r="40" cx="50" cy="50" fill="transparent" stroke="#D1D5DB" stroke-width="10px" />
      <circle id="bar" r="40" cx="50" cy="50" fill="transparent" stroke-dasharray="100"
              stroke-dashoffset="<%= 100 - completion.percentage %>" stroke="#0093E9" stroke-width="10px"
              stroke-linecap="round" pathLength="100" />
    </svg>
  </div>

  <%= render HorizontalRuleComponent.new %>

  <% factors.each do |factor| %>
    <a href="<%= factor.fetch(:url, 'javascript:void(0)') %>" class="block px-4 mb-2">
      <p class="text-xs"><%= t("carrier_profiles.completion.#{factor[:name]}") %></p>
      <div class="flex flex-row items-center -mt-1">
        <div class="flex-auto bg-gray-300 h-1 mr-1">
          <div class="h-1 bg-primary" style="width: <%= factor[:progress] * 100 %>%"></div>
        </div>
        <div class="w-4 h-4 overflow-hidden rounded-full flex-none
                    <%= factor[:progress] == 1 ? 'bg-primary' : 'bg-gray-300' %>">
          <% if factor[:progress] == 1 %>
            <%= helpers.svg_tag 'check-solid', class: 'fill-white ml-0.5 w-4 h-4' %>
          <% end %>
        </div>
      </div>
    </a>
  <% end %>
</div>
