module Carriers
  class SaferLinkComponent < ViewComponent::Base
    attr_reader :carrier, :user

    delegate :dot_number, to: :carrier

    def initialize(carrier:, user:)
      @carrier = carrier
      @user = user
    end

    def safer_url
      Addressable::URI.new(
        scheme: 'https', host: 'safer.fmcsa.dot.gov', path: 'query.asp',
        query: {
          searchtype: 'ANY', query_type: 'queryCarrierSnapshot', query_param: 'USDOT', query_string: dot_number
        }.to_query
      ).to_s
    end

    def url
      @url ||= Carriers::ProtectedFeature.new(user:).url(feature: 'safer_link')
    end
  end
end
