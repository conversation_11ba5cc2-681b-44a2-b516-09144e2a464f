<%= render CarrierComponent.new(carrier:) do |c| %>
  <% c.with_header do %>
    <div class="h-[1px] w-full md:hidden bg-gray-300"></div>

    <div class="flex items-center text-sm px-6 py-2 md:border md:border-b-0 md:rounded-t-sm border-gray-300 bg-gray-200">
      <% tooltip = capture do %>
        <dl class="p-2 space-y-2">
          <% if preferred_lanes? %>
            <div>
              <dt><strong>Preferred Lane</strong></dt>
              <dd><%= t('lane_search.matched_queries.preferred_lanes') %></dd>
            </div>
          <% end %>

          <% if review_lanes? %>
            <div>
              <dt><strong>Review Lane</strong></dt>
              <dd><%= t('lane_search.matched_queries.review_lanes') %></dd>
            </div>
          <% end %>

          <% if operation_states? %>
            <div>
              <dt><strong>Operation State</strong></dt>
              <dd><%= t('lane_search.matched_queries.operation_states') %></dd>
            </div>
          <% end %>
        </dl>
      <% end %>

      <% matched_queries = [] %>

      <% if preferred_lanes? %>
        <% matched_queries << capture do %>
          <span>Preferred Lane</span>
        <% end %>
      <% end %>

      <% if review_lanes? %>
        <% matched_queries << capture do %>
          <span>Review Lane</span>
        <% end %>
      <% end %>

      <% if operation_states? %>
        <% matched_queries << capture do %>
          <span>Operates in <strong><%= destination.state.name %></strong></span>
        <% end %>
      <% end %>

      <p>
        <%= safe_join(matched_queries, raw('&mdash;')) %>
      </p>

      <%= helpers.svg_tag 'circle-info-regular', class: 'inline w-4 h-4 fill-gray-500 ml-1',
                          data: { controller: 'tippy',
                                  tippy_options_value: { content: tooltip, allowHTML: true, theme: 'light' }.to_json } %>
    </div>
  <% end %>

  <% c.with_aside do %>
    <%= aside %>
  <% end %>

  <% c.with_footer do %>
    <%= footer %>
  <% end %>
<% end %>
