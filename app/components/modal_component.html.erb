<%= tag.div data: { controller: 'modal',
                    action: 'keyup@window->modal#closeWithKeyboard click@window->modal#closeBackground' },
            class: 'fixed inset-0 bg-black/75 z-50' do %>
  <div role="dialog" data-modal-target="modal"
       class="absolute bg-white top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-md break-words <%= options[:class] %>">
    <div class="flex flex-row justify-center border-b border-b-gray-300 pl-6 pr-2 py-3">
      <h2 class="inline-flex font-semibold text-black items-center mr-8 flex-auto"><%= title %></h2>
      <button type="button" data-action="modal#hide" class="p-2 opacity-60 hover:opacity-100">
        <span class="sr-only">Hide modal</span>
        <%= helpers.svg_tag('xmark-large-solid', class: 'w-[16px] h-[16px]') %>
      </button>
    </div>
    <div class="<%= body_class %>">
      <%= content %>
    </div>
    <% if footer? %>
      <div class="<%= footer_class %>">
        <%= footer %>
      </div>
    <% end %>
  </div>
<% end %>
