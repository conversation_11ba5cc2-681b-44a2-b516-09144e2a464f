<% if helpers.signed_in? %>
  <%= link_to url_for([:new, entity, :note]), id: dom_id(entity, 'add_note'),
              data: { turbo_frame: 'modal' }, class: "btn ghost gray x-link-add-notes #{options.fetch(:class, '')}",
              rel: 'noindex nofollow' do %>
    <%= helpers.svg_tag 'memo-pad-regular', class: 'h-4 w-4 mr-2 fill-gray-700' %>
    <span><%= button_text %></span>
  <% end %>
<% else %>
  <%= link_to protected_feature_url(feature: "#{entity.entity_type}_note", tier: 'login'), rel: 'noindex nofollow',
              data: { turbo_frame: 'modal' }, class: "btn ghost gray #{options.fetch(:class, '')}" do %>
    <%= helpers.svg_tag 'memo-pad-regular', class: 'h-4 w-4 mr-2 fill-gray-700' %>
    <span><%= button_text %></span>
  <% end %>
<% end %>
