module Brokerages
  class RequestForProposalComponent < ViewComponent::Base
    attr_reader :brokerage, :user

    delegate :active?, :authorized?, to: :brokerage
    delegate :email, to: :contact
    delegate :profile, to: :brokerage, allow_nil: true
    delegate :claimed?, to: :profile, allow_nil: true

    def initialize(brokerage:, user:)
      @brokerage = brokerage
      @user = user || Nullable.object(:user)
    end

    def render?
      active? && claimed? && authorized? && email.present? && (user.blank? || user.persona?(scope: :shipper))
    end

    def contact
      @contact ||= Companies::ContactInformation.new(brokerage)
    end
  end
end
