class ReviewComponent < ViewComponent::Base
  renders_one :header
  renders_one :footer

  attr_reader :review, :featured

  delegate :carrier, :user, :review_lanes, :offer_electronic_tracking, :electronic_tracking_worked, :reviewer_state,
           to: :review
  delegate :profile, to: :carrier

  def initialize(review:, featured: false)
    @review = review
    @featured = featured && review.featured && profile.access_to_feature?(:featured_review)
  end

  def freights
    Records[:freights].slice(*review.freights).values.map(&:name)
  end

  def monogram
    Reviews::Monogram.new(review).value
  end

  def tagline
    Reviews::Tagline.new(review).value
  end

  def title
    Reviews::Title.new(review).value
  end

  def tracking_functions
    @tracking_functions ||= [
      if offer_electronic_tracking.nil?
        nil
      else
        offer_electronic_tracking ? 'Offers electronic tracking' : 'Did not use electronic tracking'
      end,
      if electronic_tracking_worked.nil?
        nil
      else
        electronic_tracking_worked ? 'Tracking functional' : 'Tracking not functional'
      end
    ].compact
  end

  def positive_sentiments
    @positive_sentiments ||= review.sentiments.select(&:positive)
  end

  def negative_sentiments
    @negative_sentiments ||= review.sentiments.reject(&:positive)
  end

  def border_color
    featured ? 'border-primary' : 'border-gray-200'
  end
end
