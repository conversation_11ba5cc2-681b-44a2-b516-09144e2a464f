class ProfileSponsoredContentComponent < ViewComponent::Base
  attr_reader :resource

  delegate :company, :entity, to: :resource, allow_nil: true
  delegate :profile, :review, :reviews_aggregate, :profile_url, to: :provider
  delegate :logo, to: :profile, allow_nil: true
  delegate :review_count, :star_rating, to: :reviews_aggregate

  def initialize(resource)
    @resource = resource
  end

  def render?
    company.present?
  end

  def provider
    @provider ||= SponsoredContents::EntityProviders.for(entity).new(company)
  end

  def data_attributes
    {
      controller: 'intersection track-event',
      track_event_name_value: 'Sponsored Content Viewed',
      track_event_properties_value: { id: company.id, entity_type: entity },
      action: 'intersection:appear->track-event#track:once'
    }
  end
end
