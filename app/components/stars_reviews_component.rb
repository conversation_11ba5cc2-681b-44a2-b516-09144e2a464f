class StarsReviewsComponent < ViewComponent::Base
  attr_reader :rating, :count, :link, :snippet

  def initialize(rating: 0, count: 1, link: false, snippet: false)
    @rating = rating
    @count = count.to_i
    @link = link
    @snippet = snippet
  end

  def microdata
    if snippet && count.positive?
      { itemprop: 'aggregateRating', itemscope: true, itemtype: 'https://schema.org/AggregateRating' }
    else
      {}
    end
  end
end
