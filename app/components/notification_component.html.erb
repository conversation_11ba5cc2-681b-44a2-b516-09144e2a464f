<div data-controller="notification"
     data-notification-delay-value="<%= delay %>"
     data-transition-enter-from="ease-out translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
     data-transition-enter-to="ease-out translate-y-0 opacity-100 sm:translate-x-0"
     data-transition-leave-from="ease-in opacity-100"
     data-transition-leave-to="ease-in opacity-0"
     class="transform transition duration-300 hidden max-w-sm w-full bg-white shadow-xl rounded-sm pointer-events-auto
            overflow-hidden border border-gray-300 <%= border %> border-t-2">
  <div class="p-4">
    <div class="flex items-start">
      <div class="shrink-0">
        <%= helpers.svg_tag svg_file, class: "w-5 h-5 mt-0.5 #{svg_fill}" %>
      </div>
      <div class="ml-3 w-0 flex-1 pt-0.5">
        <p class="text-sm font-semibold text-black"><%= title %></p>
        <% if description? %>
          <p class="mt-1 text-sm"><%= description %></p>
        <% end %>
      </div>
      <div class="ml-2 -mr-2 -mt-1 shrink-0 flex">
        <button type="button" data-action="notification#hide"
                class="group p-1.5 rounded-full inline-flex text-gray-400 hover:bg-gray-100 focus:outline-hidden
                       focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <span class="sr-only">Close</span>
          <%= helpers.svg_tag 'xmark-solid', class: 'w-4 h-4 fill-gray-600 group-hover:fill-black' %>
        </button>
      </div>
    </div>
  </div>
</div>
