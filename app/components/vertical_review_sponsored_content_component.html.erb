<%= link_to profile_url,
            data: { controller: 'track-event', track_event_name_value: 'Sponsored Content Clicked',
                    track_event_properties_value: { id: company.id, entity_type: resource.entity },
                    action: 'track-event#track' } do %>
  <div class="relative flex flex-col p-6 pb-3 md:rounded-sm items-center"
       style="background: rgb(0, 147, 233) linear-gradient(-20deg, rgb(0, 147, 233) 35%, rgb(0, 160, 241) 35%) no-repeat center -300px;">

    <% if logo.present? %>
      <div class="flex items-center justify-center bg-white p-0.5 w-[96px] h-[96px] rounded-xs">
        <%= render PictureComponent.new(logo, dimensions: [92, 92], class: 'w-full h-full object-contain',
                                              alt: "#{company.name} logo") %>
      </div>
    <% end %>

    <h3 class="text-lg text-white font-semibold mt-3"><%= company.name %></h3>
    <div class="mt-3">
      <%= render StarsComponent.new(rating: review.star_rating, class: 'fill-white') %>
    </div>

    <%= render HorizontalRuleComponent.new %>

    <%= simple_format(review.body, class: 'text-white text-xs') %>

    <div class="self-start text-xs text-white font-light mt-2">
      <% persona = review.persona %>
      <% if persona.state.present? %>
        &ndash; A <%= persona.type %> in <strong class="text-white"><%= persona.state.name %></strong>
      <% end %>
    </div>

    <% if review.sentiments.present? %>
      <%= render HorizontalRuleComponent.new %>

      <div class="text-xs font-semibold text-white mb-4 self-start">
        <%= helpers.svg_tag 'thumbs-up-regular', class: 'w-4 h-4 fill-white inline -mt-1.5' %> What went well?
      </div>

      <ul class="flex flex-wrap gap-2">
        <% review.sentiments.first(4).each do |sentiment| %>
          <li class="px-2 py-0.5 text-2xs border border-primary rounded-full bg-white text-primary ring-1 ring-offset-1">
            <%= sentiment.label %>
          </li>
        <% end %>
      </ul>
    <% end %>
    <div class="self-end inline-flex items-center text-white text-2xs gap-1 mt-4" data-controller="tippy"
         data-tippy-content="Customers have paid for these ads">
      <span>Sponsored</span>
      <span><%= helpers.svg_tag 'circle-info-regular', class: 'h-3 w-3 fill-white' %></span>
    </div>
  </div>
<% end %>
