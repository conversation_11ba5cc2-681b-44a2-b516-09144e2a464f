<div class="flex w-full mx-auto my-6 border-0 lg:border border-gray-300 rounded-sm lg:divide-x lg:divide-gray-300">
  <% progress.steps.each_with_index do |step, index| %>
    <% completed = progress.current_index > index %>
    <div class="flex flex-col flex-1 grow lg:flex-row items-center relative lg:py-3 lg:px-6 lg:gap-6">
      <% if completed %>
        <div class="flex flex-none h-8 w-8 rounded-full bg-primary items-center justify-center">
          <%= helpers.svg_tag 'check-solid', class: 'fill-white h-4 w-4' %>
        </div>
      <% elsif step.current? %>
        <div class="flex flex-none h-8 w-8 rounded-full border-2 border-primary bg-white items-center justify-center">
          <div class="text-primary-600 hidden lg:block"><%= index + 1 %></div>
          <div class="h-3 w-3 bg-primary rounded-full lg:hidden"></div>
        </div>
      <% else %>
        <div class="flex flex-none h-8 w-8 rounded-full border-2 border-gray-300 bg-white items-center justify-center">
          <div><%= index + 1 %></div>
        </div>
      <% end %>
      <div>
        <div class="text-center text-2xs lg:text-left lg:text-xs lg:font-semibold mt-1">
          <%= t("wizards.#{wizard.name}.#{step.name}.label") %>
        </div>
        <div class="hidden lg:block text-2xs"><%= t("wizards.#{wizard.name}.#{step.name}.description") %></div>

        <% if index + 1 < progress.steps.size %>
          <div class="hidden lg:block absolute top-1/2 -translate-y-1/2 -right-[10px] w-0 h-0 border-t-[10px] border-t-transparent
                  border-l-[10px] border-l-gray-300 border-b-[10px] border-b-transparent z-10"></div>
          <div class="hidden lg:block absolute top-1/2 -translate-y-1/2 -right-[8px] w-0 h-0 border-t-[10px] border-t-transparent
                  border-l-[10px] border-l-white border-b-[10px] border-b-transparent z-10"></div>
          <div class="h-0.5 w-full bg-gray-200 absolute top-4 left-1/2 -z-10 lg:hidden"></div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
