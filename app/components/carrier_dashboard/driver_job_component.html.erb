<div class="relative border border-primary-300 w-full p-4 bg-white rounded-sm">
  <h3 class="text-lg text-black font-semibold mb-1"><%= driver_job.title %></h3>
  <p class="text-xs">
    <%= truncate(driver_job.summary, length: 255) %>
  </p>

  <%= button_to carrier_dashboard_driver_job_url(driver_job.carrier, driver_job),
                method: :delete,
                class: 'p-1.5 absolute top-1 right-1 opacity-60 hover:opacity-100',
                form: { data: { turbo_confirm: 'Are you sure?' } } do %>
    <%= helpers.svg_tag('xmark-large-solid', class: 'w-[16px] h-[16px]') %>
  <% end %>

  <ul class="mt-3 flex flex-wrap text-xs">
    <li class="whitespace-nowrap border-gray-300 border rounded-xs px-1 py-0.5 mr-1 mb-1">
      <%= t("driver_jobs.employment_modes.#{driver_job.employment_mode}") %>
    </li>

    <% if driver_job.operating_schedule.present? %>
      <li class="whitespace-nowrap border-gray-300 border rounded-xs px-1 py-0.5 mr-1 mb-1">
        <%= t("driver_jobs.operating_schedules.#{driver_job.operating_schedule}") %>
      </li>
    <% end %>

    <li class="whitespace-nowrap border-gray-300 border rounded-xs px-1 py-0.5 mr-1 mb-1">
      <%= salary %>
    </li>
  </ul>

  <%= link_to edit_carrier_dashboard_driver_job_url(driver_job.carrier, driver_job), class: 'btn hollow gray mt-3' do %>
    <%= helpers.svg_tag 'pen-to-square-regular', class: 'h-4 w-4 mr-2 fill-gray-700' %>
    <span>Edit</span>
  <% end %>
</div>
