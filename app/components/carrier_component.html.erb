<% if header? %>
  <%= header %>
<% end %>

<% rounding = header? ? 'md:rounded-b-sm' : 'md:rounded-sm' %>

<div itemscope itemtype="https://schema.org/LocalBusiness" id="<%= dom_id(carrier) %>"
     class="border-gray-300 border-t md:border <%= rounding %> p-6 pb-3.5 md:mb-6 relative"
     data-controller="intersection track-event" data-track-event-name-value="Carrier Listing Viewed"
     data-track-event-properties-value="<%= { id: carrier.id, entity_type: 'carrier' }.to_json %>"
     data-action="intersection:appear->track-event#track:once">
  <% if carrier.top_rated? %>
    <div class="ribbon before:border-primary-700 after:border-primary-700">
      <span class="bg-primary text-2xs select-none">
        Top Carrier
      </span>
    </div>
  <% end %>

  <div class="flex flex-row">
    <div class="flex-auto">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-auto">
          <div class="flex flex-col sm:flex-row items-center gap-6">
            <% if logo.present? %>
              <div class="flex-none flex items-center justify-center border border-gray-300 p-1 w-[92px] h-[92px]
                          rounded-xs">
                <%= render PictureComponent.new(logo, dimensions: [82, 82], alt: "#{name} logo",
                                                class: 'w-full h-full object-contain') %>
              </div>
            <% end %>
            <div class="<%= logo.blank? && carrier.top_rated? ? 'ml-12' : nil %>">
              <div class="text-2xl mb-1 text-black font-semibold">
                <div class="flex flex-wrap items-center gap-2 mb-2">
                  <%= link_to carrier_url(carrier) do %>
                    <h2 itemprop="name" class="x-link-carrier-name"><%= name %></h2>
                  <% end %>

                  <%= render Carriers::DoubleBrokeringTagComponent.new(carrier) %>

                  <% if claimed? %>
                    <%= helpers.svg_tag 'badge-check-solid', class: 'inline w-5 h-5 fill-primary',
                                        data: { controller: 'tippy',
                                                tippy_content: 'This carrier profile has been claimed' } %>
                  <% else %>
                    <%= render Carriers::UnclaimedTagComponent.new(carrier) %>
                  <% end %>

                  <%= render Carriers::OperationTagComponent.new(carrier) %>

                  <ul class="flex gap-2 text-sm text-primary-900 font-normal">
                    <% authority_numbers.each_with_index do |number, index| %>
                      <li class="inline-block"><%= number %></li>
                      <% if index < authority_numbers.size - 1 %>
                        <li class="inline-block">&#124;</li>
                      <% end %>
                    <% end %>
                  </ul>
                </div>
              </div>
              <%= render StarsReviewsComponent.new(rating: star_rating, count: review_count,
                                                   link: carrier_url(carrier, anchor: 'reviews')) %>
              <address itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
                <meta itemprop="streetAddress" content="<%= street %>">
                <meta itemprop="addressLocality" content="<%= city.name %>">
                <meta itemprop="addressRegion" content="<%= city.state_code %>">
                <meta itemprop="addressCountry" content="<%= city.country_code %>">
                <meta itemprop="postalCode" content="<%= zip %>">
              </address>
            </div>
          </div>
        </div>
        <% if aside? %>
          <%= aside %>
        <% end %>
      </div>
    </div>
  </div>

  <div class="-mx-6 mt-4 border border-gray-300 border-x-0 flex items-stretch">
    <div class="flex py-4 px-2 border-r border-r-gray-300 bg-primary-50 items-center">
      <%= helpers.svg_tag 'truck-regular', class: 'w-4 h-4 fill-primary',
                          data: { controller: 'tippy', tippy_content: 'This company is a carrier' } %>
    </div>
    <dl class="py-3 px-6">
      <dt class="inline pr-1 after:content-[':']">Authority</dt>
      <dd class="inline">
        <span class="rounded-full h-2.5 w-2.5 inline-block border-2 <%= authority.css %>"></span>
        <span class="x-value-authority"><%= authority.title %></span>
        <%= authority.date ? "(#{l(authority.date, format: :mdy)})" : nil %>
      </dd>
    </dl>
  </div>

  <dl class="flex flex-wrap mb-[-4px] mt-4 ">
    <div class="w-full md:w-1/2 mb-1">
      <dt class="inline pr-1 after:content-[':']">Fleet Size</dt>
      <dd class="inline x-value-fleet-size"><strong><%= number_with_delimiter(fleet_size) %></strong></dd>
    </div>
    <div class="w-full md:w-1/2 mb-1">
      <dt class="inline pr-1 after:content-[':']">Safety Rating</dt>
      <dd class="inline">
        <strong class="x-value-safety-rating"><%= safety_rating %></strong>
      </dd>
    </div>
    <div class="w-full md:w-1/2 mb-1">
      <dt class="inline pr-1 after:content-[':']">Insurance</dt>
      <dd class="inline x-value-insurance-limit">
        <strong><%= insurance_limit %></strong>
      </dd>
    </div>
    <div class="w-full md:w-1/2 mb-1">

    </div>
  </dl>

  <% if freights.present? || truck_types.present? %>
    <%= render HorizontalRuleComponent.new %>

    <div class="flex flex-col md:flex-row mt-2">
      <% if freights.present? %>
        <div class="w-full md:w-1/2" data-controller="read-more" data-read-more-more-text-value="See all"
             data-read-more-less-text-value="See fewer">
          <h3 class="font-semibold text-black">
            <%= t('helpers.label.carrier.freight') %>
            <span class="text-gray-600 font-normal x-value-freights-size">(<%= freights.size %>)</span>
          </h3>
          <ul data-read-more-target="content" class="list-disc ml-4 mt-0.5 flex-wrap flex mb-[-4px] text-sm">
            <% freights[0...4].each do |freight| %>
              <li class="w-1/2 pr-2 mb-1"><%= freight.name %></li>
            <% end %>
          </ul>
          <template data-read-more-target="full">
            <% freights.each do |freight| %>
              <li class="w-1/2 pr-2 mb-1"><%= freight.name %></li>
            <% end %>
          </template>
          <% if freights.size > 4 %>
            <a class="block text-primary ml-4 mt-1 hover:underline" href="javascript:void(0)"
               data-action="read-more#toggle">See all</a>
          <% end %>
        </div>
      <% end %>

      <% if truck_types.present? %>
        <div class="w-full md:w-1/2 mt-4 md:mt-0" data-controller="read-more" data-read-more-more-text-value="See all"
             data-read-more-less-text-value="See fewer">
          <h3 class="font-semibold text-black">
            <%= t('helpers.label.carrier.truck_types') %>
            <span class="text-gray-600 font-normal">(<%= truck_types.size %>)</span>
          </h3>
          <ul data-read-more-target="content" class="list-disc ml-4 mt-0.5 flex-wrap flex mb-[-4px] text-sm">
            <% truck_types[0...4].each do |truck_type| %>
              <li class="w-1/2 pr-2 mb-1"><%= truck_type.name %></li>
            <% end %>
          </ul>
          <template data-read-more-target="full">
            <% truck_types.each do |truck_type| %>
              <li class="w-1/2 pr-2 mb-1"><%= truck_type.name %></li>
            <% end %>
          </template>
          <% if truck_types.size > 4 %>
            <a class="block text-primary ml-4 mt-1 hover:underline" href="javascript:void(0)"
               data-action="read-more#toggle">See all</a>
          <% end %>
        </div>
      <% end %>
    </div>
  <% end %>

  <% if footer? %>
    <%= footer %>
  <% end %>
</div>
