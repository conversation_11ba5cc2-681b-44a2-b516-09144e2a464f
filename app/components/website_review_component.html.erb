<% if website.reviews.include?(review) %>
  <%= button_to review_website_url(review), form: { id: dom_id(review, 'website') }, method: :delete,
                class: 'btn hollow primary' do %>
    <%= helpers.svg_tag 'globe-regular', class: 'h-4 w-4 mr-2 fill-primary' %>
    <span>Website</span>
  <% end %>
<% else %>
  <%= button_to review_website_url(review), form: { id: dom_id(review, 'website') }, method: :patch,
                class: 'btn ghost gray' do %>
    <%= helpers.svg_tag 'globe-regular', class: 'h-4 w-4 mr-2 fill-gray-700' %>
    <span>Website</span>
  <% end %>
<% end %>
