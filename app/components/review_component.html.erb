<div class="relative border <%= border_color %> p-6 pb-0 mb-6 rounded-sm overflow-hidden">
  <% if header? %>
    <%= header %>
  <% end %>

  <% if featured %>
    <%= helpers.svg_tag 'waves', class: '-z-10 absolute left-0 bottom-0 rotate-180 w-full opacity-25 test-waves' %>
  <% end %>

  <div class="flex flex-col md:flex-row">
    <div class="flex-auto pr-4">
      <div class="flex flex-row">
        <div class="w-14 h-14 flex-none rounded-full border-2 border-primary p-0.5 mr-3">
          <div class="bg-primary w-full h-full rounded-full flex justify-center items-center">
            <span class="text-white text-xl"><%= monogram %></span>
          </div>
        </div>

        <div class="flex-auto">
          <p><%= tagline %></p>
          <div class="md:flex md:flex-row items-center mt-1">
            <%= render StarsComponent.new(rating: review.star_rating, class: 'fill-secondary') %>
            <div class="md:ml-2 mt-0.5 text-xs"><%= review.created_at.strftime('%B %d, %Y') %>
            </div>
          </div>
        </div>
      </div>
      <div>
        <h3 class="text-black font-semibold text-xl mt-3">
          &ldquo;<%= title %>&rdquo;
        </h3>
      </div>
    </div>
    <div class="flex-none w-full md:w-[260px]">
      <dl class="border border-gray-200 rounded-sm px-4 py-2 mt-4 md:mt-0 bg-white">
        <% %i(timeliness cleanliness communication).each do |key| %>
          <% value = review.public_send(key) %>
          <% next unless value %>
          <div class="flex flex-row md:justify-end">
            <dt class="mr-2"><%= Review.human_attribute_name(key) %></dt>
            <dd><strong><%= value %></strong>/10</dd>
          </div>
        <% end %>
      </dl>
    </div>
  </div>

  <%= render HorizontalRuleComponent.new %>

  <% if review.sentiments.present? %>
    <div class="flex flex-col space-y-5 mt-2.5">
      <% if positive_sentiments.present? %>
        <div>
          <div class="text-xs font-semibold text-teal mb-1">
            <%= helpers.svg_tag 'thumbs-up-regular', class: 'w-4 h-4 fill-teal inline -mt-1.5' %> What went well?
          </div>
          <ul>
            <% positive_sentiments.each do |sentiment| %>
              <li class="inline-flex items-center px-3 text-2xs border-2 border-teal rounded-full bg-white
                         text-teal mb-0.5">
                <%= sentiment.label %>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <% if negative_sentiments.present? %>
        <div>
          <div class="text-xs font-semibold mb-1">
            <%= helpers.svg_tag 'thumbs-down-regular', class: 'w-4 h-4 inline' %> What went poorly?
          </div>
          <ul>
            <% negative_sentiments.each do |sentiment| %>
              <li class="inline-flex items-center px-3 text-2xs border-2 border-gray-600 rounded-full bg-white mb-0.5">
                <%= sentiment.label %>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>

    <%= render HorizontalRuleComponent.new %>
  <% end %>

  <% if review.body.present? %>
    <%= simple_format(review.body, class: 'mt-2.5') %>
  <% else %>
    <dl>
      <dt class="font-semibold text-black">Likes</dt>
      <dd><%= simple_format(review.likes) %></dd>
      <dt class="mt-2.5 font-semibold text-black">Dislikes</dt>
      <dd><%= simple_format(review.dislikes) %></dd>
    </dl>
  <% end %>

  <%= render HorizontalRuleComponent.new %>

  <dl class="mb-6">
    <div class="flex flex-wrap gap-2 items-center mt-2">
      <dt class="text-black font-semibold after:content-[':']">Lanes</dt>
      <% review_lanes.each do |lane| %>
        <dd class="px-1.5 py-0.5 border border-gray-300 rounded-sm text-black font-semibold bg-white">
          <%= lane.pickup_city.label %>
          <span class="sr-only">to</span>
          <%= helpers.svg_tag('chevron-right-solid', class: 'fill-primary inline-block h-[12px] mt-[-2px]') %>
          <%= lane.dropoff_city.label %>
        </dd>
      <% end %>
    </div>

    <div class="flex flex-wrap gap-2 mt-2">
      <dt class="text-black font-semibold after:content-[':']">Freight Details</dt>
      <% freights.each do |freight| %>
        <dd class="after:content-[','] last:after:content-['']"><%= freight %></dd>
      <% end %>
    </div>

    <% if tracking_functions.present? %>
      <div class="flex flex-wrap gap-2 mt-2">
        <dt class="text-black font-semibold after:content-[':']">Tracking</dt>
        <% tracking_functions.each do |function| %>
          <dd class="after:content-[','] last:after:content-['']"><%= function %></dd>
        <% end %>
      </div>
    <% end %>
  </dl>

  <% if footer? %>
    <%= footer %>
  <% end %>

  <% if featured %>
    <div class="absolute flex items-center bottom-3 right-6 text-2xs gap-1" data-controller="tippy"
         data-tippy-content="<%= carrier.name %> selected this review to appear at the top of their profile">
      <span>Featured Review</span>
      <span><%= helpers.svg_tag 'circle-info-regular', class: 'h-3 w-3 fill-gray-600' %></span>
    </div>
  <% end %>
</div>
