class SponsoredContentComponent < ViewComponent::Base
  TYPES = {
    review: 'ReviewSponsoredContentComponent',
    default: 'DefaultSponsoredContentComponent'
  }.freeze

  attr_reader :resource, :types, :orientation, :track, :options

  delegate :company, :entity, to: :resource, allow_nil: true

  def initialize(resource, type: :default, orientation: :vertical, track: true, **options)
    @resource = resource
    @types = Array.wrap(type).push(:default).uniq
    @orientation = orientation
    @track = track
    @options = options
  end

  def render?
    company.present? && component.present?
  end

  def component
    return @component if defined? @component
    @component =
      types.lazy.map { |type| TYPES[type].constantize.new(resource, provider, orientation:, **options) }.find(&:render?)
  end

  def data_attributes
    if track
      {
        controller: 'intersection track-event',
        track_event_name_value: 'Sponsored Content Viewed',
        track_event_properties_value: { id: company.id, entity_type: entity },
        action: 'intersection:appear->track-event#track:once'
      }
    else
      {}
    end
  end

  def provider
    SponsoredContents::EntityProviders.for(entity).new(company)
  end
end
