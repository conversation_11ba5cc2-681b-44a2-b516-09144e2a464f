class StarsComponent < ViewComponent::Base
  attr_reader :rating, :wrapper_class, :options

  STAR_IMAGES = {
    0.0 => 'star-empty',
    0.5 => 'star-half',
    1.0 => 'star-full'
  }.freeze

  def initialize(rating: 0, wrapper_class: 'flex space-x-0.5', **options)
    @rating = rating
    @wrapper_class = wrapper_class
    @options = options
  end

  def star_images
    Array.new(10) { 0 }.fill { |i| (i + 1) <= star_rating * 2 ? 0.5 : 0 }.each_slice(2)
      .flat_map(&:sum).map { |v| STAR_IMAGES[v.to_f] }
  end

  def star_rating
    (rating * 2.0).round / 2.0
  end
end
