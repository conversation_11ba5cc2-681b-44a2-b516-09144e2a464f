class RequestForProposalComponent < ViewComponent::Base
  attr_reader :carrier, :user

  delegate :active?, :authorized?, to: :carrier
  delegate :email, to: :contact
  delegate :profile, to: :carrier, allow_nil: true
  delegate :claimed?, to: :profile, allow_nil: true

  def initialize(carrier:, user:)
    @carrier = carrier
    @user = user || Nullable.object(:user)
  end

  def render?
    active? && claimed? && authorized? && email.present? && (user.blank? || user.persona?(scope: :contacts))
  end

  def contact
    @contact ||= Companies::ContactInformation.new(carrier)
  end
end
