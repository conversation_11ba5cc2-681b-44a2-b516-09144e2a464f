class BrokerageReviewsController < ApplicationController
  before_action :require_login
  before_action :set_brokerage, only: %i(new create autosave)

  def show
    @review = BrokerageReview.find params[:id]
    authorize @review
  end

  def new
    @review = BrokerageReview.find_or_initialize_by(company: @company, user: current_user) do |review|
      review.persona = current_user.persona
    end

    authorize @review

    redirect_to edit_brokerage_review_url(@review) if @review.persisted?
  end

  def edit
    @review = BrokerageReview.find params[:id]
    authorize @review
  end

  def create
    @review = BrokerageReview.find_or_initialize_by(company: @company, user: current_user) do |review|
      review.persona = current_user.persona
    end

    authorize @review

    form = Forms::BrokerageReview.from_params(params)
    @review.assign_attributes(utm_param: Current.utm_param, **form.attributes)

    if @review.submit!
      redirect_to brokerage_review_url(@review)
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @review = BrokerageReview.find params[:id]
    authorize @review

    form = Forms::BrokerageReview.from_params(params)
    @review.assign_attributes(form.attributes)

    if @review.submit!
      redirect_to brokerage_review_url(@review)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def autosave
    @review = BrokerageReview.find_or_initialize_by(company: @company, user: current_user) do |review|
      review.persona = current_user.persona
    end

    authorize @review

    form = Forms::BrokerageReview.from_params(params)
    @review.assign_attributes(form.attributes.except(:review_lanes_attributes, :screenshot))

    if @review.save(validate: false)
      head :ok
    else
      head :unprocessable_entity
    end
  end

  private

  def set_brokerage
    @company = Company.broker.friendly.find(params[:brokerage_id])
    @brokerage = @company.as_entity(:broker)
  end
end
