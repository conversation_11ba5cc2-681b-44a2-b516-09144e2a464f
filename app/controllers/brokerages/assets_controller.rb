module Brokerages
  class AssetsController < ApplicationController
    before_action :authorize_brokerage

    def show
      asset = %w(email_signature).include?(params[:asset_id]) ? @brokerage.profile.public_send(params[:asset_id]) : nil

      if asset.present? && asset.attached?
        tracker.track 'Email Signature Viewed',
                      analytics_global_properties.merge(id: @brokerage.id, entity_type: 'broker')
        redirect_to rails_blob_path(asset)
      else
        head :not_found
      end
    end

    private

    def authorize_brokerage
      @brokerage = Company.friendly.find(params[:brokerage_id]).as_entity(:broker)
      head :unauthorized unless @brokerage.profile.access_to_feature?(:assets)
    end
  end
end
