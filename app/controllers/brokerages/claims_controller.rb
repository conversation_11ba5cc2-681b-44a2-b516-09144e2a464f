module Brokerages
  class ClaimsController < ApplicationController
    include Wisper::Publisher

    before_action :set_brokerage
    before_action :set_session_persona, if: :signed_out?
    before_action :require_login
    before_action :redirect_to_show, only: %i(new create)

    def show
      @profile_user = BrokerageProfileUser.find params[:id]
      redirect_to brokerage_dashboard_root_url(@brokerage) if @profile_user.verified?
    end

    def new
      broadcast(:claim_initiated, current_user, @brokerage)

      @model = Forms::Brokerages::Claim.new(
        truck_types: @profile.truck_types, shipment_types: @profile.shipment_types,
        specialized_services: @profile.specialized_services
      )

      render layout: 'core'
    end

    def create
      model = Forms::Brokerages::Claim.from_params(params)
      profile_user = Brokerages::ProcessClaim.call(brokerage: @brokerage, current_user:)
      BrokerageProfiles::ClaimResponse.store(profile_user, model.attributes)
      session[:return_to] ||= brokerage_claim_url(@brokerage, profile_user)
      redirect_back_or brokerage_claim_url(@brokerage, profile_user)
    end

    private

    def set_brokerage
      company = Company.broker.friendly.find params[:brokerage_id]
      @brokerage = company.as_entity(:broker)
      @profile = @brokerage.profile
    end

    def set_session_persona
      session[:persona] = { persona: :broker, dot_number: @brokerage.dot_number }
    end

    def redirect_to_show
      profile_user = BrokerageProfileUser.find_by(user: current_user, brokerage_profile: @profile)
      redirect_to brokerage_claim_url(@brokerage, profile_user) if profile_user.present?
    end
  end
end
