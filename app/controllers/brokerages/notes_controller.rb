module Brokerages
  class NotesController < ApplicationController
    before_action :require_login
    before_action :set_note

    def show
    end

    def new
    end

    def create
      if @note.update(note_params)
        respond_to do |format|
          format.html { render :show }
          format.turbo_stream
        end
      else
        render :new, status: :unprocessable_entity
      end
    end

    def destroy
      @note.destroy!

      respond_to do |format|
        format.html { redirect_back(fallback_location: brokerage_url(@brokerage)) }
        format.turbo_stream
      end
    end

    private

    def set_note
      @company = Company.broker.friendly.find params[:brokerage_id]
      @brokerage = @company.as_entity(:broker)
      @note = CompanyNote.find_or_initialize_by(user: current_user, company: @company, entity_type: 'broker')
    end

    def note_params
      params.expect(company_note: [:note])
    end
  end
end
