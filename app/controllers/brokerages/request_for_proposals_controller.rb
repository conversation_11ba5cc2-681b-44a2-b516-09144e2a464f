module Brokerages
  class RequestForProposalsController < ApplicationController
    include RecaptchaVerification
    verify_recaptcha action: 'request_for_proposal', only: :create, if: :signed_out?

    before_action :assign_brokerage

    def new
      @request_for_proposal = RequestForProposal.build(@company, current_user)
    end

    def create
      @request_for_proposal = RequestForProposal.new(request_for_proposal_params)
      @request_for_proposal.assign_attributes(company: @company, user: current_user, entity_type: 'broker')

      if @request_for_proposal.save
        respond_to do |format|
          format.html { redirect_back_or_to brokerage_url(@brokerage) }
          format.turbo_stream
        end
      else
        render :new
      end
    end

    private

    def request_for_proposal_params
      params.expect(
        request_for_proposal: %i(company_name name email phone industry_id pickup_date pickup_city_id dropoff_date
                                 dropoff_city_id frequency frequency_other freight_id shipment_type_id
                                 specialized_service_id truck_type_id description marketing_opt_in)
      )
    end

    def assign_brokerage
      @company = Company.friendly.find(params[:brokerage_id])
      @brokerage = @company.as_entity(:broker)
    end
  end
end
