module Api
  class WebhookSubscriptionsController < BaseController
    deserializable_resource :subscription, class: Webhooks::DeserializableSubscription, only: %i(create)

    def index
      @subscriptions = Webhooks::Subscription.where(developer_account: @developer_account)
      render jsonapi: @subscriptions
    end

    def show
      @subscription = Webhooks::Subscription.find_by!(uuid: params[:id])
      render jsonapi: @subscription
    end

    def create
      @subscription = Webhooks::Subscription.new(params[:subscription].to_unsafe_h)

      @subscription.assign_attributes(developer_account: @developer_account)

      if @subscription.save
        render jsonapi: @subscription.reload, status: :created
      else
        render jsonapi_errors: @subscription.errors, status: :unprocessable_entity
      end
    end

    def destroy
      @subscription = Webhooks::Subscription.find_by!(uuid: params[:id])
      @subscription.destroy
      head :no_content
    end

    def send_test_notification
      subscription = Webhooks::Subscription.find_by!(uuid: params[:id])
      event = Webhooks::SampleEvent.build(type: params[:type], meta: params[:meta] || {})
      Webhooks::DeliverEvent.call(event, subscription)
      head :no_content
    rescue Webhooks::DeliverEvent::Error
      head :unprocessable_entity
    rescue HTTP::TimeoutError
      head :request_timeout
    end
  end
end
