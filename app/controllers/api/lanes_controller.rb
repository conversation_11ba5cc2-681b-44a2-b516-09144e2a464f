module Api
  class LanesController < BaseController
    before_action :set_locations, only: :show

    # rubocop:disable Metrics/AbcSize
    def show
      model = Api::Params::Lane.from_params(
        params.except(:origin, :destination), defaults: { origin: @origin, destination: @destination }
      )

      if model.valid?
        builder = Elastic::Companies::QueryBuilder.new(**model.to_es_query)
        query = Api::ElasticQuery.new(Carrier, builder.build, params)

        render jsonapi: query.records(includes: %i(reviews_aggregate)),
               links: Pagination::Links.build(
                 response: query.response, original_url: request.original_url, size: query.size
               ),
               meta: { url: lane_search_url(**model.url_attributes) }
      else
        render jsonapi_errors: model.errors, status: :bad_request
      end
    end
    # rubocop:enable Metrics/AbcSize

    private

    def set_locations
      country_code = params.fetch(:country_code, 'US')
      @origin = City.joins(:postal_codes).find_by(postal_codes: { code: params[:origin], country_code: })
      @destination = City.joins(:postal_codes).find_by(postal_codes: { code: params[:destination], country_code: })
    end
  end
end
