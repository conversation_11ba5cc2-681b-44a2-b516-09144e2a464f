module Api
  class CapacitiesController < Api::BaseController
    include Pundit::Authorization

    deserializable_resource :capacity, class: DeserializableCarrierAvailability, only: %i(create update)

    before_action :assign_carrier, only: :create

    def index
      query_params = Api::Params::Capacity.from_params(params)
      builder = Elastic::CarrierAvailabilities::QueryBuilder.new(**query_params.to_es_query)
      query = Api::ElasticQuery.new(CarrierAvailability, builder.build, params)

      render jsonapi: query.records(includes: %i(company origin_cities destination_cities truck_type shipment_type)),
             links: Pagination::Links.build(
               response: query.response, original_url: request.original_url, size: query.size
             )
    end

    def show
      availability = CarrierAvailability.find_by!(uuid: params[:id])
      render jsonapi: availability
    end

    def create
      availability = CarrierAvailability.new(capacity_params.merge(company: @company))

      authorize @carrier.profile

      if availability.save
        render jsonapi: availability, status: :created
      else
        render jsonapi_errors: availability.errors, status: :unprocessable_entity
      end
    end

    def update
      availability = CarrierAvailability.find_by!(uuid: params[:id])

      authorize availability.carrier.profile

      if availability.update(capacity_params)
        render jsonapi: availability
      else
        render jsonapi_errors: availability.errors, status: :unprocessable_entity
      end
    end

    def destroy
      availability = CarrierAvailability.find_by!(uuid: params[:id])
      authorize availability.carrier.profile
      availability.destroy!
      head :no_content
    end

    private

    def assign_carrier
      @company = Company.indexable.locate!(params[:carrier_id])
      @carrier = @company.as_entity(:carrier)
    end

    def capacity_params
      params.expect(
        capacity: [:date, :truck_type_id, :shipment_type_id, :length, :weight, :origin_type, :destination_type,
                   { origin_city_ids: [], destination_city_ids: [], origin_state_ids: [], destination_state_ids: [] }]
      )
    end
  end
end
