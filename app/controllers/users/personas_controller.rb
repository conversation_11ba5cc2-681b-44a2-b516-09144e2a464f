module Users
  class PersonasController < ApplicationController
    include Clearance::GuardActions

    before_action :redirect_signed_in_users
    before_action :set_user

    def new
      @form = Forms::Persona.from_params(params)
    end

    def create
      @form = Forms::Persona.from_params(params, defaults: { user: @user })

      if @form.valid?
        Users::Personas.create(@user, **@form.attributes)
        sign_in_with_guards @user
      else
        render :new, status: :unprocessable_entity
      end
    end

    private

    def set_user
      @user = User.find params[:user_id]
    end
  end
end
