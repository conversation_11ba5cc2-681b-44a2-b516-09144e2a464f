class WebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token

  def create
    handler = IncomingWebhookEvents::Handlers.for(params[:source]).new(request, params)
    event = handler.event
    event.process! if event.may_process?
    handler.response.then do |response|
      render json: response.data, status: response.status
    end
  rescue StandardError => e
    render json: { message: e.message }, status: :bad_request
  end
end
