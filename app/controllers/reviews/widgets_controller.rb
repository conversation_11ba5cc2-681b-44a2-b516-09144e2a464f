module Reviews
  class WidgetsController < ApplicationController
    before_action :require_login
    before_action :set_variables
    before_action :authorize_profile

    def update
      if Reviews::AddToWidget.call(@widget, @review)
        respond_to do |format|
          format.html { redirect_to review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    def destroy
      if Reviews::RemoveFromWidget.call(@widget, @review)
        respond_to do |format|
          format.html { redirect_to review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    private

    def set_variables
      @review = Review.find params[:review_id]
      @profile = @review.carrier.profile
      @widget = Widget.find_or_create_by(company: @review.company, entity_type: 'carrier', widget_type: 'review')
    end

    def authorize_profile
      authorize @profile
    end
  end
end
