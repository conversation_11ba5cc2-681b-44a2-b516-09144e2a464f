module Reviews
  class FeaturesController < ApplicationController
    before_action :require_login
    before_action :set_review
    before_action :authorize_profile

    def update
      @featured_review = Review.find_by(company: @review.company, featured: true)

      if Reviews::MarkAsFeatured.call(@review)
        respond_to do |format|
          format.html { redirect_to review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    def destroy
      if Reviews::MarkAsUnfeatured.call(@review)
        respond_to do |format|
          format.html { redirect_to review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    private

    def set_review
      @review = Review.find params[:review_id]
    end

    def authorize_profile
      @profile = @review.carrier.profile
      authorize @profile
    end
  end
end
