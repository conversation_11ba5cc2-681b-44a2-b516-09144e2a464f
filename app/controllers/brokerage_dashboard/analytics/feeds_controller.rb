module BrokerageDashboard
  module Analytics
    class FeedsController < BrokerageDashboard::ApplicationController
      before_action do
        @skip_profile_completion = true
        @navigation_page =
          BrokerageDashboard::Navigation.page('shipper_intent.targets', @brokerage, current_user, params)
      end

      def index
        @integrations = ::Analytics::Integration.where(company: @company, entity_type: 'broker')
        @feeds = ::Analytics::CompanyEventFeed.where(company: @company, entity_type: 'broker').order(:created_at)
      end

      def new
        @feed = ::Analytics::CompanyEventFeed.new(company: @company, entity_type: 'broker')
      end

      def edit
        @feed = ::Analytics::CompanyEventFeed.find(params[:id])
      end

      def create
        @feed = ::Analytics::CompanyEventFeed.new(feed_params.merge(company: @company, entity_type: 'broker'))

        if @feed.save
          redirect_to brokerage_dashboard_analytics_feeds_url(@brokerage), notice: 'Target was successfully created.'
        else
          render :new
        end
      end

      def update
        @feed = ::Analytics::CompanyEventFeed.find(params[:id])

        if @feed.update(feed_params)
          redirect_to brokerage_dashboard_analytics_feeds_url(@brokerage), notice: 'Target was successfully updated.'
        else
          render :edit
        end
      end

      def destroy
        @feed = ::Analytics::CompanyEventFeed.find(params[:id])
        @feed.destroy!
        redirect_to brokerage_dashboard_analytics_feeds_url(@brokerage), notice: 'Target was successfully removed.'
      end

      def duplicate
        feed = ::Analytics::CompanyEventFeed.find(params[:id]).dup
        feed.update!(name: "#{feed.name} (copy)", editable: true)
        redirect_to edit_brokerage_dashboard_analytics_feed_url(@brokerage, feed)
      end

      def export
        feed = ::Analytics::CompanyEventFeed.find(params[:id])
        ::Analytics::CompanyEventFeedExport.create(user: current_user, feed:, email: params[:email])

        respond_to do |format|
          format.html do
            redirect_to brokerage_dashboard_analytics_feeds_url(@brokerage), notice: 'Exporting target...'
          end

          format.turbo_stream
        end
      end

      private

      def feed_params
        params.expect(analytics_company_event_feed: [:name, { filters: {} }]).to_h.tap do |permitted|
          permitted[:filters] = Forms::AnalyticsEvent.new(permitted[:filters]).to_h
        end
      end
    end
  end
end
