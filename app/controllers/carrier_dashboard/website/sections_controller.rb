module CarrierDashboard
  module Website
    class SectionsController < CarrierDashboard::ApplicationController
      def show
        @website = CarrierProfileWebsite.find_or_create_by(carrier_profile: @profile)
      end

      def update
        @website = CarrierProfileWebsite.find_by! carrier_profile: @profile

        if @website.update(website_params)
          redirect_to carrier_dashboard_website_sections_url(@carrier), notice: 'Saved successfully'
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def website_params
        params
          .expect(carrier_profile_website: [:highlights_heading, :highlights_subheading,
                                            { services_attributes: [%i(id name description _destroy)],
                                              highlights_attributes: [%i(id heading subheading _destroy)] }])
      end
    end
  end
end
