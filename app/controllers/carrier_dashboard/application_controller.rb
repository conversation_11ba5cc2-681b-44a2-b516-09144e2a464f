module CarrierDashboard
  class ApplicationController < ::ApplicationController
    helper CarrierDashboard::ApplicationHelper

    before_action :require_login
    before_action :set_profile
    before_action :redirect_to_verification
    before_action :authorize_profile

    def set_profile
      @company = Company.carrier.friendly.find params[:carrier_id]
      @carrier = @company.as_entity(:carrier)
      @profile = @carrier.profile
      @profile_user = CarrierProfileUser.find_by(user: current_user, carrier_profile: @profile)
    end

    def authorize_profile
      authorize @profile
    end

    def redirect_to_verification
      return if @profile_user.blank? || @profile_user.verified?
      store_location
      redirect_to carrier_claim_url(@carrier, @profile_user)
    end

    def access_to_feature?(feature)
      @profile.access_to_feature?(feature) || current_user.admin?
    end
  end
end
