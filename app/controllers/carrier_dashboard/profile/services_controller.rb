module CarrierDashboard
  module Profile
    class ServicesController < ApplicationController
      def show
      end

      def update
        if @profile.update(profile_params)
          redirect_back_or_to carrier_dashboard_profile_services_url(@carrier), notice: 'Saved successfully'
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def profile_params
        params.expect(
          carrier_profile: [:no_specialized_services, { truck_types: [], shipment_types: [], specialized_services: [] }]
        )
      end
    end
  end
end
