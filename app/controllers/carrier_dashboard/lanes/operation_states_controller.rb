module CarrierDashboard
  module Lanes
    class OperationStatesController < ApplicationController
      def show
        @form = Forms::CarrierLane.from_profile(@profile)
      end

      def update
        @form = Forms::CarrierLane.from_params(params)

        @profile.on(:after_update) do
          CarrierProfiles::StoreLanesMapJob.perform_async(@profile.id)
          ::Analytics::CompanyEventFeeds::UpdateCarrierDefaultJob.perform_async(@carrier.to_gid.to_s)
          Elastic::SyncRecordJob.perform_async(@carrier.to_gid.to_s)
        end

        if @profile.update(@form.attributes)
          redirect_to carrier_dashboard_lanes_operation_states_url(@carrier), notice: 'Saved successfully'
        else
          render :show, status: :unprocessable_entity
        end
      end
    end
  end
end
