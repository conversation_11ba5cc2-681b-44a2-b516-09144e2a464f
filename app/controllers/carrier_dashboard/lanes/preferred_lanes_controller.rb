module CarrierDashboard
  module Lanes
    class PreferredLanesController < ApplicationController
      def show
      end

      def update
        @profile.on(:after_update) { Elastic::SyncRecordJob.perform_async(@carrier.to_gid.to_s) }

        if @profile.update(permitted_params)
          redirect_to carrier_dashboard_lanes_preferred_lanes_url(@carrier), notice: 'Saved successfully'
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def permitted_params
        params.expect(carrier_profile: [preferred_lanes_attributes: [%i(id pickup_city_id dropoff_city_id _destroy)]])
      end
    end
  end
end
