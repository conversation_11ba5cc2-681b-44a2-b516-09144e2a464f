module CarrierDashboard
  module Lanes
    class TerminalsController < ApplicationController
      def show
      end

      def update
        @profile.on(:after_update) do
          CarrierProfiles::StoreLanesMapJob.perform_async(@profile.id)
          Elastic::SyncRecordJob.perform_async(@carrier.to_gid.to_s)
        end

        if @profile.update(permitted_params)
          redirect_to carrier_dashboard_lanes_terminals_url(@carrier), notice: 'Saved successfully'
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def permitted_params
        params.expect(carrier_profile: [:no_additional_terminals, { terminals_attributes: [%i(id city_id _destroy)] }])
      end
    end
  end
end
