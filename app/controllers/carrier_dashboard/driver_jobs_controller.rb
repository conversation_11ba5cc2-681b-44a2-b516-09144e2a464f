module CarrierDashboard
  class DriverJobsController < ApplicationController
    before_action do
      @navigation_page = CarrierDashboard::Navigation.page('driver_jobs', @carrier, current_user, params)
    end

    def index
      @driver_jobs = DriverJob.where(company: @company)
    end

    def new
      @driver_job = DriverJob.new(company: @company)
    end

    def edit
      @driver_job = DriverJob.find(params[:id])
    end

    def create
      @driver_job = DriverJob.new(driver_job_params.merge(company: @company))

      if @driver_job.save
        redirect_to carrier_dashboard_driver_jobs_url(@carrier)
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      @driver_job = DriverJob.find(params[:id])

      if @driver_job.update(driver_job_params)
        redirect_to carrier_dashboard_driver_jobs_url(@carrier)
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @driver_job = DriverJob.find(params[:id])
      @driver_job.destroy
      redirect_to carrier_dashboard_driver_jobs_url(@carrier)
    end

    private

    def driver_job_params
      params.expect(
        driver_job: %i(title
                       summary
                       employment_mode
                       operating_schedule
                       salary_min
                       salary_max
                       salary_unit
                       published
                       description
                       requirements
                       benefits)
      )
    end
  end
end
