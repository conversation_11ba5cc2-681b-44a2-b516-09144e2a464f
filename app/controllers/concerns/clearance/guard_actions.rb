module Clearance
  module GuardActions
    extend ActiveSupport::Concern

    included do
      include Wisper::Publisher
    end

    # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
    def sign_in_with_guards(user, redirect_location = nil)
      Personas::CreateFromSession.call(session:, user:)

      sign_in user.try(:reload) do |status|
        if status.success?
          broadcast(:user_signed_in, user)
          redirect_back_or redirect_location || Clearance.configuration.redirect_url
        else
          case status.failure_message
          when 'sign_in_guards.email_verification'
            redirect_to user_verification_url(user)
          when 'sign_in_guards.persona'
            redirect_to new_user_persona_url(user)
          else
            flash.now.alert = I18n.t(status.failure_message, default: status.failure_message)
            render :new, status: :unauthorized
          end
        end
      end
    end
    # rubocop:enable Metrics/AbcSize,Metrics/MethodLength
  end
end
