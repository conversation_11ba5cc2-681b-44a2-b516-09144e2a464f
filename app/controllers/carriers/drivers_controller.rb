module Carriers
  class DriversController < ApplicationController
    include Clearance::GuardActions

    before_action :set_carrier

    def show
      return redirect_to new_carrier_driver_review_url(@carrier) if DriverCarrierReviewPolicy.new(
        current_user || User.new, DriverCarrierReview.new
      ).new?

      session[:persona] = { persona: :driver, dot_number: @carrier.dot_number }
      session[:return_to] = new_carrier_driver_review_url(@carrier)

      render layout: 'core'
    end

    private

    def set_carrier
      @company = Company.carrier.friendly.find params[:carrier_id]
      @carrier = @company.as_entity(:carrier)
      @profile = Carriers::Profile.new(@carrier)
    end
  end
end
