module Carriers
  class EmailClaimsController < ApplicationController
    include Clearance::GuardActions

    before_action :redirect_signed_in_users
    before_action :set_carrier
    before_action :verify_token

    def show
      @user = User.new email: @carrier.contact_email
    end

    def update
      @user = User.new verified: true, **user_params.to_h.symbolize_keys

      if @user.save
        session[:persona] = { persona: :carrier, dot_number: @carrier.dot_number }
        sign_in_with_guards @user
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def user_params
      params.expect(user: %i(first_name last_name email password))
    end

    def set_carrier
      @company = Company.friendly.find params[:carrier_id]
      @carrier = @company.as_entity(:carrier)
    end

    def verify_token
      return if @carrier.claim_token == params[:token]
      redirect_to root_url, alert: 'You are not authorized to perform this action'
    end
  end
end
