module Carriers
  class DriverReviewsController < ApplicationController
    before_action :require_login
    before_action :set_carrier, only: %i(new create autosave)

    def show
      @review = DriverCarrierReview.find params[:id]
      authorize @review
    end

    def new
      @review = DriverCarrierReview.find_or_initialize_by(company: @company, user: current_user)
      authorize @review

      redirect_to edit_driver_review_url(@review) if @review.persisted?
    end

    def edit
      @review = DriverCarrierReview.find params[:id]
      authorize @review
    end

    def create
      @review = DriverCarrierReview.find_or_initialize_by(company: @company, user: current_user)
      authorize @review

      form = Forms::DriverCarrierReview.from_params(params)
      @review.assign_attributes(utm_param: Current.utm_param, **form.attributes)

      if @review.submit!
        redirect_to driver_review_url(@review), notice: 'Your review has been submitted.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      @review = DriverCarrierReview.find params[:id]
      authorize @review

      form = Forms::DriverCarrierReview.from_params(params)
      @review.assign_attributes(form.attributes)

      if @review.submit!
        redirect_to driver_review_url(@review), notice: 'Your review has been updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def autosave
      @review = DriverCarrierReview.find_or_initialize_by(company: @company, user: current_user)
      authorize @review

      form = Forms::DriverCarrierReview.from_params(params)
      @review.assign_attributes(form.attributes.except(:screenshot))

      if @review.save(validate: false)
        head :ok
      else
        head :unprocessable_entity
      end
    end

    private

    def set_carrier
      @company = Company.friendly.find params[:carrier_id]
      @carrier = @company.as_entity(:carrier)
    end
  end
end
