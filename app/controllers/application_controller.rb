class ApplicationController < ActionController::Base
  include Clearance::Controller
  include Pundit::Authorization
  include SetCurrentRequestDetails

  helper_method :analytics_global_properties

  before_action :store_return_to_location
  before_action :store_current_login_ip
  before_action :store_wizard_return_to

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  protected

  def analytics_global_properties
    { url: request.original_url, route: "#{controller_path}##{action_name}", path_parameters: request.path_parameters }
  end

  def tracker
    @tracker ||= Ahoy::Tracker.new(controller: self, api: true)
  end

  private

  def store_return_to_location
    return if params[:return_to].blank?
    session[:return_to] = params[:return_to]
  end

  def store_current_login_ip
    return if current_user.blank?
    current_user.update(current_login_ip: request.ip)
  end

  def user_not_authorized
    flash.alert = 'You are not authorized to perform this action.'
    redirect_back(fallback_location: root_url)
  end

  def store_wizard_return_to
    return unless Current.wizard.active?
    session_return_to = Current.wizard.session_return_to
    session[:return_to] = session_return_to if session_return_to.present?
  end

  def redirect_signed_in_users
    redirect_back_or Clearance.configuration.redirect_url if signed_in?
  end
end
