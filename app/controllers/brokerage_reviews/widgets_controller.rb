module BrokerageReviews
  class WidgetsController < ApplicationController
    before_action :require_login
    before_action :set_variables
    before_action :authorize_profile

    def update
      if BrokerageReviews::AddToWidget.call(@widget, @review)
        respond_to do |format|
          format.html { redirect_to brokerage_review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    def destroy
      if BrokerageReviews::RemoveFromWidget.call(@widget, @review)
        respond_to do |format|
          format.html { redirect_to brokerage_review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    private

    def set_variables
      @review = BrokerageReview.find params[:brokerage_review_id]
      @profile = @review.brokerage.profile
      @widget = Widget.find_or_create_by(
        company: @review.company, entity_type: 'broker', widget_type: 'review'
      )
    end

    def authorize_profile
      authorize @profile
    end
  end
end
