module BrokerageReviews
  class ReportsController < ApplicationController
    before_action :require_login
    before_action :set_review
    before_action :authorize_profile

    def show
      @report = @review.report
    end

    def new
      @report = @review.report || @review.build_report
    end

    def edit
      @report = @review.report
    end

    def create
      @report = @review.build_report(report_params)
      @report.user = current_user

      if @report.save
        respond_to do |format|
          format.html { redirect_to brokerage_review_report_url(@review) }
          format.turbo_stream
        end
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      @report = @review.report

      if @report.update(report_params)
        respond_to do |format|
          format.html { redirect_to brokerage_review_report_url(@review) }
          format.turbo_stream
        end
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      if @review.report.destroy
        respond_to do |format|
          format.html { redirect_to brokerage_review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    private

    def set_review
      @review = BrokerageReview.find params[:brokerage_review_id]
    end

    def authorize_profile
      @profile = @review.brokerage.profile
      authorize @profile
    end

    def report_params
      params.expect(brokerage_review_report: %i(reason description))
    end
  end
end
