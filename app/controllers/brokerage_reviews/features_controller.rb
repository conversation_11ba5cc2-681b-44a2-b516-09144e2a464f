module BrokerageReviews
  class FeaturesController < ApplicationController
    before_action :require_login
    before_action :set_review
    before_action :authorize_profile

    def update
      @featured_review = BrokerageReview.find_by(company: @review.company, featured: true)

      if BrokerageReviews::MarkAsFeatured.call(@review)
        respond_to do |format|
          format.html { redirect_to brokerage_review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    def destroy
      if BrokerageReviews::MarkAsUnfeatured.call(@review)
        respond_to do |format|
          format.html { redirect_to brokerage_review_url(@review) }
          format.turbo_stream
        end
      else
        head :unprocessable_entity
      end
    end

    private

    def set_review
      @review = BrokerageReview.find params[:brokerage_review_id]
    end

    def authorize_profile
      @profile = @review.brokerage.profile
      authorize @profile
    end
  end
end
