module Dashboard
  class SubscriptionsController < ApplicationController
    def show
      redirect_to broker_pricing_url if current_user.subscription.blank?
    end

    def success
      subscription = Subscriptions::CreateFromStripeSession.call(session_id: params[:session_id])
      SubscriptionMailer.with(subscription:).welcome.deliver_later(wait: 5.minutes)
      redirect_to dashboard_subscription_url
    end

    def edit
      subscription = Stripe::Subscription.retrieve(current_user.subscription.external_id)

      Stripe::BillingPortal::Session.create({ customer: subscription.customer, return_url: dashboard_subscription_url })
        .then { |session| redirect_to session.url, allow_other_host: true }
    end

    def create
      return redirect_to dashboard_subscription_url if current_user.subscription

      Subscriptions::CreateCheckoutSession.call(user: current_user, tier: params[:tier], interval: params[:interval])
        .then { |session| redirect_to session.url, allow_other_host: true }
    end
  end
end
