module Dashboard
  class ReviewsController < ApplicationController
    def show
      query = Elastic::Reviews::QueryBuilder.new(filters: { user_id: current_user.id }, order: %i(id desc))
      @reviews = Elasticsearch::Model.search(query.build.to_hash, search_models)
                   .page(params.fetch(:page, 1)).per(10).records(includes:)
    end

    private

    def search_models
      [::Review, ::BrokerageReview]
    end

    def includes
      [:user, :reply, { review_lanes: %i(pickup_city dropoff_city) }].then do |associations|
        { 'Review' => associations, 'BrokerageReview' => associations }
      end
    end
  end
end
