module TruckingCompanies
  class LoadsController < ApplicationController
    before_action :assign_record
    before_action :redirect_to_load_slug
    before_action :assign_country, except: :index
    before_action :redirect_to_country_slug, except: :index
    before_action :assign_state, except: %i(index country)
    before_action :assign_city, only: :city
    before_action :redirect_to_closest_city, only: :city

    def index
      perform_search
    end

    def country
      @states = TruckingCompanies::Loads::Location.new(@record).states(@country)
      perform_search(country_id: @country.slug)
    end

    def state
      @cities = TruckingCompanies::Loads::Location.new(@record).cities(@country, @state)
      perform_search(state_id: @state.id)
    end

    def city
      perform_search(city_id: @city.id)
      @expanded_provider = Companies::SearchProviders::Standard.new(@search_form.new(radius: 50), params)
    end

    private

    def assign_record
      @record = TruckingCompanies::Load.record(params[:id]) || TruckingCompanies::Load::NULL.instance
    end

    def assign_country
      @country = Geo::Country.find(params[:country]) || raise(ActiveRecord::RecordNotFound)
    end

    def assign_state
      @state = @country.states.find_by(slug: params[:state]) || raise(ActiveRecord::RecordNotFound)
    end

    def assign_city
      @city = City.find_by!(country_code: @country.alpha2, state_code: @state.abbr, slug: params[:city])
    end

    def redirect_to_country_slug
      return if @country.slug == params[:country]
      redirect_to url_for(country: @country.slug, **params.permit(:controller, :action, :state, :city).to_h)
    end

    def redirect_to_load_slug
      return if @record.slug == params[:id]
      redirect_to url_for(id: @record.slug, **params.permit(:controller, :action, :country, :state, :city).to_h)
    end

    def redirect_to_closest_city
      return if TruckingCompanies::Loads::Location.new(@record).exists?(@city)
      city = TruckingCompanies::Loads::Location.new(@record).alternate_city(@city)
      redirect_to city_load_trucking_companies_url(@record, *city.path) if city.present?
    end

    # rubocop:disable Metrics/AbcSize
    def perform_search(defaults = {})
      @search_form = Forms::Carrier.from_params(
        params,
        defaults: {
          sort: :cs_score, @record.model_name.param_key => @record.to_param, entity_type: :carrier,
          **defaults
        }
      )
      @provider = Companies::SearchProviders::Standard.new(@search_form, params)
      customer_provider = Companies::SearchProviders::Standard.new(
        @search_form.new(radius: 50, upgraded: true), params
      )
      @companies = @provider.records
      @customers = customer_provider.records.sample(2).cycle(2).to_a

      redirect_to Current.canonical_url if @companies.current_page > [@companies.total_pages, 1].max
    end
    # rubocop:enable Metrics/AbcSize
  end
end
