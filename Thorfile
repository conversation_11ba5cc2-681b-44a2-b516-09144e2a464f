# Load the Rails environment
require_relative 'config/environment' unless %w(-h --help help -T list -v version).include?(ARGV.first)

module Verbose<PERSON>og<PERSON>
  def self.prepended(base)
    base.class_eval do
      class_option :verbose, type: :boolean
    end
  end

  def initialize(args = [], local_options = {}, config = {})
    super

    return unless options[:verbose]

    logger = Logger.new($stdout)
    logger.level = Logger::DEBUG
    Rails.logger = ActiveRecord::Base.logger = logger
  end
end

# Ensure tasks exit with non-zero status if something goes wrong
Thor.class_eval do
  prepend VerboseLogger

  def self.exit_on_failure?
    true
  end
end
