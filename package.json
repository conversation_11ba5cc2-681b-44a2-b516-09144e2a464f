{"name": "carrier_source", "version": "1.0.0", "repository": "**************:CarrierSource-code/carrier_source.git", "license": "MIT", "private": true, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "dependencies": {"@xmldom/xmldom": "^0.9.8", "geojson-extent": "^0.3.2", "geojson2svg": "^2.0.2", "proj4": "^2.19.3"}, "devDependencies": {"@activeadmin/activeadmin": "4.0.0-beta6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/cz-commitlint": "^19.8.1", "@eslint/js": "^9.29.0", "@github/combobox-nav": "^3.0.1", "@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^8.0.16", "@rails/actiontext": "^8.0.200", "@rails/activestorage": "^8.0.200", "@rails/request.js": "^0.0.12", "@rails/ujs": "^7.1.501", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@tailwindcss/postcss": "^4.1.10", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "concurrently": "^9.1.2", "conventional-changelog-conventionalcommits": "^9.0.0", "cssnano": "^7.0.7", "esbuild": "^0.25.5", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jest": "^29.0.1", "flowbite": "^2.5.2", "globals": "^16.2.0", "hotkeys-js": "^3.13.10", "inquirer": "^12.6.3", "jest": "^30.0.2", "lodash.isequal": "^4.5.0", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "postcss-import": "^16.1.1", "postcss-nested": "^7.0.2", "prettier": "^3.5.3", "run-pty": "^5.0.0", "sortablejs": "^1.15.6", "stimulus-carousel": "^5.0.1", "stimulus-character-counter": "^4.2.0", "stimulus-chartjs": "^5.0.1", "stimulus-clipboard": "^4.0.1", "stimulus-color-picker": "^1.1.0", "stimulus-dropdown": "^2.1.0", "stimulus-lightbox": "^3.2.0", "stimulus-notification": "^2.2.0", "stimulus-rails-nested-form": "^4.1.0", "stimulus-scroll-to": "^4.1.0", "stimulus-sortable": "^4.1.1", "stimulus-use": "^0.52.3", "svgo": "^3.3.2", "swiper": "^11.2.8", "tailwindcss": "^4.1.10", "tailwindcss-box-shadow": "^2.0.3", "tailwindcss3": "npm:tailwindcss@3.4.17", "tippy.js": "^6.3.7", "tom-select": "^2.4.3", "trix": "^2.1.15"}, "engines": {"node": "~22.11.0"}, "scripts": {"build": "concurrently \"yarn:build:*\"", "build:js": "node esbuild.js", "build:css": "postcss ./app/assets/stylesheets/postcss/*.css --dir ./app/assets/builds --ext .css", "test": "jest"}, "packageManager": "yarn@4.9.1"}