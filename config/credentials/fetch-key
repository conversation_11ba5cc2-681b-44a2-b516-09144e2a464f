#!/usr/bin/env bash

set -e

green=$(tput setaf 2)
reset=$(tput sgr0)

usage() {
  echo "usage: fetch-key [[-e environment] | [-h]]"
}

environment=development
profile=default
directory=$(dirname "$0")

while [[ "$1" != "" ]]; do
  case $1 in
  -e | --environment)
    shift
    environment=$1
    ;;
  -h | --help)
    usage
    exit
    ;;
  -p | --profile)
    shift
    profile=$1
    ;;
  *)
    usage
    exit 1
    ;;
  esac
  shift
done

if ! [ -x "$(command -v jq)" ]; then
  echo 'Error: jq is not installed. https://stedolan.github.io/jq/' >&2
  exit 1
fi

if ! [ -x "$(command -v aws)" ]; then
  echo 'Error: AWS Command Line Interface is not installed. https://aws.amazon.com/cli/' >&2
  exit 1
fi

key=$(aws ssm get-parameter --name /carrier_source/config/credentials/"$environment".key --profile "$profile" | jq --raw-output '.Parameter.Value')

echo "$key" > "${directory}"/"$environment".key
echo "Created ${green}${environment}.key${reset} file"
