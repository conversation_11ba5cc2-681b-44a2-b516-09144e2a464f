pin 'flowbite' # downloaded from https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js
pin '@rails/ujs', to: 'https://ga.jspm.io/npm:@rails/ujs@7.1.3/app/assets/javascripts/rails-ujs.esm.js'
pin 'application.admin'
pin_all_from File.join(`bundle show activeadmin`.strip, 'app/javascript/active_admin'), under: 'active_admin'

pin '@hotwired/stimulus', to: 'stimulus.min.js'
pin '@hotwired/stimulus-loading', to: 'stimulus-loading.js'
pin_all_from 'app/javascript/controllers', under: 'controllers'
pin_all_from 'app/javascript/util', under: 'util'

pin 'tom-select', to: 'https://ga.jspm.io/npm:tom-select@2.4.3/dist/js/tom-select.complete.js'
