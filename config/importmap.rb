# Pin npm packages by running ./bin/importmap

pin 'application'
pin 'application.dashboard'
pin '@hotwired/turbo-rails', to: 'turbo.min.js'
pin '@hotwired/stimulus', to: 'stimulus.min.js'
pin '@hotwired/stimulus-loading', to: 'stimulus-loading.js'
pin '@rails/activestorage', to: 'activestorage.esm.js'
pin '@rails/actiontext', to: 'actiontext.js'
pin 'ahoy', to: 'ahoy.js'
pin 'trix'
pin_all_from 'app/javascript/controllers', under: 'controllers'
pin_all_from 'app/javascript/util', under: 'util'

pin '@github/combobox-nav', to: 'https://ga.jspm.io/npm:@github/combobox-nav@3.0.1/dist/index.js'
pin '@kurkle/color', to: 'https://ga.jspm.io/npm:@kurkle/color@0.3.4/dist/color.esm.js'
pin '@popperjs/core', to: 'https://ga.jspm.io/npm:@popperjs/core@2.11.8/lib/index.js'
pin '@rails/request.js', to: 'https://ga.jspm.io/npm:@rails/request.js@0.0.11/src/index.js'
pin '@simonwep/pickr', to: 'https://ga.jspm.io/npm:@simonwep/pickr@1.9.1/dist/pickr.min.js'
pin 'dom7', to: 'https://ga.jspm.io/npm:dom7@4.0.6/dom7.esm.js'
pin 'hotkeys-js', to: 'https://ga.jspm.io/npm:hotkeys-js@3.13.10/dist/hotkeys.esm.js'
pin 'ssr-window', to: 'https://ga.jspm.io/npm:ssr-window@4.0.2/ssr-window.esm.js'
pin 'swiper/bundle', to: 'https://ga.jspm.io/npm:swiper@11.2.8/swiper-bundle.mjs'

pin 'chart.js/auto', to: 'https://ga.jspm.io/npm:chart.js@4.4.7/auto/auto.js'
pin 'lightgallery', to: 'https://ga.jspm.io/npm:lightgallery@2.8.2/lightgallery.es5.js'
pin 'lodash.isequal', to: 'https://ga.jspm.io/npm:lodash.isequal@4.5.0/index.js'
pin 'sortablejs', to: 'https://ga.jspm.io/npm:sortablejs@1.15.6/modular/sortable.esm.js'
pin 'stimulus-carousel', to: 'https://ga.jspm.io/npm:stimulus-carousel@5.0.1/dist/stimulus-carousel.mjs'
pin 'stimulus-character-counter', to: 'https://ga.jspm.io/npm:stimulus-character-counter@4.2.0/dist/stimulus-character-counter.mjs'
pin 'stimulus-chartjs', to: 'https://ga.jspm.io/npm:stimulus-chartjs@5.0.1/dist/stimulus-chartjs.mjs'
pin 'stimulus-clipboard', to: 'https://ga.jspm.io/npm:stimulus-clipboard@4.0.1/dist/stimulus-clipboard.mjs'
pin 'stimulus-color-picker', to: 'https://ga.jspm.io/npm:stimulus-color-picker@1.1.0/dist/stimulus-color-picker.mjs'
pin 'stimulus-dropdown', to: 'https://ga.jspm.io/npm:stimulus-dropdown@2.1.0/dist/stimulus-dropdown.mjs'
pin 'stimulus-lightbox', to: 'https://ga.jspm.io/npm:stimulus-lightbox@3.2.0/dist/stimulus-lightbox.mjs'
pin 'stimulus-notification', to: 'https://ga.jspm.io/npm:stimulus-notification@2.2.0/dist/stimulus-notification.mjs'
pin 'stimulus-rails-nested-form', to: 'https://ga.jspm.io/npm:stimulus-rails-nested-form@4.1.0/dist/stimulus-rails-nested-form.mjs'
pin 'stimulus-sortable', to: 'https://ga.jspm.io/npm:stimulus-sortable@4.1.1/dist/stimulus-sortable.mjs'
pin 'stimulus-scroll-to', to: 'https://ga.jspm.io/npm:stimulus-scroll-to@4.1.0/dist/stimulus-scroll-to.mjs'
pin 'stimulus-use', to: 'https://ga.jspm.io/npm:stimulus-use@0.52.3/dist/index.js'
pin 'tippy.js', to: 'https://ga.jspm.io/npm:tippy.js@6.3.7/dist/tippy.esm.js'
pin 'tom-select', to: 'https://ga.jspm.io/npm:tom-select@2.4.3/dist/js/tom-select.complete.js'
