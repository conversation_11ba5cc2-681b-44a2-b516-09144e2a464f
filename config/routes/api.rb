constraints Routes::BuildConstraints.call(subdomain: 'api') do
  scope module: :api, as: :api do
    resource :me, only: :show
    resources :carriers, only: %i(index show) do
      collection do
        get '/lanes/:origin/:destination', to: 'lanes#show', as: 'lanes'
      end

      resources :capacities, except: %i(new edit), shallow: true
      resources :reviews, only: %i(index show), shallow: true
    end

    resources :cities, only: %i(index show)
    resources :states, only: %i(index show)
    resources :regions, only: %i(index show)
    resources :countries, only: %i(index show)
    resources :freights, only: %i(index show)
    resources :truck_types, only: %i(index show)
    resources :shipment_types, only: %i(index show)
    resources :webhook_subscriptions, only: %i(index show create destroy) do
      member do
        post :send_test_notification
      end
    end

    namespace :analytics do
      resources :company_event_feeds, only: [] do
        resources :webhook_subscriptions, only: %i(create destroy)
      end
    end
  end
end
