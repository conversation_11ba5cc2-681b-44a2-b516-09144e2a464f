Rails.application.config.middleware.use OmniAuth::Builder do
  provider :linkedin,
           CarrierSource::Credentials.lookup(:linkedin, :client_id),
           CarrierSource::Credentials.lookup(:linkedin, :client_secret)

  provider :facebook,
           CarrierSource::Credentials.lookup(:facebook, :app_id),
           CarrierSource::Credentials.lookup(:facebook, :app_secret),
           client_options: {
             site: 'https://graph.facebook.com/v17.0',
             authorize_url: 'https://www.facebook.com/v17.0/dialog/oauth'
           },
           info_fields: 'name,email,first_name,last_name'

  provider :google_oauth2,
           CarrierSource::Credentials.lookup(:google, :client_id),
           CarrierSource::Credentials.lookup(:google, :client_secret),
           name: 'google',
           prompt: 'select_account'
end
