require 'carrier_source/credentials'

AssetSync.configure do |config|
  config.fog_provider = 'AWS'
  config.aws_access_key_id = CarrierSource::Credentials.lookup(:aws, :access_key_id)
  config.aws_secret_access_key = CarrierSource::Credentials.lookup(:aws, :secret_access_key)
  config.fog_directory = CarrierSource::Credentials.lookup(:asset_sync, :bucket)

  # Set `public` option when uploading file depending on value,
  # Setting to "default" makes asset sync skip setting the option
  # Possible values: true, false, "default" (default: true)
  config.fog_public = 'default'

  # Upload files concurrently
  config.concurrent_uploads = true

  config.run_on_precompile = false
end
