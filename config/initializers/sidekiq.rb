# :nocov:
# Disable uniqueness in testing, this has the potential to cause much head scratching...
Sidekiq::Enterprise.unique! unless Rails.env.test?

Sidekiq.configure_server do |config|
  config.reliable!
  config.logger.level = Rails.logger.level

  config.client_middleware do |chain|
    chain.add Sidekiq::Middleware::ElasticReindex
  end

  config.periodic(&Sidekiq::PeriodicConfig.new.to_proc)
end
# :nocov:

Sidekiq.configure_client do |config|
  config.logger.level = Rails.logger.level

  config.client_middleware do |chain|
    chain.add Sidekiq::Middleware::ElasticReindex
  end
end

Sidekiq.strict_args!

Sidekiq.default_job_options = { backtrace: true }

require 'sidekiq-ent/web'
require 'authorization_middleware'

Sidekiq::Web.use AuthorizationMiddleware, :admin

require 'sidekiq/job_logger'
require 'sidekiq/logging_with_arguments'

Sidekiq::Logger.include ActiveSupport::LoggerSilence
Sidekiq::JobLogger.prepend Sidekiq::LoggingWithArguments
