Rack::Attack.blocklist('block specific IPs') do |req|
  BlockedIp.exists?(ip: req.ip)
end

Rack::Attack.blocklist('block unproxied request') do |req|
  next false if ENV['BLOCK_UNPROXIED_REQUEST'].blank? || req.path == '/healthz'
  Cloudflare::BlockUnproxiedRequest.call(req)
end

Rack::Attack.blocklist('block blocked users') do |req|
  current_user = req.env[:clearance].current_user
  next false if current_user.blank?
  current_user.blocked?
end
