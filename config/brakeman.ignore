{"ignored_warnings": [{"warning_type": "Cross-Site Scripting", "warning_code": 2, "fingerprint": "2493e36443ab267cdf1a217e5da02b0598533f00a10b0cfeacd3606a5769ec29", "check_name": "CrossSiteScripting", "message": "Unescaped model attribute", "file": "app/views/driver_jobs/index.html.erb", "line": 2, "link": "https://brakemanscanner.org/docs/warning_types/cross_site_scripting", "code": "Company.friendly.find(params[:carrier_id]).name", "render_path": [{"type": "controller", "class": "DriverJobsController", "method": "index", "line": 11, "file": "app/controllers/driver_jobs_controller.rb", "rendered": {"name": "driver_jobs/index", "file": "app/views/driver_jobs/index.html.erb"}}], "location": {"type": "template", "template": "driver_jobs/index"}, "user_input": null, "confidence": "High", "cwe_id": [79], "note": "The name of a carrier is not an editable attribute"}, {"warning_type": "Cross-Site Scripting", "warning_code": 2, "fingerprint": "430ae24411618e9273d10fe25fee23e3d7b9167bef610be2ba93bfee1c1178b6", "check_name": "CrossSiteScripting", "message": "Unescaped model attribute", "file": "app/views/carriers/_layout.html.erb", "line": 2, "link": "https://brakemanscanner.org/docs/warning_types/cross_site_scripting", "code": "Company.carrier.viewable.friendly.find(params[:id]).name", "render_path": [{"type": "controller", "class": "CarriersController", "method": "show", "line": 38, "file": "app/controllers/carriers_controller.rb", "rendered": {"name": "carriers/show", "file": "app/views/carriers/show.html.erb"}}, {"type": "template", "name": "carriers/show", "line": 7, "file": "app/views/carriers/show.html.erb", "rendered": {"name": "carriers/_layout", "file": "app/views/carriers/_layout.html.erb"}}], "location": {"type": "template", "template": "carriers/_layout"}, "user_input": null, "confidence": "High", "cwe_id": [79], "note": "The name of a carrier is not an editable attribute"}, {"warning_type": "Cross-Site Scripting", "warning_code": 2, "fingerprint": "99ed0e2b2a2034dba3370cd4bbf29f507e1223e03d695696a1669ac4f1be84b4", "check_name": "CrossSiteScripting", "message": "Unescaped model attribute", "file": "app/views/carriers/related.html.erb", "line": 6, "link": "https://brakemanscanner.org/docs/warning_types/cross_site_scripting", "code": "Company.carrier.viewable.friendly.find(params[:id]).name", "render_path": [{"type": "controller", "class": "CarriersController", "method": "related", "line": 18, "file": "app/controllers/carriers_controller.rb", "rendered": {"name": "carriers/related", "file": "app/views/carriers/related.html.erb"}}], "location": {"type": "template", "template": "carriers/related"}, "user_input": null, "confidence": "High", "cwe_id": [79], "note": "The name of a carrier is not an editable attribute"}, {"warning_type": "Cross-Site Scripting", "warning_code": 2, "fingerprint": "d3e075259ccccc5f0c400f817140f4360e6b8cfcf331253ff7cd7a52b89c3036", "check_name": "CrossSiteScripting", "message": "Unescaped model attribute", "file": "app/views/brokerages/show.html.erb", "line": 2, "link": "https://brakemanscanner.org/docs/warning_types/cross_site_scripting", "code": "Company.broker.viewable.friendly.find(params[:id]).becomes(Brokerage).name", "render_path": [{"type": "controller", "class": "BrokeragesController", "method": "show", "line": 16, "file": "app/controllers/brokerages_controller.rb", "rendered": {"name": "brokerages/show", "file": "app/views/brokerages/show.html.erb"}}], "location": {"type": "template", "template": "brokerages/show"}, "user_input": null, "confidence": "High", "cwe_id": [79], "note": "The name of a brokerage is not an editable attribute"}, {"warning_type": "Command Injection", "warning_code": 14, "fingerprint": "f7d07b53c41a83a08b1372d9c8f7e13da90c3d5cc4fbbe719ce732f51b1bbd7c", "check_name": "Execute", "message": "Possible command injection", "file": "app/domain/carrier_profiles/generate_lanes_map.rb", "line": 20, "link": "https://brakemanscanner.org/docs/warning_types/command_injection/", "code": "Open3.capture2(\"./app/javascript/maps/generate '#{arguments.to_json}'\")", "render_path": null, "location": {"type": "method", "class": "CarrierProfiles::GenerateLanesMap", "method": "call"}, "user_input": "arguments.to_json", "confidence": "Medium", "cwe_id": [77], "note": ""}], "updated": "2024-12-28 10:15:52 -0700", "brakeman_version": "6.2.2"}