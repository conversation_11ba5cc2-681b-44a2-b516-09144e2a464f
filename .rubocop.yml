plugins:
  - rubocop-capybara
  - rubocop-factory_bot
  - rubocop-rails
  - rubocop-rspec

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.4
  Exclude:
    - bin/bundle
    - db/schema.rb
    - db/migrate/*.rb

Layout/EmptyLineAfterGuardClause:
  Enabled: false

Layout/LineLength:
  AllowedPatterns:
    - \A#

Layout/MultilineMethodCallIndentation:
  EnforcedStyle: indented_relative_to_receiver

Lint/MissingSuper:
  Enabled: false

Metrics/AbcSize:
  Max: 20

Metrics/BlockLength:
  Enabled: false

Metrics/MethodLength:
  Max: 15

Naming/VariableNumber:
  AllowedIdentifiers:
    - mlg_150

Rails/I18nLocaleTexts:
  Enabled: false

Rails/SkipsModelValidations:
  Enabled: false

Style/Documentation:
  Enabled: false

Style/EmptyMethod:
  Enabled: false

Style/FrozenStringLiteralComment:
  Enabled: false

Style/PercentLiteralDelimiters:
  PreferredDelimiters:
    default: ()
    '%w': '()'
    '%W': '()'
    '%i': '()'
    '%I': '()'

Style/SafeNavigation:
  Enabled: false
