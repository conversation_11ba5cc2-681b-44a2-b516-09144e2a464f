[![CircleCI](https://dl.circleci.com/status-badge/img/gh/CarrierSource-code/carrier_source/tree/main.svg?style=svg&circle-token=****************************************)](https://dl.circleci.com/status-badge/redirect/gh/CarrierSource-code/carrier_source/tree/main)
[![codecov](https://codecov.io/gh/CarrierSource-code/carrier_source/branch/main/graph/badge.svg?token=BHH9D5PVO8)](https://codecov.io/gh/CarrierSource-code/carrier_source)

# CarrierSource

## Environment Setup

1. Install [Homebrew](http://brew.sh)

   ```
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. Install system dependencies

   ```
   brew bundle
   ```

3. Hook [asdf](https://asdf-vm.com/guide/getting-started.html#_3-install-asdf) into your ZSH shell

   ```
   echo -e "\n. $(brew --prefix asdf)/libexec/asdf.sh" >> ${ZDOTDIR:-~}/.zshrc
   ```

4. Configure AWS Command Line Interface

   ```
   aws configure
   ```

   ```
   AWS Access Key ID [None]: [redacted]
   AWS Secret Access Key [None]: [redacted]
   Default region name [None]: us-east-1
   Default output format [None]: json
   ```

5. Hook [direnv](https://direnv.net/docs/hook.html) into your ZSH shell

   ```
   echo -e "\neval \"\$(direnv hook zsh)\"" >> ${ZDOTDIR:-~}/.zshrc
   ```

6. Export bundler secrets

   ```
   echo -e "\nexport BUNDLE_ENTERPRISE__CONTRIBSYS__COM=\"$(aws ssm get-parameter --name /carrier_source/config/bundle/gems.contribsys.com | jq --raw-output '.Parameter.Value')\"" >> ${ZDOTDIR:-~}/.zshrc
   ```

7. Install [Ruby](https://www.ruby-lang.org/en/)

   ```
   asdf plugin add ruby
   asdf plugin update ruby
   asdf install ruby $(awk '/ruby/ {print $2}' .tool-versions)
   ```

8. Install [nodejs](https://github.com/asdf-vm/asdf-nodejs)

   ```
   asdf plugin add nodejs https://github.com/asdf-vm/asdf-nodejs.git
   asdf install nodejs $(awk '/nodejs/ {print $2}' .tool-versions)
   ```

9. Install project dependencies

   ```
   bundle install
   yarn install
   ```

10. Configure pre-commit hooks with [lefthook](https://github.com/evilmartians/lefthook)

    ```
    lefthook install
    ```

11. Install [Docker Desktop](https://www.docker.com/products/docker-desktop/)

12. Fetch secret keys:

    ```
    ./config/credentials/fetch-key -e development
    ./config/credentials/fetch-key -e test
    ```

13. Setup [puma-dev](https://github.com/puma/puma-dev)

    ```
    sudo puma-dev -setup
    puma-dev -install
    puma-dev link -n carriersource .
    ```

## Database Setup

1. From the project directory, start the required services with docker:

   ```
   docker compose up -d
   ```

2. Restore your postgres database using a snapshot from staging:

   ```
   restore-db
   ```

3. Create elasticsearch indices:

   ```
   bundle exec rails elasticsearch:reindex
   ```

## Start Rails

1. Run `dev`
2. Visit [https://www.carriersource.test](https://www.carriersource.test)
