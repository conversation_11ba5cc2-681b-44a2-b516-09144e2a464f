module Ransackable
  include Conifer

  conifer :ransackable, singleton: true

  def self.module(model)
    Module.new do
      define_method :ransackable_attributes do |_auth_object = nil|
        key = "#{model.name}.attributes"
        Ransackable.ransackable[key] || []
      end

      define_method :ransackable_associations do |_auth_object = nil|
        key = "#{model.name}.associations"
        Ransackable.ransackable[key] || []
      end
    end
  end
end
