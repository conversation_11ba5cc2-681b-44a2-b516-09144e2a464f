module Records
  class Base
    class_attribute :model_class
    class_attribute :order_by, default: :id

    delegate :[], :slice, to: :indexed

    def all
      indexed.values.sort_by(&order_by)
    end

    def indexed
      @indexed ||= model_class.all.index_by(&:id)
    end

    # rubocop:disable Metrics/AbcSize
    def where(records = all, **opts)
      return records if records.blank? || opts.blank?

      key, value = opts.first
      raise ArgumentError, "Unknown key: #{key}" unless model_class.method_defined?(key)

      Array.wrap(value)
        .map { |v| model_class.attribute_types[key.to_s].cast(v) }
        .then { |values| records.select { |record| values.include?(record.public_send(key)) } }
        .then { |filtered| where(filtered, **opts.except(key)) }
    end
    # rubocop:enable Metrics/AbcSize

    def find_by(**)
      where(**).first
    end

    def reset
      instance_variables.each { |iv| remove_instance_variable(iv) }
    end
  end
end
