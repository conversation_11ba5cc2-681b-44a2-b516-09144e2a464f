namespace :sidekiq do
  namespace :batch do
    task :invalidate_all, %i(bid) => :environment do |_, args|
      args.with_defaults(bid: nil)
      raise ArgumentError, 'Batch ID is required' if args[:bid].nil?
      Sidekiq::Batch.new(args[:bid]).invalidate_all
    end
  end

  namespace :stats do
    task :reset, %i(stats) => :environment do |_, args|
      args.with_defaults(stats: 'failed:processed')
      Sidekiq::Stats.new.reset(*args[:stats].split(':'))
    end
  end
end
