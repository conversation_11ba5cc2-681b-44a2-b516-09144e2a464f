namespace :cities do
  task :upsert_blurbs, %i(bucket key) => %i(environment stdout_logger) do |_t, args|
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: 'manual-uploads/city-blurbs.csv')
    Aws::S3::Object.new(args[:bucket], args[:key])
      .then { |object| CSV.parse(object.get.body, headers: true) }.then do |csv|
      csv.each do |row|
        city = City.find_by(name: row['city'], state_code: row['state'], country_code: 'US')
        next if city.blank?
        CitiesMetadatum.find_or_initialize_by(city:).update(description: row['blurb'])
      end
    end
  end

  task :upsert_postal_codes, %i(country bucket key) => %i(environment stdout_logger) do |_t, args|
    defaults = {
      'US' => 'manual-uploads/uszips.csv',
      'CA' => 'manual-uploads/canada-postal-code-database-202408.csv',
      'MX' => 'manual-uploads/mexico-postal-codes.csv'
    }
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: defaults[args[:country]])
    PostalCodes::UpsertPostalCodes.call(country_code: args[:country], key: args[:key], bucket: args[:bucket])
  end

  task :create_cities_postal_codes, %i(country bucket key) => %i(environment stdout_logger) do |_t, args|
    defaults = {
      'US' => 'manual-uploads/uszips.csv',
      'CA' => 'manual-uploads/canada-postal-code-database-202408.csv',
      'MX' => 'manual-uploads/mexico-postal-codes.csv'
    }
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: defaults[args[:country]])
    PostalCodes::CreateCitiesPostalCodes.call(country_code: args[:country], key: args[:key], bucket: args[:bucket])
  end

  task :upsert_cities_postal_codes, %i(country bucket key) => %i(environment stdout_logger) do |_t, args|
    defaults = {
      'US' => 'manual-uploads/uszips.csv',
      'CA' => 'manual-uploads/canada-postal-code-database-202408.csv',
      'MX' => 'manual-uploads/mexico-postal-codes.csv'
    }
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: defaults[args[:country]])
    PostalCodes::UpsertCitiesPostalCodes.call(country_code: args[:country], key: args[:key], bucket: args[:bucket])
  end

  task :upsert_mexican_cities, %i(bucket key) => %i(environment stdout_logger) do |_t, args|
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: 'manual-uploads/mexico-postal-codes.csv')
    PostalCodes::UpsertMexicanCities.call(key: args[:key], bucket: args[:bucket])
  end

  task populate_acceptable_cities: :environment do
    Aws::S3::Object.new('carriersource-v2-files-production', 'manual-uploads/zip_code_database_small_business.csv')
      .then { |object| CSV.parse(object.get.body, headers: true) }
      .then do |csv|
      postal_codes = PostalCode.where(code: csv['zip'], country_code: 'US').index_by(&:code)
      csv.each do |row|
        code = row['zip']
        next if row['acceptable_cities'].blank?
        acceptable_cities = row['acceptable_cities'].split(',').map(&:strip)
        next if acceptable_cities.blank?
        postal_code = postal_codes[code]
        next if postal_code.blank?
        postal_code.update_columns(acceptable_cities:)
      end
    end
  end

  task :upload_mexico_postal_codes_csv, %i(bucket key) => %i(environment stdout_logger) do |_t, args|
    # The input TSV is downloaded from https://download.geonames.org/export/zip/MX.zip
    args.with_defaults(bucket: 'carriersource-v2-files-production', key: 'manual-uploads/mexico-postal-codes.tsv')
    Aws::S3::Client.new.get_object({ bucket: args[:bucket], key: args[:key] }, target: 'tmp/mexico-postal-codes.tsv')
    input_headers = %i(country_code postal_code place_name admin_name1 admin_code1 admin_name2 admin_code2 admin_name3
                       admin_code3 latitude longitude accuracy)
    output_headers = %i(postal_code city_name state_code latitude longitude)

    postal_codes = {}

    File.foreach('tmp/mexico-postal-codes.tsv') do |line|
      attrs = input_headers.zip(line.chomp.split("\t").map(&:squish)).to_h
      postal_code = attrs[:postal_code]
      state_code = Geo::Country.find(:mx).states.find_by(name: attrs[:admin_name1]).abbr
      latitude = attrs[:latitude]
      longitude = attrs[:longitude]

      if postal_codes[postal_code].blank?
        city_name = attrs[:admin_name3].presence || attrs[:admin_name2]
        postal_codes[postal_code] = { postal_code:, city_name:, state_code:, latitude:, longitude: }
      elsif attrs[:admin_name3].present?
        postal_codes[postal_code][:city_name] = attrs[:admin_name3]
      end
    end

    CSV.open('tmp/mexico-postal-codes.csv', 'w') do |csv|
      csv << output_headers
      postal_codes.each_value do |attrs|
        csv << attrs.values_at(*output_headers)
      end
    end

    Aws::S3::Object.new('carriersource-v2-files-production', 'manual-uploads/mexico-postal-codes.csv')
      .upload_file('tmp/mexico-postal-codes.csv')
  end
end
